{"name": "my-better-t-app", "private": true, "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F server dev", "db:push": "turbo -F @my-better-t-app/db db:push", "db:studio": "turbo -F @my-better-t-app/db db:studio", "db:generate": "turbo -F @my-better-t-app/db db:generate", "db:migrate": "turbo -F @my-better-t-app/db db:migrate", "db:start": "turbo -F @my-better-t-app/db db:start", "db:watch": "turbo -F @my-better-t-app/db db:watch", "db:stop": "turbo -F @my-better-t-app/db db:stop", "db:down": "turbo -F @my-better-t-app/db db:down"}, "dependencies": {"dotenv": "^17.2.3", "zod": "^4.1.12"}, "devDependencies": {"@types/node": "^22.18.13", "tsdown": "^0.15.12", "turbo": "^2.6.0"}, "packageManager": "pnpm@10.12.3"}