{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "lib": ["ESNext"], "verbatimModuleSyntax": true, "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "types": ["node"]}}