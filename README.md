# checkou-v2

## 🚀 Cakto Checkout v2

Checkout moderno e responsivo construído com Next.js 16, React 19 e TailwindCSS 4.1.

### 🎯 Características

- ✅ Next.js 16 + React 19 + Turbopack
- ✅ TypeScript 5.8+
- ✅ TailwindCSS 4.1
- ✅ i18n (Português, Inglês, Espanhol)
- ✅ 11 métodos de pagamento
- ✅ Checkout Builder configurável
- ✅ Tracking pixels (Facebook, Google, TikTok, Kwai)

### 📦 Estrutura do Projeto

```
cakto-checkoutv2/
├── apps/
│   ├── web/          # Next.js App
│   └── server/       # API Backend
├── packages/
│   └── api/          # Shared API types
└── _docs/            # Documentação
```

### 🛠️ Instalação

```bash
pnpm install
```

### 🚀 Desenvolvimento

```bash
pnpm dev:web
```

### 📖 Documentação

Consulte o arquivo `.cursorrules` na raiz do projeto para documentação completa.

### 📝 Status

**Versão:** 1.0.0 (Beta)  
**Progresso:** ~75% Completo
