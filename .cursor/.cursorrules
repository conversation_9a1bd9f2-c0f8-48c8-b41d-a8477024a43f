# Cakto Checkout v2 - Cursor AI Rules

## 🎯 STATUS ATUAL DO PROJETO

**Versão:** 1.0.0 (Beta)  
**Fase Atual:** 7 - i18n Completo e UX  
**Progresso Geral:** ~75% Completo  
**Última Atualização:** 10/11/2025

### ✅ O QUE JÁ FOI FEITO

1. **Setup Completo** ⭐⭐⭐⭐⭐
   - ✅ Monorepo com Next.js 16 + React 19 + Turbopack
   - ✅ TailwindCSS 4.1 + TypeScript 5.8+
   - ✅ 50+ componentes migrados
   - ✅ 20+ hooks customizados

2. **Sistema i18n** ⭐⭐⭐⭐⭐
   - ✅ Suporte a 3 idiomas (pt/en/es)
   - ✅ 500+ mensagens traduzidas
   - ✅ Tipagem forte TypeScript
   - ✅ Hook `useTranslation()` padronizado
   - ✅ 6 componentes principais 100% internacionalizados

3. **Checkout Funcional** ⭐⭐⭐⭐⭐
   - ✅ Sistema de pagamento completo (11 métodos)
   - ✅ Validações de formulário
   - ✅ Checkout builder (11 componentes)
   - ✅ Tracking pixels (Facebook, Google, TikTok, Kwai)

### ⏳ O QUE ESTÁ PENDENTE (Fase 7 - Continuação)

1. **Componentes de Status** 🔴 ALTA PRIORIDADE
   - ⏳ `WaitingPayment.tsx` - Remover textos hardcoded
   - ⏳ `SuccessPayment.tsx` - Remover textos hardcoded
   - ⏳ `PixPayment.tsx` - Remover textos hardcoded
   - ⏳ `BoletoPayment.tsx` - Remover textos hardcoded
   - ⏳ Outros componentes de pagamento

2. **Componentes Faltantes** 🟡 MÉDIA PRIORIDADE
   - ⏳ `Toast.tsx` - Notificações toast
   - ⏳ `ErrorBoundary.tsx` - Error boundary
   - ⏳ `Skeleton.tsx` - Loading states
   - ⏳ `EmailAutoComplete.tsx` - Autocomplete de email

3. **Validações e UX** 🟡 MÉDIA PRIORIDADE
   - ⏳ Sistema de validações com mensagens traduzidas
   - ⏳ Error handling robusto
   - ⏳ Loading states em todos os componentes

4. **Testes** 🟢 BAIXA PRIORIDADE
   - ⏳ Testes completos em 3 idiomas
   - ⏳ Validação de todos os fluxos

### 📊 Estatísticas

| Métrica | Status |
|---------|--------|
| Componentes Migrados | 50+ |
| Hooks Customizados | 20+ |
| Mensagens i18n | 500+ (3 idiomas) |
| Textos Hardcoded Removidos | 35 |
| Progresso i18n | 40% |
| Progresso Geral | ~75% |

### 📚 Documentação

**IMPORTANTE:** Toda a documentação está organizada em `_docs/`:

- **`_docs/README.md`** - Índice completo da documentação
- **`PROMPT_FASE_7_CONTINUACAO.md`** - Próximos passos (RAIZ)
- **`STATUS_FASE_7_PROGRESSO.md`** - Status atual detalhado (RAIZ)
- **`_docs/fases/`** - Documentação de todas as fases anteriores

**Antes de começar qualquer tarefa, SEMPRE:**
1. Leia `_docs/README.md` para contexto
2. Consulte `STATUS_FASE_7_PROGRESSO.md` para ver o que já foi feito
3. Leia `PROMPT_FASE_7_CONTINUACAO.md` para próximos passos

---

## 📁 Estrutura do Projeto

Este é um projeto **monorepo** com a seguinte estrutura:

```
cakto-checkoutv2/
├── apps/
│   ├── web/          # Aplicação Next.js 16 (React 19)
│   └── server/       # API Backend
├── packages/
│   └── api/          # Shared API types
└── _docs/
    └── cakto-checkout/  # Projeto original (Vite + React 18) - APENAS REFERÊNCIA
```

## 🎯 Projeto Ativo

**SEMPRE trabalhar em:** `apps/web/`

**NUNCA modificar:** `_docs/cakto-checkout/` (apenas referência)

---

## 🏗️ Arquitetura Next.js 16 + React 19

### Stack Tecnológico
- **Framework:** Next.js 16.0.0 (App Router + Turbopack)
- **React:** 19.2.0 (com React Compiler)
- **TypeScript:** 5.8+
- **Styling:** TailwindCSS 4.1 + tailwind-variants
- **Forms:** react-hook-form 7.46
- **State:** Context API + React Query
- **UI:** shadcn/ui (customizado) + @heroicons/react

### Padrões de Importação

```typescript
// ✅ SEMPRE usar path aliases
import { useCheckout } from "@/contexts";
import Button from "@/components/ui/button-form";
import { formatPrice } from "@/lib/utils/format";

// ❌ NUNCA usar imports relativos longos
import { useCheckout } from "../../contexts/checkout-context";
```

### Path Aliases (@/)

```
@/components    → apps/web/src/components
@/lib           → apps/web/src/lib
@/hooks         → apps/web/src/hooks
@/contexts      → apps/web/src/contexts
@/types         → apps/web/src/types
@/constants     → apps/web/src/constants
```

---

## 🎨 Componentes de UI

### Componentes Base (apps/web/src/components/ui/)

**Usar SEMPRE estes componentes ao invés de criar novos:**

- `button-form.tsx` - Botões (com loading, cores customizáveis)
- `card-form.tsx` - Cards (com border-wave-effect para PIX)
- `text-field.tsx` - Inputs de texto (com máscara customizada React 19)
- `select.tsx` - Selects (integrado com react-hook-form)
- `checkbox-form.tsx` - Checkboxes (integrado com react-hook-form)
- `alert.tsx` - Alertas
- `divider.tsx` - Divisores

**Propriedades importantes:**

```typescript
// Button
<Button
  sizes="sm"  // ou "md", "lg"
  loading={true}
  style={{ backgroundColor: settings.payButton.color }}
>

// Card  
<Card
  style={{ backgroundColor: settings.form?.background.color }}
  borderEffect="border-wave-effect"  // efeito especial para PIX
>

// TextField
<TextField
  name="cardNumber"
  mask="9999 9999 9999 9999"  // máscara customizada
  cpfCnpj  // máscara automática CPF/CNPJ
  normalize  // normaliza texto (remove acentos)
/>
```

---

## 🔄 Contexts e State Management

### CheckoutContext (PRINCIPAL)

**Hook:** `useCheckout()`

**NUNCA** usar `useContext(CheckoutContext)` diretamente!

```typescript
// ✅ CORRETO
import { useCheckout } from "@/contexts";
const { offer, firstPayment, paying, checkPayment } = useCheckout();

// ❌ ERRADO
import { CheckoutContext } from "@/contexts/checkout-context";
const context = useContext(CheckoutContext);
```

**Dados disponíveis:**

```typescript
{
  offer: ProductData | undefined,
  firstPayment: Payment | null,
  paying: boolean,
  checkingPayment: boolean,
  checkPayment: () => Promise<unknown>,
  paymentTabs: PaymentTab[],
  paymentMethod: PaymentMethodOption | null,
  setPaymentMethod: (payment) => void,
  couponData: CouponData | undefined,
  validateCoupon: (coupon: string) => Promise<unknown>,
  calcInstallments: unknown,
  setCalcInstallments: (installments) => void,
  fingerprint: string | undefined,
  hasAnySubscription: boolean,
  canBePaidInInstallments: boolean,
  // ... +10 outros campos
}
```

### Outros Contexts

- `useCheckoutMode()` - Modo preview/produção
- `useNotificationContext()` - Sistema de notificações
- `useMessage()` - i18n (intl)

---

## 🔐 Variáveis de Ambiente

### NUNCA usar `import.meta.env`

```typescript
// ❌ ERRADO (Vite)
const apiUrl = import.meta.env.VITE_API_URL;

// ✅ CORRETO (Next.js)
import { getCaktoUrl, getPixCheckInterval } from '@/lib/env';
const apiUrl = getCaktoUrl();
const interval = getPixCheckInterval();
```

### Helpers Disponíveis (lib/env.ts)

**Client-side:**
- `getBranding()` - "cakto" ou "nommi"
- `getCaktoUrl()` - URL da plataforma
- `getPixCheckInterval()` - Intervalo de polling PIX (5000ms)
- `getPixCheckDuration()` - Duração de polling PIX (300000ms)
- `getPostHogKey()` - Chave do PostHog
- `get3dsProvider()` - Provider 3DS (cielo/pagarme)
- `getGoogleMapsApiKey()` - Chave Google Maps
- ... +10 outros

**Server-side:**
- `getCheckoutApiUrl()`
- `getApiBaseUrl()`
- `getGeoipResolverUrl()`

---

## 🎨 Hooks Customizados

### useSettings()

Retorna todas as configurações visuais do checkout:

```typescript
const settings = useSettings();

settings.form.background.color      // Cor de fundo do formulário
settings.payButton.color            // Cor do botão de pagamento
settings.payButton.text.color       // Cor do texto do botão
settings.payButton.text.text        // Texto do botão
settings.text.color.primary         // Cor do texto primário
settings.text.color.secondary       // Cor do texto secundário
settings.backgroundColor            // Background da página
settings.backgroundImage            // Imagem de fundo
```

### usePrice({ form })

Calcula preços com descontos, cupons e taxas:

```typescript
const prices = usePrice({ form });

prices.mainOfferPrice               // Preço principal
prices.mainOfferPriceWithDiscount   // Com desconto
prices.totalPrice                   // Total sem desconto
prices.totalPriceWithDiscount       // Total com desconto
prices.totalPriceWithFee            // Total com taxa
prices.serviceFee                   // Taxa de serviço
```

### useDevice()

Detecta dispositivo do usuário:

```typescript
const { device } = useDevice();
// device: "desktop" | "mobile"
```

---

## 🔒 Regras de Segurança e SSR

### SEMPRE verificar window/navigator

```typescript
// ✅ CORRETO
if (typeof window === 'undefined') return null;
window.location.href = url;

// ✅ CORRETO
if (typeof window === 'undefined') return;
navigator.clipboard.writeText(text);

// ❌ ERRADO
window.location.href = url; // Quebra no SSR
```

### Client Components

**SEMPRE adicionar** `"use client"` no topo quando usar:
- `useState`, `useEffect`, `useCallback`, `useMemo`
- `useFormContext`, `useWatch` (react-hook-form)
- `window`, `navigator`, `document`
- Event handlers (`onClick`, `onChange`, etc)

```typescript
"use client";

import { useState } from "react";
// ... resto do código
```

---

## 💳 Sistema de Pagamento

### Métodos Suportados

```typescript
type PaymentMethod =
  | "credit_card"
  | "pix"
  | "pix_auto"
  | "boleto"
  | "picpay"
  | "applepay"
  | "googlepay"
  | "openfinance_nubank";
```

### Formulários de Pagamento

Cada método tem seu formulário em `components/payments/`:
- `CreditCardForm.tsx` - Cartão de crédito
- `PixForm.tsx` - PIX
- `PixAutoForm.tsx` - PIX Automático
- `BoletoForm.tsx` - Boleto
- `PicPayForm.tsx` - PicPay
- `ApplePayForm.tsx` - Apple Pay
- `GooglePayForm.tsx` - Google Pay
- `NubankPayForm.tsx` - Nubank (Open Finance)

### Componentes de Pagamento (QR/Boleto)

Usados em `WaitingPayment.tsx`:
- `PixPayment.tsx` - Exibe QR Code PIX
- `PixAutoPayment.tsx` - Exibe QR Code PIX Auto
- `BoletoPayment.tsx` - Exibe código de barras
- `PicPayPayment.tsx` - Exibe QR Code PicPay

### Estados de Pagamento

```typescript
type PaymentStatus =
  | "waiting_payment"  // → Renderiza WaitingPayment
  | "paid"             // → Renderiza SuccessPayment
  | "redo_payment"     // → Volta para formulário
  | "failed"           // → Mostra erro
  | "pending";
```

---

## 🎨 Checkout Builder

### Componentes Disponíveis

O checkout pode ser configurado via builder com estes componentes:

**Implementados:**
- ✅ `TEXT` - Texto formatado
- ✅ `IMAGE` - Imagem
- ✅ `HEADER` - Cabeçalho
- ✅ `VIDEO` - Player de vídeo
- ✅ `FACEBOOK` - Facebook widget
- ✅ `MAP` - Google Maps
- ✅ `CHECKOUT` - Formulário de checkout
- ✅ `ADVANTAGE` - Vantagens com ícones
- ✅ `SEAL` - Selos de garantia
- ✅ `LIST` - Lista com ícones
- ✅ `TESTIMONIAL` - Depoimentos
- ✅ `COUNTDOWN` - Contador regressivo

**Placeholders (TODO):**
- ⏳ `CHAT` - Widget de chat
- ⏳ `EXIT_POPUP` - Popup de saída
- ⏳ `NOTIFICATION` - Notificações toast

### Renderização

```typescript
// CheckoutForm detecta automaticamente se há config do builder
if (offer?.checkout?.config) {
  // Renderiza com builder
  <CheckoutConfigRenderer config={offer.checkout.config} device={device} />
} else {
  // Renderiza formulário simples
  <CheckoutComponent />
}
```

---

## 🐛 Problemas Conhecidos

### 1. Facebook Pixel + SSR

**Erro:** "Switched to client rendering because the server rendering errored: window is not defined"

**Impacto:** ⚠️ Warning recuperável (não afeta funcionamento)

**Solução futura:** Lazy load do Facebook Pixel apenas no cliente

### 2. Peer Dependencies

```
@tanstack/react-query-devtools 5.90.2
└── ✕ unmet peer @tanstack/react-query@^5.90.2: found 5.85.5
```

**Impacto:** ⚠️ Warning apenas (funciona normalmente)

**Solução futura:** Atualizar `@tanstack/react-query` para 5.90.2+

---

## 📝 Convenções de Código

### Nomenclatura

```typescript
// Componentes: PascalCase
export default function PaymentForm() {}
export function CheckoutForm() {}

// Hooks: camelCase com 'use' prefix
export function useCheckout() {}
export default function useSettings() {}

// Utils/Helpers: camelCase
export function formatPrice() {}
export const applyMask = () => {}

// Types: PascalCase
export type ProductData = {}
export interface PaymentProps {}

// Constants: SCREAMING_SNAKE_CASE
export const PAYMENT_METHODS = []
export const DEFAULT_LOCALE = "pt"
```

### Exportações

```typescript
// Componentes: default export
export default function MeuComponente() {}

// Hooks: named ou default
export function useCheckout() {}  // Named
export default function useSettings() {}  // Default

// Types: always named
export type MeuTipo = {}
export interface MinhaInterface {}

// Utils: named
export function minhaFuncao() {}
export const minhaConstante = {}
```

---

## 🚫 O QUE NUNCA FAZER

### ❌ NUNCA usar estas bibliotecas

```typescript
// Incompatíveis com React 19
import InputMask from "react-input-mask";           // ❌ Usa findDOMNode
import { Tab } from "@headlessui/react";            // ❌ v1.7 incompatível

// Vite-specific
import.meta.env.VITE_API_URL                        // ❌ Não existe no Next.js
```

### ❌ NUNCA fazer

```typescript
// 1. Usar useContext diretamente
const { offer } = useContext(CheckoutContext);      // ❌

// 2. Hooks dentro de providers que os usam
export function CheckoutProvider() {
  const { offer } = useCheckout();                  // ❌ Loop circular!
}

// 3. Esquecer "use client"
import { useState } from "react";
export default function Comp() {
  const [x, setX] = useState();                     // ❌ Precisa "use client"
}

// 4. Acessar window sem verificação
window.location.href = url;                         // ❌ Quebra SSR

// 5. Import paths errados
import Button from "../../../components/ui/button"; // ❌ Usar @/
```

---

## ✅ O QUE SEMPRE FAZER

### ✅ SEMPRE seguir estes padrões

```typescript
// 1. "use client" quando necessário
"use client";

import { useState } from "react";
export default function Comp() {
  const [x, setX] = useState();                     // ✅
}

// 2. useCheckout() hook
import { useCheckout } from "@/contexts";
const { offer, firstPayment } = useCheckout();     // ✅

// 3. Variáveis de ambiente
import { getCaktoUrl, getPixCheckInterval } from '@/lib/env';
const url = getCaktoUrl();                          // ✅

// 4. SSR checks
if (typeof window === 'undefined') return null;
window.location.href = url;                         // ✅

// 5. Path aliases
import Button from '@/components/ui/button-form';   // ✅
```

---

## 📋 Formulários (react-hook-form)

### Setup Padrão

```typescript
"use client";

import { FormProvider, useForm } from "react-hook-form";
import TextField from "@/components/ui/text-field";

export function MeuFormulario() {
  const form = useForm({
    defaultValues: {
      name: "",
      email: "",
      cpf: "",
    },
  });

  const handleSubmit = form.handleSubmit((data) => {
    console.log(data);
  });

  return (
    <FormProvider {...form}>
      <form onSubmit={handleSubmit}>
        <TextField name="name" label="Nome" />
        <TextField name="email" label="Email" type="email" />
        <TextField name="cpf" label="CPF/CNPJ" cpfCnpj />
        <button type="submit">Enviar</button>
      </form>
    </FormProvider>
  );
}
```

### Dentro de Form Context

```typescript
import { useFormContext, useWatch } from "react-hook-form";

export function MeuCampo() {
  const { control, setValue, getValues } = useFormContext();
  const cardNumber = useWatch({ control, name: "cardNumber" });
  
  // trabalhar com form
}
```

---

## 💡 Máscaras de Input (React 19 Compatible)

### Usar TextField com máscara

```typescript
// CPF/CNPJ automático
<TextField name="cpf" cpfCnpj />

// Máscara customizada
<TextField
  name="cardNumber"
  mask="9999 9999 9999 9999"
  maskChar=" "
/>

<TextField
  name="cardExpiration"
  mask="99/99"
  placeholder="MM/AA"
/>

<TextField
  name="phone"
  mask="(99) 99999-9999"
/>
```

**Padrão de máscara:**
- `9` = dígito numérico
- Qualquer outro char = literal (ex: `/`, `-`, `(`, `)`)

---

## 🎨 Estilização com Settings

### useSettings Hook

```typescript
const settings = useSettings();

// Aplicar cores do tema
<div style={{ 
  backgroundColor: settings.form?.background.color,
  color: settings.text.color.primary 
}}>

<Button style={{
  backgroundColor: settings.payButton.color,
  color: settings.payButton.text.color
}}>
  {settings.payButton.text.text}
</Button>
```

### Cores Padrão

```typescript
// Nommi
backgroundColor: "#2886B9"
hoverColor: "#1f6e94"

// Cakto
backgroundColor: "#0F7864"
hoverColor: "#0b6856"
```

---

## 🔌 APIs e Serviços

### APIs Client-Side (apps/web/src/lib/api/checkout-client.ts)

```typescript
import {
  startPayment,
  getPaymentStatus,
  validateCoupon,
  generateFingerprint,
} from "@/lib/api/checkout-client";

// Usar diretamente no CheckoutProvider
const pay = async (payload: PaymentPayload) => {
  const result = await startPayment(checkoutId, payload);
  // ...
};
```

### APIs Server-Side (apps/web/src/lib/api/checkout.ts)

```typescript
import { getCheckoutData } from "@/lib/api/checkout";

// Usar APENAS em Server Components
export default async function CheckoutPage({ params }) {
  const { id } = await params;
  const checkoutData = await getCheckoutData(id);
  // ...
}
```

---

## 🎯 Fluxo de Checkout

### 1. Carregamento Inicial

```
app/[locale]/[id]/page.tsx (Server Component)
  ↓ getCheckoutData(id) - SSR
  ↓ 
CheckoutClient (Client Component)
  ↓ CheckoutProvider(initialData, checkoutId)
  ↓
CheckoutForm (detecta status)
```

### 2. Estados do Checkout

```
[Normal] → CheckoutConfigRenderer + CheckoutComponent
  ↓ (usuário paga)
[waiting_payment] → WaitingPayment + polling
  ↓ (pagamento confirmado)
[paid] → SuccessPayment + tracking
```

### 3. Renderização Condicional

```typescript
export function CheckoutForm() {
  const { firstPayment, offer } = useCheckout();

  // Status: waiting_payment
  if (firstPayment?.status === "waiting_payment") {
    return <WaitingPayment />;
  }

  // Status: paid
  if (firstPayment?.status === "paid") {
    return <SuccessPayment />;
  }

  // Normal: renderizar formulário
  if (offer?.checkout?.config) {
    return <CheckoutConfigRenderer .../>;  // Com builder
  }
  
  return <CheckoutComponent />;  // Simples
}
```

---

## 🎨 Componentes de Pagamento

### Payment Methods (Tabs)

```typescript
// Tabs são gerados automaticamente pelo CheckoutProvider
const { paymentTabs } = useCheckout();

// Estrutura:
type PaymentTab = {
  id: PaymentMethod,
  label: string,
  Icon: React.ComponentType,
  Component: React.ComponentType,
};

// Mapeamento de ícones
const mapIcons = {
  credit_card: CreditCardIcon,
  pix: PixIcon,
  pix_auto: PixAutoIcon,
  boleto: BarcodeIcon,
  picpay: PicPayIcon,
  applepay: ApplePayIcon,
  googlepay: GooglePayIcon,
  openfinance_nubank: NubankIcon,
};

// Mapeamento de formulários
const mapForms = {
  credit_card: CreditCardForm,
  pix: PixForm,
  pix_auto: PixAutoForm,
  boleto: BoletoForm,
  picpay: PicPayForm,
  applepay: ApplePayForm,
  googlepay: GooglePayForm,
  openfinance_nubank: NubankPayForm,
};
```

### Exemplo de Uso

```typescript
<PaymentMethods />
// Renderiza:
// - Tabs (PIX, Boleto, Cartão)
// - Formulário ativo baseado no tab selecionado
```

---

## 🔍 Tracking e Analytics

### PostHog

```typescript
import posthog from 'posthog-js';

posthog.capture('event_name', {
  product_id: offer.product.short_id,
  payment_method: firstPayment.paymentMethod,
});
```

### Analytics Helper

```typescript
import { trackEvent, trackPageView } from '@/lib/utils/analytics';

trackPageView('checkout_success');
trackEvent('purchase_success', { ... });
```

### Pixel Hooks

```typescript
// No CheckoutProvider (já configurado)
const googleAds = useGoogleAds(offer);
const facebookPixels = useFacebookPixels(offer);
const tikTokPixels = useTikTokPixels(offer);
const kwaiPixels = useKwaiPixels(offer);

// Eventos disponíveis
googleAds.onCheckoutVisit();
facebookPixels.onCheckoutVisit();
tikTokPixels.onCheckoutVisit();
kwaiPixels.onCheckoutVisit();
```

---

## 🧪 Testes e Debugging

### Testar Checkout

```bash
# Iniciar servidor
pnpm dev:web

# Acessar checkout com ID de teste
http://localhost:3001/pt/7rohg3i
http://localhost:3001/en/7rohg3i
http://localhost:3001/es/7rohg3i
```

### Ver Logs

```bash
# Console do navegador
F12 → Console

# Terminal do servidor
# Ver output do pnpm dev:web

# React Query Devtools
# Canto inferior esquerdo da página
```

### Verificar Erros de Build

```bash
pnpm --filter web run build
```

---

## 📦 Instalação de Dependências

### Adicionar Nova Dependência

```bash
# No diretório raiz
cd /Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2

# Adicionar ao apps/web/package.json
# Depois instalar
pnpm install

# Reiniciar servidor
pkill -f "next dev"
pnpm dev:web
```

### Dependências Críticas

**Obrigatórias para pagamento:**
- `react-qrcode-logo` - QR Codes PIX/PicPay
- `react-confetti` - Animação de sucesso
- `credit-card-type` - Detecção de bandeira
- `cpf-cnpj-validator` - Validação de documentos
- `moment` - Datas (boleto)
- `posthog-js` - Analytics
- `react-hook-form` - Formulários

---

## 🎓 Boas Práticas

### 1. Componentização

```typescript
// ✅ Componentes pequenos e reutilizáveis
export function CampoEmail() {
  return <TextField name="email" type="email" />;
}

// ✅ Composição
export function FormularioContato() {
  return (
    <>
      <CampoNome />
      <CampoEmail />
      <CampoCPF />
    </>
  );
}
```

### 2. Hooks Customizados

```typescript
// ✅ Extrair lógica complexa
export function usePaymentLogic() {
  const { offer, paying } = useCheckout();
  const form = useFormContext();
  
  const handlePay = useCallback(() => {
    // lógica de pagamento
  }, [offer, form]);
  
  return { handlePay, isPaying: paying };
}
```

### 3. Type Safety

```typescript
// ✅ Sempre tipar props
interface MeuComponenteProps {
  qrCode: string;
  onCheck: () => void;
}

export function MeuComponente({ qrCode, onCheck }: MeuComponenteProps) {}

// ✅ Usar types do projeto
import type { Payment, ProductData, PaymentMethod } from "@/types";
```

---

## 🔄 Migração de Código Legacy

### Se encontrar código do projeto antigo para migrar:

**1. Adicionar "use client"**
```typescript
"use client";  // ← Adicionar no topo
```

**2. Atualizar imports**
```typescript
// Trocar:
'@/components/common/*'  → '@/components/ui/*'
'@/utils/*'              → '@/lib/utils/*'
'@/services/*'           → '@/lib/api/*'
```

**3. Substituir context**
```typescript
// Trocar:
useContext(CheckoutContext) → useCheckout()
```

**4. Substituir env vars**
```typescript
// Trocar:
import.meta.env.VITE_API_URL → getCaktoUrl()
```

**5. Adicionar SSR checks**
```typescript
// Se usar window/navigator/document:
if (typeof window === 'undefined') return null;
```

---

## 📚 Recursos Úteis

### Documentação
- [Status da Migração - Fase 5](./STATUS_MIGRACAO_FASE_5.md)
- [Prompt Fase 5 - Componentes de Pagamento](./PROMPT_FASE_5_COMPONENTES_PAGAMENTO.md)
- [Prompt Fase 5 - Continuação](./PROMPT_FASE_5_CONTINUACAO.md)
- [Prompt Fase 4 - Hooks e Serviços](./PROMPT_FASE_4_HOOKS_SERVICOS.md)

### Arquivos de Referência
- `apps/web/src/contexts/checkout-context.tsx` - Context principal
- `apps/web/src/components/payments/` - Todos os componentes de pagamento
- `apps/web/src/lib/env.ts` - Helpers de environment
- `apps/web/src/types/index.ts` - Types principais

### Projeto Original (Referência)
- `_docs/cakto-checkout/src/` - Código fonte original

---

## 🎯 Próximos Passos

### Fase 6: Componentes Restantes
- [ ] AddressForm (formulário de endereço)
- [ ] BumpItem (order bumps)
- [ ] Validação completa de formulários
- [ ] 3DS Integration (Cielo/Pagarme)
- [ ] Antifraude (Nethone)

### Fase 7: Testes e Otimização
- [ ] Testes e2e com Playwright
- [ ] Otimização de bundle
- [ ] Performance improvements
- [ ] Error boundary implementation

---

## ⚡ Performance Tips

### Code Splitting

```typescript
// Lazy load componentes pesados
const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <Loading />,
  ssr: false,  // Se não precisar SSR
});
```

### Memoização

```typescript
// Memoizar cálculos pesados
const priceCalculation = useMemo(() => {
  return complexCalculation(offer, bumps, coupon);
}, [offer, bumps, coupon]);

// Callbacks estáveis
const handleClick = useCallback(() => {
  doSomething();
}, []);
```

---

## 🎉 Resumo de Conquistas - Fase 5

- ✅ **30+ componentes** migrados
- ✅ **20 dependências** instaladas
- ✅ **4 componentes de checkout** criados
- ✅ **11 componentes de builder** implementados
- ✅ **Compatibilidade React 19** garantida
- ✅ **SSR/SSG** funcionando
- ✅ **Pixel tracking** configurado
- ✅ **Forms completos** com validação
- ✅ **Checkout builder** renderizando
- ✅ **Sistema de pagamento** funcional

**Status:** 🎯 Pronto para desenvolvimento avançado!

---

**Última atualização:** 10/11/2025 01:30 AM
**Versão:** 1.0.0
**Mantenedor:** Equipe Cakto

