# Cursor Rules - Cakto Checkout V2

## 📋 Contexto do Projeto

Este é o projeto **Cakto Checkout V2**, uma migração do checkout original (`cakto-checkout`) de **Vite + React** para **Next.js 16** com **Server-Side Rendering (SSR)**, **multi-idioma** e melhor compatibilidade com navegadores de redes sociais.

### Status da Migração
- ✅ Sistema de i18n implementado (pt, es, en)
- ✅ Detecção de idioma via IP implementada
- ✅ Rotas SSR com `[locale]/[id]` configuradas
- ✅ API client adaptado para Next.js
- ✅ Variáveis de ambiente migradas para padrão Next.js
- 🔄 **EM ANDAMENTO**: Migração de componentes do checkout original

### Referências Importantes
- **Projeto Original**: `/Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkout`
- **Gui<PERSON> de <PERSON>ção**: `MIGRACAO_COMPONENTES.md`
- **Documentação de Envs**: `apps/web/ENV.md`

## 🏗️ Estrutura do Projeto

### Monorepo com Turborepo
```
cakto-checkoutv2/
├── apps/
│   ├── web/              # Next.js 16 App (checkout frontend)
│   └── server/           # Elysia + oRPC backend
├── packages/
│   ├── api/              # Shared API types
│   └── db/               # Database package
└── turbo.json            # Turborepo config
```

### Estrutura do App Web (Next.js)
```
apps/web/src/
├── app/                  # Next.js App Router
│   ├── [locale]/         # Rotas internacionalizadas
│   │   ├── [id]/         # Checkout dinâmico: /pt/abc123
│   │   │   ├── page.tsx  # Server Component
│   │   │   ├── loading.tsx
│   │   │   └── error.tsx
│   │   ├── preview/      # Preview mode
│   │   └── layout.tsx    # Layout com i18n
│   └── layout.tsx        # Root layout
├── components/
│   ├── checkout/         # Componentes do checkout
│   ├── payments/         # Formulários de pagamento (A MIGRAR)
│   ├── builder/          # Componentes do builder (A MIGRAR)
│   ├── common/           # Componentes comuns (A MIGRAR)
│   └── pixels/            # Tracking pixels (A MIGRAR)
├── contexts/             # Context providers
│   └── intl-context.tsx  # Provider de i18n
├── hooks/                # Custom hooks (A MIGRAR)
├── lib/
│   ├── api/              # Clientes de API (server-side)
│   │   ├── checkout.ts
│   │   └── config.ts
│   ├── i18n/             # Sistema de internacionalização
│   │   ├── config.ts
│   │   ├── locale.ts
│   │   ├── server.ts
│   │   └── messages/      # pt.json, es.json, en.json
│   ├── env.ts            # Helpers para variáveis de ambiente
│   └── utils.ts          # Utilitários
├── types/                # TypeScript types (A MIGRAR)
└── utils/                # Utilitários (A MIGRAR)
```

## 🎯 Padrões e Convenções

### 1. Server vs Client Components

**Server Components (padrão)**:
- Usar para páginas (`app/**/page.tsx`)
- Usar para layouts (`app/**/layout.tsx`)
- Usar para buscar dados iniciais
- **NÃO** usar hooks do React (`useState`, `useEffect`, etc.)
- **NÃO** usar event handlers (`onClick`, etc.)

**Client Components**:
- Adicionar `"use client"` como primeira linha
- Usar para interatividade (formulários, botões, etc.)
- Usar para hooks do React
- Usar para tracking pixels
- Usar para componentes do builder

### 2. Variáveis de Ambiente

**NUNCA** usar `import.meta.env` ou `process.env` diretamente.

**SEMPRE** usar helpers de `lib/env.ts`:
```typescript
// ❌ ERRADO
const apiUrl = process.env.CHECKOUT_API_URL;
const posthogKey = process.env.NEXT_PUBLIC_POSTHOG_KEY;

// ✅ CORRETO
import { getCheckoutApiUrl, getPostHogKey } from '@/lib/env';
const apiUrl = getCheckoutApiUrl();
const posthogKey = getPostHogKey();
```

**Padrão Next.js**:
- Variáveis **server-side only**: sem prefixo (`CHECKOUT_API_URL`)
- Variáveis **client-side accessible**: com `NEXT_PUBLIC_` (`NEXT_PUBLIC_POSTHOG_KEY`)

### 3. Imports e Paths

**SEMPRE** usar path aliases:
```typescript
// ✅ CORRETO
import { getCheckoutData } from '@/lib/api/checkout';
import { useMessage } from '@/contexts/intl-context';
import { Button } from '@/components/ui/button';

// ❌ ERRADO
import { getCheckoutData } from '../../../lib/api/checkout';
```

**Paths configurados**:
- `@/` → `apps/web/src/`
- `@/components/` → `apps/web/src/components/`
- `@/lib/` → `apps/web/src/lib/`
- `@/contexts/` → `apps/web/src/contexts/`
- `@/hooks/` → `apps/web/src/hooks/`
- `@/types/` → `apps/web/src/types/`

### 4. Internacionalização (i18n)

**SEMPRE** usar o sistema de i18n:
```typescript
// ✅ CORRETO
import { useMessage } from '@/contexts/intl-context';

function MyComponent() {
  const title = useMessage('checkout.title');
  return <h1>{title}</h1>;
}
```

**Mensagens**:
- Adicionar em `lib/i18n/messages/pt.json`, `es.json`, `en.json`
- Usar chaves aninhadas: `checkout.title`, `payment.methods.creditCard`

### 5. API Calls

**Server-side** (em Server Components):
```typescript
import { getCheckoutData } from '@/lib/api/checkout';

export default async function CheckoutPage({ params }) {
  const { id } = await params;
  const data = await getCheckoutData(id);
  return <CheckoutClient initialData={data} />;
}
```

**Client-side** (em Client Components):
- Usar Server Actions quando possível
- Usar `fetch` com `credentials: "include"` quando necessário
- **NÃO** usar Axios (removido do projeto)

### 6. TypeScript

**SEMPRE** usar tipos explícitos:
```typescript
// ✅ CORRETO
type CheckoutData = {
  id: string;
  name: string;
  price: number;
};

export async function getCheckoutData(id: string): Promise<CheckoutData> {
  // ...
}
```

**Next.js 15+**:
- `params` e `searchParams` são **Promises**:
```typescript
// ✅ CORRETO
export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ affiliate?: string }>;
}) {
  const { id } = await params;
  const { affiliate } = await searchParams;
  // ...
}
```

## 🔄 Migração de Componentes

### Princípios da Migração

1. **REUTILIZAR** componentes do builder sem reescrever
2. **ADAPTAR** apenas o necessário (imports, "use client", env vars)
3. **MANTER** toda lógica de negócio e validação
4. **NÃO REESCREVER** componentes existentes

### Ordem de Migração

1. ✅ **Tipos TypeScript** (base para tudo)
2. ✅ **Utilitários** (usados por muitos componentes)
3. 🔄 **Componentes do Builder** (copiar direto, adicionar "use client")
4. ⏳ **Contextos** (adaptar para SSR)
5. ⏳ **Hooks Customizados** (manter lógica, atualizar imports)
6. ⏳ **Componentes de Pagamento** (migrar mantendo lógica)
7. ⏳ **Componentes do Checkout** (adaptar para Server Actions)
8. ⏳ **Tracking Pixels** (migrar mantendo funcionalidade)

### Checklist de Migração

Para cada componente migrado:
- [ ] Adicionado `"use client"` se necessário
- [ ] Imports atualizados para estrutura Next.js
- [ ] Variáveis de ambiente adaptadas para `lib/env.ts`
- [ ] Removido código específico do Vite (`import.meta.env`)
- [ ] Removido uso de React Router (se houver)
- [ ] Adaptado uso de APIs para `lib/api/`
- [ ] Testado funcionamento no navegador

## 🚫 O Que NÃO Fazer

1. **NÃO** usar `import.meta.env` - usar `lib/env.ts`
2. **NÃO** usar `axios` - usar `fetch` ou helpers de `lib/api/`
3. **NÃO** usar `react-router-dom` - usar Next.js App Router
4. **NÃO** usar `useNavigate`, `useLocation` - usar Next.js navigation
5. **NÃO** acessar `process.env` diretamente - usar helpers
6. **NÃO** criar Server Components com hooks do React
7. **NÃO** criar Client Components sem `"use client"`
8. **NÃO** reescrever componentes do builder - apenas adaptar

## ✅ O Que Fazer

1. **SEMPRE** usar path aliases (`@/`)
2. **SEMPRE** usar helpers de `lib/env.ts` para env vars
3. **SEMPRE** usar `useMessage` para textos traduzíveis
4. **SEMPRE** adicionar `"use client"` em componentes interativos
5. **SEMPRE** usar Server Components para dados iniciais
6. **SEMPRE** testar após migrar cada componente
7. **SEMPRE** seguir a ordem de migração recomendada
8. **SEMPRE** manter compatibilidade com APIs existentes

## 📚 Arquivos de Referência

### Documentação
- `MIGRACAO_COMPONENTES.md` - Guia completo de migração
- `apps/web/ENV.md` - Documentação de variáveis de ambiente
- `README.md` - Documentação geral do projeto

### Código de Referência
- `apps/web/src/lib/api/checkout.ts` - Exemplo de API client
- `apps/web/src/lib/env.ts` - Exemplo de helpers de env
- `apps/web/src/app/[locale]/[id]/page.tsx` - Exemplo de Server Component
- `apps/web/src/components/checkout/checkout-client.tsx` - Exemplo de Client Component

### Projeto Original
- `/Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkout` - Projeto Vite original

## 🔍 Buscar Antes de Criar

Antes de criar novos componentes ou funcionalidades:

1. **Verificar** se já existe no projeto original
2. **Verificar** se já foi migrado
3. **Consultar** `MIGRACAO_COMPONENTES.md` para instruções
4. **Seguir** padrões estabelecidos nos arquivos existentes

## 🎨 Estilização

- **Tailwind CSS 4** - Usar classes Tailwind
- **Componentes UI** - Usar componentes de `components/ui/` (shadcn/ui)
- **Responsive** - Mobile-first approach
- **Dark Mode** - Suportado via `next-themes`

## 🌍 Multi-idioma

### Idiomas Suportados
- **pt** - Português (Brasil)
- **es** - Espanhol (América Latina)
- **en** - Inglês (fallback)

### Detecção de Idioma
1. Verifica cookie `NEXT_LOCALE`
2. Detecta país via IP (usando `GEOIP_RESOLVER_URL`)
3. Mapeia país para idioma:
   - BR → pt
   - AR, CL, CO, MX, PE, EC, BO, PY, UY, VE, etc. → es
   - Outros → en
4. Fallback para `Accept-Language` header
5. Fallback para pt (default)

### Rotas
- `/pt/7rohg3i` - Checkout em português
- `/es/7rohg3i` - Checkout em espanhol
- `/en/7rohg3i` - Checkout em inglês
- `/pt/preview?id=7rohg3i` - Preview mode

## 🔐 Variáveis de Ambiente

### Server-side Only
- `CHECKOUT_API_URL` - URL da API de checkout
- `API_BASE_URL` - URL base da API
- `GEOIP_RESOLVER_URL` - URL do serviço de geolocalização
- `GEOIP_RESOLVER_TOKEN` - Token do serviço de geolocalização

### Client-side Accessible (NEXT_PUBLIC_)
- `NEXT_PUBLIC_POSTHOG_KEY` - Chave do PostHog
- `NEXT_PUBLIC_POSTHOG_HOST` - Host do PostHog
- `NEXT_PUBLIC_BRANDING_IMAGES_URL` - URL das imagens de branding
- `NEXT_PUBLIC_3DS_PROVIDER` - Provedor 3DS (cielo/pagarme)
- E outras variáveis que precisam ser acessíveis no cliente

Ver `apps/web/ENV.md` para lista completa.

## 🧪 Testes

### Testar Localmente
```bash
cd apps/web
pnpm dev
```

### Testar Checkout
- Acessar: `http://localhost:3001/pt/7rohg3i`
- Verificar SSR funcionando
- Verificar i18n funcionando
- Verificar API respondendo

## 📝 Commits

### Padrão de Commits
```
feat: descrição da feature
fix: descrição do bug fix
chore: mudanças de configuração
docs: mudanças na documentação
refactor: refatoração de código
```

### Exemplos
- `feat: migrate builder components to Next.js`
- `fix: correct locale detection in middleware`
- `chore: update TypeScript config for Next.js 15`
- `docs: add component migration guide`

## 🚀 Próximos Passos

1. Migrar componentes do builder (copiar direto, adicionar "use client")
2. Migrar contextos (adaptar para SSR)
3. Migrar hooks customizados
4. Migrar componentes de pagamento
5. Migrar componentes do checkout
6. Migrar tracking pixels
7. Testar fluxo completo
8. Testar em navegadores de redes sociais

## 💡 Dicas

1. **Sempre** consultar `MIGRACAO_COMPONENTES.md` antes de migrar
2. **Sempre** testar após cada migração
3. **Sempre** manter compatibilidade com APIs existentes
4. **Nunca** reescrever componentes do builder - apenas adaptar
5. **Nunca** usar código específico do Vite
6. **Sempre** usar helpers de `lib/env.ts` para env vars
7. **Sempre** adicionar `"use client"` em componentes interativos

