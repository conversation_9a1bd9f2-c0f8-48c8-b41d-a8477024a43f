{"name": "@my-better-t-app/api", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}, "./*": {"types": "./dist/*.d.ts", "default": "./src/*.ts"}}, "type": "module", "scripts": {"build": "tsdown"}, "devDependencies": {"tsdown": "catalog:"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@orpc/server": "catalog:", "@orpc/client": "catalog:", "@orpc/openapi": "catalog:", "@orpc/zod": "catalog:", "elysia": "catalog:", "dotenv": "catalog:", "zod": "catalog:", "@my-better-t-app/db": "workspace:*"}}