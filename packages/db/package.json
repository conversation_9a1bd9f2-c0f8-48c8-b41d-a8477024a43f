{"name": "@my-better-t-app/db", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}, "./*": {"types": "./dist/*.d.ts", "default": "./src/*.ts"}}, "scripts": {"build": "tsdown", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:start": "docker compose up -d", "db:watch": "docker compose up", "db:stop": "docker compose stop", "db:down": "docker compose down"}, "devDependencies": {"drizzle-kit": "^0.31.2", "@types/pg": "^8.11.11", "tsdown": "catalog:"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"drizzle-orm": "^0.44.2", "pg": "^8.14.1", "dotenv": "catalog:", "zod": "catalog:"}}