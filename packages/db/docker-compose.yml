name: my-better-t-app

services:
  postgres:
    image: postgres
    container_name: my-better-t-app-postgres
    environment:
      POSTGRES_DB: my-better-t-app
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - my-better-t-app_postgres_data:/var/lib/postgresql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

volumes:
  my-better-t-app_postgres_data: