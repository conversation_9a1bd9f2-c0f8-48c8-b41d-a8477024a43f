{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "tsx watch src/index.ts", "start": "node dist/index.js"}, "dependencies": {"elysia": "catalog:", "@elysiajs/cors": "^1.3.3", "@elysiajs/node": "^1.3.1", "@orpc/server": "catalog:", "@orpc/openapi": "catalog:", "@orpc/zod": "catalog:", "dotenv": "catalog:", "zod": "catalog:", "@my-better-t-app/api": "workspace:*", "@my-better-t-app/db": "workspace:*"}, "devDependencies": {"typescript": "catalog:", "tsx": "^4.19.2", "@types/node": "catalog:", "tsdown": "catalog:"}}