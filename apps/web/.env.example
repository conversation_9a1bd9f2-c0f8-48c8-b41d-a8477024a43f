# API Configuration (Server-side only)
CHECKOUT_API_URL=https://checkout-api.example.com
BASE_CHECKOUT_API_URL=https://checkout-api.example.com
API_BASE_URL=https://api.example.com

# Branding & Images (Public - accessible on client)
NEXT_PUBLIC_BRANDING_IMAGES_URL=https://images.example.com
NEXT_PUBLIC_BRANDING=default
NEXT_PUBLIC_CAKTO_URL=https://cakto.example.com

# Payment Providers (Public - accessible on client)
NEXT_PUBLIC_HOPYPAY_PUBLIC_KEY=your_hopypay_public_key

# PostHog Analytics (Public - accessible on client)
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key
NEXT_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com

# 3DS Configuration (Public - accessible on client)
NEXT_PUBLIC_3DS_PROVIDER=cielo
NEXT_PUBLIC_CIELO_3DS_API_VERSION=v2
NEXT_PUBLIC_CIELO_3DS_SCRIPT=https://3ds2.cielo.com.br/v1/3ds2.min.js
NEXT_PUBLIC_PAGARME_3DS_SCRIPT=https://3ds2.pagar.me/v1/3ds2.min.js

# Antifraud (Nethone) (Public - accessible on client)
NEXT_PUBLIC_NETHONE_SCRIPT_SRC=https://nethone.example.com/script.js

# Google Pay (Public - accessible on client)
NEXT_PUBLIC_GOOGLEPAY_ENVIRONMENT=TEST
NEXT_PUBLIC_GOOGLEPAY_GATEWAY=your_gateway
NEXT_PUBLIC_GOOGLEPAY_GATEWAY_MERCHANT_ID=your_merchant_id
NEXT_PUBLIC_GOOGLEPAY_MERCHANT_NAME=Your Merchant Name
NEXT_PUBLIC_GOOGLEPAY_MERCHANT_ID=your_merchant_id

# Google Maps (Public - accessible on client)
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# PIX Configuration (Public - accessible on client)
NEXT_PUBLIC_PIX_CHECK_INTERVAL=5000
NEXT_PUBLIC_PIX_CHECK_DURATION=300000

# Mock API (for testing) (Public - accessible on client)
NEXT_PUBLIC_MOCK_API_URL=https://mock-api.example.com

# Environment (Public - accessible on client)
NEXT_PUBLIC_ENVIRONMENT=development

# GeoIP Resolver (Server-side only)
GEOIP_RESOLVER_URL=https://ipapi.co/:ip/country/
GEOIP_RESOLVER_TOKEN=
