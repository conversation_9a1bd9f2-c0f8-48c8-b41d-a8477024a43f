import { NextResponse, type NextRequest } from "next/server";

import {
	LOCALE_COOKIE,
	type Locale,
	isSupportedLocale,
} from "@/lib/i18n/config";
import {
	getLocaleFromPathname,
	resolveLocaleFromRequest,
} from "@/lib/i18n/locale";

const PUBLIC_FILE = /^\/(_next|favicon\.ico|robots\.txt|sitemap\.xml)/;

export async function middleware(request: NextRequest) {
	const { pathname } = request.nextUrl;

	if (PUBLIC_FILE.test(pathname)) {
		return NextResponse.next();
	}

	const existingLocale = getLocaleFromPathname(pathname);
	if (existingLocale) {
		return setLocaleCookieIfNeeded(request, existingLocale);
	}

	const locale = await resolveLocaleFromRequest(request);
	const response = NextResponse.redirect(
		getLocaleRedirectUrl(request, locale),
	);

	response.cookies.set(LOCALE_COOKIE, locale, {
		path: "/",
		httpOnly: false,
		secure: process.env.NODE_ENV === "production",
		maxAge: 60 * 60 * 24 * 365,
	});

	return response;
}

function getLocaleRedirectUrl(request: NextRequest, locale: Locale) {
	const url = request.nextUrl.clone();
	url.pathname = `/${locale}${request.nextUrl.pathname}`;
	return url;
}

function setLocaleCookieIfNeeded(request: NextRequest, locale: Locale) {
	const cookieLocale = request.cookies.get(LOCALE_COOKIE)?.value;
	if (cookieLocale && isSupportedLocale(cookieLocale) && cookieLocale === locale) {
		return NextResponse.next();
	}

	const response = NextResponse.next();
	response.cookies.set(LOCALE_COOKIE, locale, {
		path: "/",
		httpOnly: false,
		secure: process.env.NODE_ENV === "production",
		maxAge: 60 * 60 * 24 * 365,
	});

	return response;
}

export const config = {
	matcher: [
		"/((?!_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|api|assets).*)",
	],
};


