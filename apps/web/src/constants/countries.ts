import type { CountryConfig, CountryCode } from "@/types/country";

export const COUNTRIES: Record<CountryCode, CountryConfig> = {
  BR: {
    code: "BR",
    name: "Brasil",
    flag: "🇧🇷",
    language: "pt",
    currency: "BRL",
    provider: "cakto",
    paymentMethods: [
      "pix",
      "pix_auto",
      "boleto",
      "credit_card",
      "picpay",
      "googlepay",
      "applepay",
      "openfinance_nubank",
    ],
    documentTypes: ["cpf", "cnpj"],
    phoneFormat: "(99) 99999-9999",
    zipCodeFormat: "99999-999",
    zipCodeLabel: "CEP",
    documentLabel: "CPF/CNPJ",
  },
  MX: {
    code: "MX",
    name: "México",
    flag: "🇲🇽",
    language: "es",
    currency: "MXN",
    provider: "ebanx",
    paymentMethods: ["credit_card", "spei", "oxxo"],
    documentTypes: ["curp", "rfc"],
    phoneFormat: "+52 99 9999 9999",
    zipCodeFormat: "99999",
    zipCodeLabel: "Código Postal",
    documentLabel: "CURP/RFC",
  },
  AR: {
    code: "AR",
    name: "Argentina",
    flag: "🇦🇷",
    language: "es",
    currency: "ARS",
    provider: "ebanx",
    paymentMethods: ["credit_card", "rapipago", "pagofacil"],
    documentTypes: ["dni", "cuit"],
    phoneFormat: "+54 9 99 9999 9999",
    zipCodeFormat: "9999",
    zipCodeLabel: "Código Postal",
    documentLabel: "DNI/CUIT",
  },
  CL: {
    code: "CL",
    name: "Chile",
    flag: "🇨🇱",
    language: "es",
    currency: "CLP",
    provider: "ebanx",
    paymentMethods: ["credit_card", "servipag", "multicaja"],
    documentTypes: ["rut"],
    phoneFormat: "+56 9 9999 9999",
    zipCodeFormat: "9999999",
    zipCodeLabel: "Código Postal",
    documentLabel: "RUT",
  },
  CO: {
    code: "CO",
    name: "Colombia",
    flag: "🇨🇴",
    language: "es",
    currency: "COP",
    provider: "ebanx",
    paymentMethods: ["credit_card", "pse", "efecty", "baloto"],
    documentTypes: ["cc", "ce", "nit"],
    phoneFormat: "+57 ************",
    zipCodeFormat: "999999",
    zipCodeLabel: "Código Postal",
    documentLabel: "CC/CE/NIT",
  },
  PE: {
    code: "PE",
    name: "Perú",
    flag: "🇵🇪",
    language: "es",
    currency: "PEN",
    provider: "ebanx",
    paymentMethods: ["credit_card", "pagoefectivo", "safetypay"],
    documentTypes: ["dni", "ruc"],
    phoneFormat: "+51 999 999 999",
    zipCodeFormat: "99999",
    zipCodeLabel: "Código Postal",
    documentLabel: "DNI/RUC",
  },
  US: {
    code: "US",
    name: "United States",
    flag: "🇺🇸",
    language: "en",
    currency: "USD",
    provider: "ebanx",
    paymentMethods: ["credit_card", "googlepay", "applepay"],
    documentTypes: ["ssn"],
    phoneFormat: "+****************",
    zipCodeFormat: "99999",
    zipCodeLabel: "ZIP Code",
    documentLabel: "SSN",
  },
  OTHER: {
    code: "OTHER",
    name: "International",
    flag: "🌎",
    language: "en",
    currency: "USD",
    provider: "ebanx",
    paymentMethods: ["credit_card", "googlepay", "applepay"],
    documentTypes: ["ssn"],
    phoneFormat: "+****************",
    zipCodeFormat: "99999",
    zipCodeLabel: "ZIP Code",
    documentLabel: "Tax ID",
  },
};

// Lista de países para o selector (ordenada por prioridade)
export const COUNTRY_OPTIONS: CountryConfig[] = [
  COUNTRIES.BR,  // Brasil (prioridade máxima)
  COUNTRIES.MX,
  COUNTRIES.AR,
  COUNTRIES.CL,
  COUNTRIES.CO,
  COUNTRIES.PE,
  COUNTRIES.US,
];

// Mapeamento de idioma → país padrão (para compatibilidade com URLs /pt/, /es/, /en/)
export const LANGUAGE_TO_DEFAULT_COUNTRY: Record<string, CountryCode> = {
  pt: "BR",
  es: "MX", // México como padrão para espanhol
  en: "US",
};

