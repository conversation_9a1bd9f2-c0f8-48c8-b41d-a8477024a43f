import { getTypeInfo } from 'credit-card-type';
import { CreditCardType } from 'credit-card-type/dist/types';
import { InstallmentsData, PaymentMethod, PaymentStatus } from '@/types';

export const PaymentStatusLabels: Partial<
  Record<
    PaymentStatus,
    {
      title: string;
      message: string;
    }
  >
> = {
  authorized: {
    title: 'Pagamento autorizado',
    message: 'O pagamento foi autorizado com sucesso.'
  },
  processing: {
    title: 'Pagamento em processamento',
    message: 'O pagamento está sendo processado.'
  },
  canceled: {
    title: 'Pagamento cancelado',
    message: 'O pagamento foi cancelado.'
  },
  chargedback: {
    title: 'Pagamento estornado',
    message: 'O pagamento foi estornado.'
  },
  in_protest: {
    title: 'Pagamento em protesto',
    message: 'O pagamento está em protesto.'
  },
  paid: {
    title: 'Pagamento confirmado',
    message: 'O pagamento foi confirmado.'
  },
  partially_paid: {
    title: 'Pagamento parcial',
    message: 'O pagamento foi parcialmente confirmado.'
  },
  refunded: {
    title: 'Pagamento reembolsado',
    message: 'O pagamento foi reembolsado.'
  },
  refused: {
    title: 'Pagamento recusado',
    message:
      'O pagamento foi recusado pela operadora do seu cartão. Verifique os dados e tente novamente.'
  },
  waiting_payment: {
    title: 'Pagamento pendente',
    message: 'O pagamento está pendente.'
  }
};

export const PaymentApprovedStatus: PaymentStatus[] = ['authorized', 'paid', 'processing'];

export const PaidInstallmentOption: InstallmentsData = {
  value: 0,
  installment: '1'
};

export const PaymentMethods: {
  type: PaymentMethod;
  name: string;
  installments: {
    enabled: boolean;
  };
}[] = [
  {
    type: 'boleto',
    name: 'Boleto',
    installments: {
      enabled: false
    }
  },
  {
    type: 'credit_card',
    name: 'Cartão de Crédito',
    installments: {
      enabled: true
    }
  },
  {
    type: 'picpay',
    name: 'PicPay',
    installments: {
      enabled: false
    }
  },
  {
    type: 'openfinance_nubank',
    name: 'Nubank',
    installments: {
      enabled: false
    }
  },
  {
    type: 'googlepay',
    name: 'Google Pay',
    installments: {
      enabled: true
    }
  },
  {
    type: 'applepay',
    name: 'Apple Pay',
    installments: {
      enabled: true
    }
  },
  {
    type: 'pix',
    name: 'PIX',
    installments: {
      enabled: false
    }
  },
  {
    type: 'pix_auto',
    name: 'PIX Automático',
    installments: {
      enabled: false
    }
  }
];

export const DefaultCreditCardType: CreditCardType = {
  ...getTypeInfo('mastercard'),
  niceType: 'Cartão de Crédito',
  type: 'default',
  code: {
    size: 3,
    name: 'CVV'
  }
};

export const RecurrencePeriodMap: Record<
  number,
  {
    label: string;
    description: string;
  }
> = {
  1: {
    label: 'dia',
    description: 'Renovação diária'
  },
  7: {
    label: 'semana',
    description: 'Renovação semanal'
  },
  30: {
    label: 'mês',
    description: 'Renovação mensal'
  },
  60: {
    label: 'bimestre',
    description: 'Renovação bimestral'
  },
  90: {
    label: 'trimestre',
    description: 'Renovação trimestral'
  },
  180: {
    label: 'semestre',
    description: 'Renovação semestral'
  },
  365: {
    label: 'ano',
    description: 'Renovação anual'
  }
};

