import type { CheckoutComponentSealAttibutesType } from "@/types/builder";

const SealTwoIllustration: React.FC<CheckoutComponentSealAttibutesType> = ({
	title,
	subtitle,
	primaryColor,
	titleTextColor,
	darkMode,
}) => (
	<svg
		data-v-6e3475d8=""
		viewBox="0 0 472 361"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
	>
		<path
			data-v-6e3475d8=""
			d="M427.103 244.562L464.382 225.083C464.382 225.083 449.138 220.621 420.605 216.5C392.073 212.38 382.587 214.756 382.587 214.756L328.647 261.29C328.647 261.29 358.48 257.65 393.687 262.199C428.894 266.748 457.753 277.59 457.753 277.59L427.103 244.562Z"
			fill={primaryColor}
			stroke="black"
			strokeWidth="5"
		/>
		<path
			data-v-6e3475d8=""
			d="M44.1845 244.872L7.32182 224.615C7.32182 224.615 22.6568 220.473 51.2694 216.953C79.8821 213.433 89.3158 216.007 89.3158 216.007L142.267 263.663C142.267 263.663 112.518 259.398 77.2224 263.207C41.9272 267.015 12.8473 277.249 12.8473 277.249L44.1845 244.872Z"
			fill={primaryColor}
			stroke="black"
			strokeWidth="5"
		/>
		<path
			data-v-6e3475d8=""
			d="M108.568 245L35.5684 212.278V188H108.568V245Z"
			fill={primaryColor}
			stroke="black"
			strokeWidth="5"
		/>
		<path
			data-v-6e3475d8=""
			d="M363.568 245L432.568 211.704V187H363.568V245Z"
			fill={primaryColor}
			stroke="black"
			strokeWidth="5"
		/>
		<mask data-v-6e3475d8="" id="path-5-inside-1" fill="white">
			<path
				data-v-6e3475d8=""
				d="M214.765 7.08462C227.686 -2.36154 245.239 -2.36154 258.175 7.08462C289.146 29.7374 282.482 27.5714 320.839 27.451C336.858 27.4059 351.057 37.7245 355.946 52.9617C367.678 89.4678 363.572 83.8272 394.678 106.269C407.659 115.64 413.089 132.337 408.095 147.559C396.152 183.914 396.092 176.935 408.095 213.441C413.104 228.648 407.674 245.36 394.678 254.731C363.572 277.158 367.693 271.517 355.946 308.038C351.057 323.29 336.843 333.609 320.839 333.549C282.467 333.429 289.131 331.263 258.175 353.915C245.254 363.362 227.701 363.362 214.765 353.915C183.794 331.278 190.458 333.414 152.101 333.549C136.082 333.594 121.883 323.275 116.994 308.038C105.262 271.487 109.308 277.128 78.2617 254.731C65.2808 245.36 59.8507 228.663 64.8446 213.441C76.8027 177.086 76.8478 184.065 64.8446 147.559C59.8357 132.322 65.2657 115.625 78.2467 106.254C109.263 83.8573 105.246 89.543 116.979 52.9467C121.868 37.6944 136.082 27.3758 152.086 27.436C190.367 27.5563 183.614 29.8577 214.765 7.08462Z"
			/>
		</mask>
		<path
			data-v-6e3475d8=""
			d="M214.765 7.08462C227.686 -2.36154 245.239 -2.36154 258.175 7.08462C289.146 29.7374 282.482 27.5714 320.839 27.451C336.858 27.4059 351.057 37.7245 355.946 52.9617C367.678 89.4678 363.572 83.8272 394.678 106.269C407.659 115.64 413.089 132.337 408.095 147.559C396.152 183.914 396.092 176.935 408.095 213.441C413.104 228.648 407.674 245.36 394.678 254.731C363.572 277.158 367.693 271.517 355.946 308.038C351.057 323.29 336.843 333.609 320.839 333.549C282.467 333.429 289.131 331.263 258.175 353.915C245.254 363.362 227.701 363.362 214.765 353.915C183.794 331.278 190.458 333.414 152.101 333.549C136.082 333.594 121.883 323.275 116.994 308.038C105.262 271.487 109.308 277.128 78.2617 254.731C65.2808 245.36 59.8507 228.663 64.8446 213.441C76.8027 177.086 76.8478 184.065 64.8446 147.559C59.8357 132.322 65.2657 115.625 78.2467 106.254C109.263 83.8573 105.246 89.543 116.979 52.9467C121.868 37.6944 136.082 27.3758 152.086 27.436C190.367 27.5563 183.614 29.8577 214.765 7.08462Z"
			fill={darkMode ? "black" : "white"}
			stroke="black"
			strokeWidth="10"
			mask="url(#path-5-inside-1)"
		/>
		<mask data-v-6e3475d8="" id="path-6-inside-2" fill="white">
			<path
				data-v-6e3475d8=""
				d="M216.717 25.6795C228.202 17.2722 243.804 17.2722 255.302 25.6795C282.83 45.841 276.907 43.9133 311 43.8062C325.238 43.766 337.859 52.9498 342.204 66.5113C352.633 99.0027 348.983 93.9824 376.631 113.956C388.169 122.297 392.996 137.157 388.557 150.705C377.941 183.063 377.888 176.851 388.557 209.342C393.009 222.877 388.182 237.75 376.631 246.091C348.983 266.052 352.646 261.031 342.204 293.536C337.859 307.111 325.225 316.295 311 316.241C276.894 316.134 282.817 314.206 255.302 334.368C243.817 342.775 228.215 342.775 216.717 334.368C189.189 314.22 195.112 316.121 161.019 316.241C146.781 316.281 134.16 307.097 129.815 293.536C119.386 261.004 122.983 266.025 95.388 246.091C83.85 237.75 79.0236 222.89 83.4623 209.342C94.0911 176.985 94.1312 183.196 83.4623 150.705C79.0102 137.144 83.8366 122.283 95.3746 113.943C122.943 94.0092 119.373 99.0696 129.801 66.4979C134.146 52.923 146.781 43.7392 161.006 43.7928C195.032 43.8999 189.029 45.9481 216.717 25.6795Z"
			/>
		</mask>
		<path
			data-v-6e3475d8=""
			d="M216.717 25.6795C228.202 17.2722 243.804 17.2722 255.302 25.6795C282.83 45.841 276.907 43.9133 311 43.8062C325.238 43.766 337.859 52.9498 342.204 66.5113C352.633 99.0027 348.983 93.9824 376.631 113.956C388.169 122.297 392.996 137.157 388.557 150.705C377.941 183.063 377.888 176.851 388.557 209.342C393.009 222.877 388.182 237.75 376.631 246.091C348.983 266.052 352.646 261.031 342.204 293.536C337.859 307.111 325.225 316.295 311 316.241C276.894 316.134 282.817 314.206 255.302 334.368C243.817 342.775 228.215 342.775 216.717 334.368C189.189 314.22 195.112 316.121 161.019 316.241C146.781 316.281 134.16 307.097 129.815 293.536C119.386 261.004 122.983 266.025 95.388 246.091C83.85 237.75 79.0236 222.89 83.4623 209.342C94.0911 176.985 94.1312 183.196 83.4623 150.705C79.0102 137.144 83.8366 122.283 95.3746 113.943C122.943 94.0092 119.373 99.0696 129.801 66.4979C134.146 52.923 146.781 43.7392 161.006 43.7928C195.032 43.8999 189.029 45.9481 216.717 25.6795Z"
			stroke={primaryColor}
			strokeWidth="10"
			fill={darkMode ? "black" : "white"}
			mask="url(#path-6-inside-2)"
		/>
		<path
			data-v-6e3475d8=""
			d="M69.5 204H403L406.5 221C406.5 221 302.694 215.038 230.97 215.038C159.246 215.038 65 221.5 65 221.5L69.5 204Z"
			fill="black"
			fillOpacity="0.2"
		/>
		<mask data-v-6e3475d8="" id="path-8-inside-3" fill="white">
			<path
				data-v-6e3475d8=""
				d="M28 112H444L435.266 214C435.266 214 323.222 208.858 236.115 208.858C149.008 208.858 33.8608 214 33.8608 214L28 112Z"
			/>
		</mask>
		<path
			data-v-6e3475d8=""
			d="M28 112H444L435.266 214C435.266 214 323.222 208.858 236.115 208.858C149.008 208.858 33.8608 214 33.8608 214L28 112Z"
			fill={primaryColor}
			stroke="black"
			strokeWidth="10"
			mask="url(#path-8-inside-3)"
		/>
		<path
			data-v-6e3475d8=""
			d="M235 63.5869L238.668 71.1549L247 72.3059L240.936 78.1339L242.416 86.4129L235 82.4459L227.583 86.4129L229.064 78.1339L223 72.3059L231.332 71.1549L235 63.5869Z"
			fill={darkMode ? "white" : "black"}
		/>
		<path
			data-v-6e3475d8=""
			d="M270.5 66.4648L273.404 72.4562L280 73.3674L275.199 77.9812L276.371 84.5354L270.5 81.3949L264.628 84.5354L265.801 77.9812L261 73.3674L267.596 72.4562L270.5 66.4648Z"
			fill={darkMode ? "white" : "black"}
		/>
		<path
			data-v-6e3475d8=""
			d="M199.5 66.4648L202.404 72.4562L209 73.3674L204.199 77.9812L205.371 84.5354L199.5 81.3949L193.628 84.5354L194.801 77.9812L190 73.3674L196.596 72.4562L199.5 66.4648Z"
			fill={darkMode ? "white" : "black"}
		/>
		<text
			data-v-6e3475d8=""
			transform="translate(245 188)"
			id="title-seal"
			fontSize="70"
			fontWeight="bold"
			fill={titleTextColor}
		>
			<tspan data-v-6e3475d8="" textAnchor="middle">
				{title}
			</tspan>
		</text>
		<text
			data-v-6e3475d8=""
			transform="translate(235 260)"
			id="subtitle-seal"
			fontSize="22"
			fontWeight="bold"
			fill={darkMode ? "white" : "black"}
		>
			<tspan data-v-6e3475d8="" textAnchor="middle">
				{subtitle}
			</tspan>
		</text>
	</svg>
);

export default SealTwoIllustration;
