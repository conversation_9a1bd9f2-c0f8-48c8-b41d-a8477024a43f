import { Suspense } from "react";

import { CheckoutClient } from "@/components/checkout/checkout-client";
import { Loader } from "@/components/loader";
import { getCheckoutData } from "@/lib/api/checkout";
import { isSupportedLocale, type Locale } from "@/lib/i18n/config";

type CheckoutPageProps = {
	params: Promise<{
		locale: string;
		id: string;
	}>;
	searchParams: Promise<{
		affiliate?: string;
	}>;
};

export default async function CheckoutPage({
	params,
	searchParams,
}: CheckoutPageProps) {
	const { locale: localeParam, id } = await params;
	const { affiliate } = await searchParams;
	const locale = normalizeLocaleParam(localeParam);

	if (!locale) {
		throw new Error("Unsupported locale");
	}

	const checkoutData = await getCheckoutData(id, affiliate);

	return (
		<Suspense fallback={<Loader messageKey="checkout.loading" />}>
			<CheckoutClient initialData={checkoutData} checkoutId={id} />
		</Suspense>
	);
}

function normalizeLocaleParam(value: string): Locale | undefined {
	if (isSupportedLocale(value)) {
		return value;
	}

	return undefined;
}


