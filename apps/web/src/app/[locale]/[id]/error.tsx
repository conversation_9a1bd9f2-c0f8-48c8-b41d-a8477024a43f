"use client";

import { AlertOctagon, RotateCw } from "lucide-react";

import { Button } from "@/components/ui/button";
import { useTranslation } from "@/hooks/useTranslation";

type CheckoutErrorProps = {
	error: Error & { digest?: string };
	reset: () => void;
};

export default function CheckoutError({ error, reset }: CheckoutErrorProps) {
	const { t } = useTranslation();

	return (
		<div className="flex min-h-[50vh] flex-col items-center justify-center gap-6 p-6 text-center">
			<div className="flex size-16 items-center justify-center rounded-full border border-destructive/30 bg-destructive/5 text-destructive">
				<AlertOctagon className="size-8" />
			</div>
			<div className="space-y-2">
				<h2 className="text-xl font-semibold">{t("errors.generic.something_went_wrong")}</h2>
				<p className="text-sm text-muted-foreground">
					{error.message || t("errors.generic.contact_support")}
				</p>
			</div>
			<Button type="button" onClick={reset} variant="outline" className="gap-2">
				<RotateCw className="size-4" />
				{t("common.retry")}
			</Button>
		</div>
	);
}

