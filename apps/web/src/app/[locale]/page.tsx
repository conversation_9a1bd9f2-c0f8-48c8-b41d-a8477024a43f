"use client";

import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "@/hooks/useTranslation";

export default function LocaleRootPage() {
	const { t } = useTranslation();

	return (
		<main className="flex min-h-[50vh] flex-col items-center justify-center gap-4 p-6 text-center">
			<h1 className="text-2xl font-semibold">{t("common.checkoutTitle")}</h1>
			<p className="max-w-md text-muted-foreground">{t("common.selectCheckout")}</p>
			<Button asChild>
				<Link href="/">{t("common.continue")}</Link>
			</Button>
		</main>
	);
}

