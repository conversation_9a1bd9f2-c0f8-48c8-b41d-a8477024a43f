import { notFound } from "next/navigation";
import { Suspense } from "react";

import { CheckoutClient } from "@/components/checkout/checkout-client";
import { Loader } from "@/components/loader";
import { getCheckoutData } from "@/lib/api/checkout";
import { isSupportedLocale } from "@/lib/i18n/config";

type PreviewPageProps = {
	params: Promise<{ locale: string }>;
	searchParams: Promise<{ id?: string; affiliate?: string }>;
};

export default async function CheckoutPreviewPage({
	params,
	searchParams,
}: PreviewPageProps) {
	const { locale } = await params;
	const { id, affiliate } = await searchParams;

	if (!isSupportedLocale(locale)) {
		notFound();
	}

	if (!id) {
		throw new Error("Missing checkout identifier");
	}

	const checkoutData = await getCheckoutData(id, affiliate);

	return (
		<Suspense fallback={<Loader messageKey="checkout.loading" />}>
			<CheckoutClient initialData={checkoutData} checkoutId={id} />
		</Suspense>
	);
}


