import { notFound } from "next/navigation";
import type { ReactNode } from "react";

import { IntlProvider } from "@/contexts/intl-context";
import { loadMessages } from "@/lib/i18n";
import { isSupportedLocale, type Locale } from "@/lib/i18n/config";

type LocaleLayoutProps = {
	children: ReactNode;
	params: Promise<{
		locale: string;
	}>;
};

export async function generateStaticParams() {
	return [];
}

export default async function LocaleLayout({
	children,
	params,
}: LocaleLayoutProps) {
	const { locale: localeParam } = await params;
	const locale = normalizeLocaleParam(localeParam);

	if (!locale) {
		notFound();
	}

	const messages = await loadMessages(locale);

	return (
		<IntlProvider locale={locale} messages={messages}>
			{children}
		</IntlProvider>
	);
}

function normalizeLocaleParam(value: string): Locale | undefined {
	if (isSupportedLocale(value)) {
		return value;
	}

	return undefined;
}


