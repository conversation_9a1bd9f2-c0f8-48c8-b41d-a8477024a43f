import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const ip = request.headers.get("x-forwarded-for")?.split(",")[0]?.trim() || 
               request.headers.get("x-real-ip") || 
               "unknown";
    
    // Se for localhost ou IP desconhecido, retornar Brasil como padrão
    if (ip === "unknown" || ip === "::1" || ip.startsWith("127.") || ip.startsWith("192.168.") || ip.startsWith("10.")) {
      return NextResponse.json({
        ip: ip,
        countryCode: "BR",
        country: "Brazil",
        city: "São Paulo",
        region: "São Paulo",
      });
    }
    
    // Chamar ipapi.co para detectar país
    const geoipUrl = process.env.NEXT_PUBLIC_GEOIP_API_URL || "https://ipapi.co";
    const response = await fetch(`${geoipUrl}/${ip}/json/`, {
      headers: {
        "User-Agent": "Cakto-Checkout/1.0",
      },
    });
    
    if (!response.ok) {
      throw new Error(`GeoIP API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Mapear country_code de 2 letras para nossos códigos
    const countryCodeMap: Record<string, string> = {
      "BR": "BR",
      "MX": "MX",
      "AR": "AR",
      "CL": "CL",
      "CO": "CO",
      "PE": "PE",
      "US": "US",
    };
    
    const detectedCountryCode = data.country_code || data.country || "BR";
    const mappedCountryCode = countryCodeMap[detectedCountryCode] || "OTHER";
    
    return NextResponse.json({
      ip,
      countryCode: mappedCountryCode,
      country: data.country_name || "Brazil",
      city: data.city || "",
      region: data.region || "",
    });
  } catch (error) {
    console.error("GeoIP error:", error);
    
    // Fallback para Brasil em caso de erro
    return NextResponse.json({
      countryCode: "BR",
      country: "Brazil",
      ip: "unknown",
    });
  }
}

