@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --font-sans: "<PERSON>", "<PERSON>eist", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  /* ==================== CAKTO BRAND COLORS ==================== */
  
  /* Primary Colors - Cakto Green */
  --color-cakto-primary: #0F7864;
  --color-cakto-primary-hover: #0b6856;
  --color-cakto-primary-light: #f0fdf9;
  --color-cakto-primary-lighter: #ecfdf5;
  
  /* Primary Colors - Nommi Blue */
  --color-nommi-primary: #2886B9;
  --color-nommi-primary-hover: #1f6e94;
  --color-nommi-primary-light: #f0f9ff;
  
  /* Secondary Colors */
  --color-secondary: #eef2ff;
  --color-secondary-dark: #e0e7ff;
  
  /* Border Colors */
  --color-border-light: #919EAB33;
  --color-border-default: #d1d5db;
  
  /* ==================== SHADOWS ==================== */
  
  /* Button Shadows */
  --shadow-button-hover: 0px 8px 16px 0px rgba(15, 120, 101, 0.24);
  --shadow-button-nommi-hover: 0px 8px 16px 0px rgba(40, 134, 185, 0.24);
  
  /* Card Shadows */
  --shadow-card-subtle: 0px 1px 3px 0px rgba(15, 120, 101, 0.1);
  
  /* ==================== RADIUS ==================== */
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

html,
body {
  @apply bg-white dark:bg-gray-950;

  @media (prefers-color-scheme: dark) {
    color-scheme: dark;
  }
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* ==================== BRAND UTILITIES ==================== */

/* Background Colors */
.bg-brand-primary {
  background-color: var(--color-cakto-primary);
}

.bg-brand-primary-hover {
  background-color: var(--color-cakto-primary-hover);
}

.bg-brand-primary-light {
  background-color: var(--color-cakto-primary-light);
}

.bg-brand-primary-lighter {
  background-color: var(--color-cakto-primary-lighter);
}

.bg-secondary-light {
  background-color: var(--color-secondary);
}

/* Text Colors */
.text-brand-primary {
  color: var(--color-cakto-primary);
}

.text-brand-primary-hover {
  color: var(--color-cakto-primary-hover);
}

/* Border Colors */
.border-brand-primary {
  border-color: var(--color-cakto-primary);
}

.border-brand-primary-hover {
  border-color: var(--color-cakto-primary-hover);
}

/* Hover States */
.hover\:bg-brand-primary-hover:hover {
  background-color: var(--color-cakto-primary-hover);
}

.hover\:bg-brand-primary-light:hover {
  background-color: var(--color-cakto-primary-light);
}

.hover\:border-brand-primary:hover {
  border-color: var(--color-cakto-primary);
}

.hover\:text-brand-primary:hover {
  color: var(--color-cakto-primary);
}

/* Focus States */
.focus\:ring-brand-primary:focus {
  --tw-ring-color: var(--color-cakto-primary);
}

/* Shadow Utilities */
.shadow-button-hover {
  box-shadow: var(--shadow-button-hover);
}

.shadow-card-subtle {
  box-shadow: var(--shadow-card-subtle);
}

/* Hover Shadow */
.hover\:shadow-button-hover:hover {
  box-shadow: var(--shadow-button-hover);
}

/* ==================== NOMMI BRAND OVERRIDES ==================== */

[data-branding="nommi"] .bg-brand-primary {
  background-color: var(--color-nommi-primary);
}

[data-branding="nommi"] .bg-brand-primary-hover {
  background-color: var(--color-nommi-primary-hover);
}

[data-branding="nommi"] .bg-brand-primary-light {
  background-color: var(--color-nommi-primary-light);
}

[data-branding="nommi"] .text-brand-primary {
  color: var(--color-nommi-primary);
}

[data-branding="nommi"] .border-brand-primary {
  border-color: var(--color-nommi-primary);
}

[data-branding="nommi"] .shadow-button-hover,
[data-branding="nommi"] .hover\:shadow-button-hover:hover {
  box-shadow: var(--shadow-button-nommi-hover);
}
