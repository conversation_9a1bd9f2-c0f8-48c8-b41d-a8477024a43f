export type MessageValue = string;

export type Messages = {
	checkout: {
		title: MessageValue;
		subtitle: MessageValue;
		loading: MessageValue;
		loading_checkout: MessageValue;
		order_summary: MessageValue;
		coupon_code: MessageValue;
		coupon_placeholder: MessageValue;
		coupon_applied: MessageValue;
		coupon_success: MessageValue;
		service_fee: MessageValue;
		service_fee_tooltip: MessageValue;
		total: MessageValue;
		subtotal: MessageValue;
		discount: MessageValue;
		secure_purchase: MessageValue;
		recaptcha_notice: MessageValue;
		contact_info_title: MessageValue;
		payment_method_title: MessageValue;
		processing_payment: MessageValue;
		no_payment_methods: MessageValue;
		not_found: MessageValue;
	};
	common: {
		continue: MessageValue;
		retry: MessageValue;
		save: MessageValue;
		cancel: MessageValue;
		close: MessageValue;
		yes: MessageValue;
		no: MessageValue;
		loading: MessageValue;
		checkoutTitle: MessageValue;
		selectCheckout: MessageValue;
		apply: MessageValue;
		remove: MessageValue;
		from: MessageValue;
		per: MessageValue;
		month: MessageValue;
		year: MessageValue;
		week: MessageValue;
		day: MessageValue;
		or: MessageValue;
		and: MessageValue;
		to: MessageValue;
		of: MessageValue;
		at: MessageValue;
		in: MessageValue;
		on: MessageValue;
		with: MessageValue;
		without: MessageValue;
		free: MessageValue;
		discount_label: MessageValue;
	};
	errors: {
		validation: {
			required: MessageValue;
			invalid_email: MessageValue;
			invalid_phone: MessageValue;
			invalid_cpf: MessageValue;
			invalid_cnpj: MessageValue;
			invalid_cpf_cnpj: MessageValue;
			invalid_card_number: MessageValue;
			invalid_cvv: MessageValue;
			invalid_expiry: MessageValue;
			invalid_date: MessageValue;
			min_length: MessageValue;
			max_length: MessageValue;
			generic: MessageValue;
		};
		network: {
			connection_failed: MessageValue;
			timeout: MessageValue;
			server_error: MessageValue;
			not_found: MessageValue;
			unauthorized: MessageValue;
			forbidden: MessageValue;
			generic: MessageValue;
		};
		payment: {
			card_declined: MessageValue;
			insufficient_funds: MessageValue;
			invalid_card: MessageValue;
			expired_card: MessageValue;
			processing_error: MessageValue;
			provider_error: MessageValue;
			generic: MessageValue;
		};
		threeds: {
			authentication_failed: MessageValue;
			challenge_failed: MessageValue;
			not_enrolled: MessageValue;
			unsupported_card: MessageValue;
			generic: MessageValue;
		};
		system: {
			unexpected_error: MessageValue;
			service_unavailable: MessageValue;
			maintenance: MessageValue;
			generic: MessageValue;
		};
		generic: {
			something_went_wrong: MessageValue;
			try_again: MessageValue;
			contact_support: MessageValue;
		};
	};
	form: {
		labels: {
			name: MessageValue;
			email: MessageValue;
			phone: MessageValue;
			cpf: MessageValue;
			cnpj: MessageValue;
			cpf_cnpj: MessageValue;
			card_number: MessageValue;
			card_expiry: MessageValue;
			card_cvv: MessageValue;
			card_holder_name: MessageValue;
			installments: MessageValue;
			coupon: MessageValue;
			zipcode: MessageValue;
			street: MessageValue;
			number: MessageValue;
			complement: MessageValue;
			neighborhood: MessageValue;
			city: MessageValue;
			state: MessageValue;
			shipping: MessageValue;
		};
		placeholders: {
			name: MessageValue;
			email: MessageValue;
			phone: MessageValue;
			cpf_cnpj: MessageValue;
			card_number: MessageValue;
			card_expiry: MessageValue;
			card_cvv: MessageValue;
			card_holder_name: MessageValue;
			coupon: MessageValue;
			zipcode: MessageValue;
			street: MessageValue;
			number: MessageValue;
			complement: MessageValue;
			neighborhood: MessageValue;
			city: MessageValue;
			state: MessageValue;
			shipping: MessageValue;
		};
		helpers: {
			cpf_required: MessageValue;
			phone_format: MessageValue;
			card_number_hint: MessageValue;
		};
	};
	payment: {
		methods: {
			credit_card: MessageValue;
			pix: MessageValue;
			pix_auto: MessageValue;
			boleto: MessageValue;
			picpay: MessageValue;
			applepay: MessageValue;
			googlepay: MessageValue;
			openfinance_nubank: MessageValue;
		};
		messages: {
			processing: MessageValue;
			waiting: MessageValue;
			success: MessageValue;
			failed: MessageValue;
			expired: MessageValue;
			check_payment: MessageValue;
			checking_payment: MessageValue;
		};
		installments: MessageValue;
		subscription: MessageValue;
		installments_of: MessageValue;
		installments_with_interest: MessageValue;
		installments_without_interest: MessageValue;
		one_time: MessageValue;
		new_badge: MessageValue;
		loading_installments: MessageValue;
		pix: {
			title: MessageValue;
			subtitle: MessageValue;
			qr_code_label: MessageValue;
			copy_code: MessageValue;
			code_copied: MessageValue;
			expires_in: MessageValue;
			waiting_payment: MessageValue;
			instant_release: MessageValue;
			easy_to_use: MessageValue;
			scan_qrcode: MessageValue;
			or_copy_code: MessageValue;
			scan_instruction: MessageValue;
			open_bank_app: MessageValue;
			scan_code: MessageValue;
			confirm_payment: MessageValue;
			step_1: MessageValue;
			step_2: MessageValue;
			step_3: MessageValue;
			step_4: MessageValue;
			generated_success: MessageValue;
			finalize_payment: MessageValue;
			expires_in_label: MessageValue;
			approval_time: MessageValue;
			already_paid: MessageValue;
		};
		pix_auto: {
			title: MessageValue;
			subtitle: MessageValue;
			description: MessageValue;
			step_4: MessageValue;
			waiting_payment: MessageValue;
			copy_code: MessageValue;
			info_1: MessageValue;
			info_2: MessageValue;
			advantages: {
				title: MessageValue;
				advantage_1: MessageValue;
				advantage_2: MessageValue;
				advantage_3: MessageValue;
				advantage_4: MessageValue;
			};
		};
		boleto: {
			title: MessageValue;
			subtitle: MessageValue;
			barcode_label: MessageValue;
			copy_code: MessageValue;
			code_copied: MessageValue;
			due_date: MessageValue;
			download: MessageValue;
			print: MessageValue;
			print_billet: MessageValue;
			pay_at_bank: MessageValue;
			payer_document: MessageValue;
			instructions: MessageValue;
			instruction_1: MessageValue;
			instruction_2: MessageValue;
			instruction_3: MessageValue;
			amount_label: MessageValue;
			due_date_label: MessageValue;
			barcode_number: MessageValue;
			digital_access_info: MessageValue;
		};
		credit_card: {
			title: MessageValue;
			subtitle: MessageValue;
			card_number: MessageValue;
			card_number_placeholder: MessageValue;
			cardholder_name: MessageValue;
			cardholder_name_placeholder: MessageValue;
			expiration_date: MessageValue;
			expiration_placeholder: MessageValue;
			cvv: MessageValue;
			cvv_placeholder: MessageValue;
			save_card: MessageValue;
			installments_label: MessageValue;
			select_installments: MessageValue;
			secure_payment: MessageValue;
		};
		picpay: {
			title: MessageValue;
			subtitle: MessageValue;
			scan_qrcode: MessageValue;
			waiting_payment: MessageValue;
		};
		applepay: {
			title: MessageValue;
			subtitle: MessageValue;
			button_label: MessageValue;
		};
		googlepay: {
			title: MessageValue;
			subtitle: MessageValue;
			button_label: MessageValue;
		};
		nubank: {
			title: MessageValue;
			subtitle: MessageValue;
			button_label: MessageValue;
		};
	};
	address: {
		title: MessageValue;
		delivery_title: MessageValue;
		zipcode_label: MessageValue;
		zipcode_not_found: MessageValue;
		zipcode_searching: MessageValue;
		zipcode_found: MessageValue;
		street_label: MessageValue;
		number_label: MessageValue;
		complement_label: MessageValue;
		neighborhood_label: MessageValue;
		city_label: MessageValue;
		state_label: MessageValue;
		shipping_method: MessageValue;
		shipping_options: MessageValue;
	};
	bump: {
		add_product: MessageValue;
		selected: MessageValue;
		discount_label: MessageValue;
	};
	success: {
		title: MessageValue;
		subtitle: MessageValue;
		order_number: MessageValue;
		approved: MessageValue;
		rejected: MessageValue;
		pending: MessageValue;
		redirecting: MessageValue;
		email_sent: MessageValue;
		access_product: MessageValue;
		thank_you: MessageValue;
		confirmation_email: MessageValue;
		order_details: MessageValue;
		payment_method: MessageValue;
		amount_paid: MessageValue;
		transaction_id: MessageValue;
		purchase_details: MessageValue;
		code_label: MessageValue;
		payment_details: MessageValue;
		amount: MessageValue;
		status: MessageValue;
		transaction_id_label: MessageValue;
		pix_auto_configured: MessageValue;
		pix_auto_configured_description: MessageValue;
		pix_auto_active: MessageValue;
		pix_auto_active_description: MessageValue;
		access_my_product: MessageValue;
		email_confirmation_sent: MessageValue;
		qrcode_generated: MessageValue;
		finalize_payment_now: MessageValue;
		expires_in_label: MessageValue;
		open_picpay_scan: MessageValue;
		qrcode_problem: MessageValue;
		already_paid: MessageValue;
		pay_now_pix_auto: MessageValue;
	};
	about: {
		cpf_title: MessageValue;
		cpf_description: MessageValue;
		pix_security_title: MessageValue;
		pix_security_description: MessageValue;
	};
	in_app_browser: {
		warning_title: MessageValue;
		warning_message: MessageValue;
		open_in_browser: MessageValue;
	};
	errors: {
		validation: {
			required: MessageValue;
			invalid_email: MessageValue;
			invalid_phone: MessageValue;
			invalid_cpf: MessageValue;
			invalid_cnpj: MessageValue;
			invalid_cpf_cnpj: MessageValue;
			invalid_expiry: MessageValue;
			invalid_cvv: MessageValue;
			invalid_card: MessageValue;
			generic: MessageValue;
			invalid_date: MessageValue;
		};
		payment: {
			check_error: MessageValue;
			polling_error: MessageValue;
		};
	};
	notifications: {
		payment_unavailable: MessageValue;
		payment_not_available: MessageValue;
	};
};
