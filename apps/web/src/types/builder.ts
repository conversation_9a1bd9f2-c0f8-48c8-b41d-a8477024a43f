export type CheckoutImage = {
  id: string;
  preview: string;
};

export enum CheckoutAlignment {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right'
}

export enum CheckoutComponentTypeNames {
  TEXT = 'text',
  IMAGE = 'image',
  ADVANTAGE = 'advantage',
  SEAL = 'seal',
  HEADER = 'header',
  LIST = 'list',
  COUNTDOWN = 'countdown',
  TESTIMONIAL = 'testimonial',
  VIDEO = 'video',
  FACEBOOK = 'facebook',
  MAP = 'map',
  CHECKOUT = 'checkout'
}

export enum CheckoutExtraComponentTypeNames {
  EXIT_POPUP = 'exitPopup',
  NOTIFICATION = 'notification',
  CHAT = 'chat'
}

export type CheckoutComponentType<T = unknown> = {
  id: string;
  type: CheckoutComponentTypeNames | CheckoutExtraComponentTypeNames;
  attributes: T;
  index?: number;
};

export type CheckoutComponentTextAttibutesType = {
  backgroundColor: string;
  borderColor: string;
  text: string;
  borderWidth: number;
  borderRadius: number;
};

export type CheckoutComponentImageAttibutesType = {
  image: CheckoutImage;
  alignment: CheckoutAlignment;
  redirectUrl: string;
};

export type CheckoutComponentAdvantageAttibutesType = {
  icon:
    | 'atsign'
    | 'chart'
    | 'chat'
    | 'click'
    | 'cloud'
    | 'download'
    | 'file'
    | 'heart'
    | 'people'
    | 'play'
    | 'verified'
    | 'web';
  title: string;
  subtitle: string;
  primaryColor: string;
  titleTextColor: string;
  size: 'original' | 'small' | 'medium' | 'large';
  darkMode: boolean;
  vertical: boolean;
};

export type CheckoutComponentSealAttibutesType = {
  type: 'one' | 'two' | 'three';
  title: string;
  subtitle: string;
  topText: string;
  primaryColor: string;
  titleTextColor: string;
  alignment: CheckoutAlignment;
  darkMode: boolean;
  width: number;
};

export type CheckoutComponentHeaderAttibutesType = {
  backgroundType: 'image' | 'color' | 'transparent';
  backgroundColor: string;
  backgroundImage: CheckoutImage;
  productImageType: 'same' | 'custom' | 'none';
  productImage: CheckoutImage;
  productImageAlignment: CheckoutAlignment;
  titleFontSize: number;
  titleTextColor: string;
  titleText: string;
  showSubtitle: boolean;
  subtitleFontSize: number;
  subtitleTextColor: string;
  subtitleText: string;
};

export type CheckoutComponentListAttibutesType = {
  backgroundColor: string;
  textColor: string;
  alignment: CheckoutAlignment;
  fontSize: number;
  showTitle: boolean;
  title: string;
  style: 'none' | 'check' | 'decimal' | 'disc';
  iconColor: string;
  items: {
    id: string;
    text: string;
  }[];
};

export type CheckoutComponentCountdownAttibutesType = {
  backgroundColor: string;
  textColor: string;
  activeText: string;
  finishedText: string;
  fixedOnTop: boolean;
  type: 'time' | 'date';
  time: string;
  date: string;
};

export type CheckoutComponentTestimonialAttibutesType = {
  backgroundColor: string;
  textColor: string;
  avatar: CheckoutImage;
  author: string;
  text: string;
  rating: number;
  horizontal: boolean;
};

export type CheckoutComponentVideoAttibutesType = {
  url: string;
  alignment: CheckoutAlignment;
  width: number;
  hideControls: boolean;
};

export type CheckoutComponentFacebookAttibutesType = {
  type: 'commentSection' | 'singleComment' | 'page' | 'post';
  url: string;
  size: 'original' | 'small' | 'medium' | 'large';
  count: number;
  orderBy: 'relevant' | 'latest' | 'oldest';
  tabs: ('timeline' | 'events' | 'messages')[];
  options: ('smallHeader' | 'coverPhoto' | 'facePile' | 'callToAction')[];
};

export type CheckoutComponentMapAttibutesType = {
  address: string;
  alignment: CheckoutAlignment;
  width: number;
};

export type CheckoutComponentExitPopupAttibutesType = {
  enabled: boolean;
  type: 'image' | 'imageAndText' | 'video';
  actionOnClick: 'offer' | 'discount' | 'redirect' | 'close';
  offer: string;
  title: string;
  description: string;
  actionLabel: string;
  image: CheckoutImage;
  video: string;
  url: string;
  backgroundButtonColor: string;
  textButtonColor: string;
  coupon: string;
};

export enum CheckoutComponentNotificationType {
  INTERESTED_LAST_24_HOURS = 'interestedLast24Hours',
  INTERESTED_LAST_WEEK = 'interestedLastWeek',
  INTERESTED_RIGHT_NOW = 'interestedRightNow',
  PURCHASED_LAST_24_HOURS = 'purchasedLast24Hours',
  PURCHASED_LAST_WEEK = 'purchasedLastWeek'
}

export type CheckoutComponentNotificationAttibutesType = {
  enabled: boolean;
  [CheckoutComponentNotificationType.INTERESTED_LAST_24_HOURS]: {
    enabled: boolean;
    value: number;
  };
  [CheckoutComponentNotificationType.INTERESTED_LAST_WEEK]: {
    enabled: boolean;
    value: number;
  };
  [CheckoutComponentNotificationType.INTERESTED_RIGHT_NOW]: {
    enabled: boolean;
    value: number;
  };
  [CheckoutComponentNotificationType.PURCHASED_LAST_24_HOURS]: {
    enabled: boolean;
    value: number;
  };
  [CheckoutComponentNotificationType.PURCHASED_LAST_WEEK]: {
    enabled: boolean;
    value: number;
  };
};

export enum CheckoutComponentChatProvider {
  WHATSAPP = 'whatsapp',
  JIVOCHAT = 'jivochat',
  ZENDESK = 'zendesk',
  MANYCHAT = 'manychat',
  CRISP = 'crisp',
  TAWK = 'tawk',
  FACEBOOK = 'facebook',
  FRESHCHAT = 'freshchat',
  INTERCOM = 'intercom'
}

export type CheckoutComponentChatAttibutesType = {
  enabled: boolean;
  provider: CheckoutComponentChatProvider;
  accountId: string;
};

export type CheckoutComponentAttributesTypes =
  | CheckoutComponentTextAttibutesType
  | CheckoutComponentImageAttibutesType
  | CheckoutComponentAdvantageAttibutesType
  | CheckoutComponentSealAttibutesType
  | CheckoutComponentHeaderAttibutesType
  | CheckoutComponentListAttibutesType
  | CheckoutComponentCountdownAttibutesType
  | CheckoutComponentTestimonialAttibutesType
  | CheckoutComponentVideoAttibutesType
  | CheckoutComponentFacebookAttibutesType
  | CheckoutComponentMapAttibutesType
  | CheckoutComponentExitPopupAttibutesType
  | CheckoutComponentNotificationAttibutesType;

export type CheckoutColumnType = {
  id: string;
  type: string;
  components: CheckoutComponentType<CheckoutComponentAttributesTypes>[];
};

export type CheckoutRowType = {
  id: string;
  type: string;
  layout: number[];
  columns: CheckoutColumnType[];
};

type CheckoutConfigData = {
  settings: {
    background: {
      color: string;
      image: {
        preview: string;
      };
      fixed: boolean;
      repeat: boolean;
      cover: boolean;
    };
    text: {
      family: string;
      color?: {
        primary: string;
        secondary: string;
        active: string;
      };
    };
    form: {
      background: {
        color: string;
      };
    };
    icon: {
      color?: string;
    };
    paymentOptions: {
      button: {
        unselected: {
          text: {
            color: string;
          };
          background: {
            color: string;
          };
          icon: {
            color: string;
          };
        };
        selected: {
          text: {
            color: string;
          };
          background: {
            color: string;
          };
          icon: {
            color: string;
          };
        };
      };
    };
    payButton: {
      text: {
        text: string;
        color: string;
      };
      color: string;
    };
    box: {
      default: {
        header: {
          background: {
            color: string;
          };
          text: {
            color: {
              primary: string;
              secondary: string;
            };
          };
        };
        background: {
          color: string;
        };
        text: {
          color: {
            primary: string;
            secondary: string;
          };
        };
      };
      unselected: {
        header: {
          background: {
            color: string;
          };
          text: {
            color: {
              primary: string;
              secondary: string;
            };
          };
        };
        background: {
          color: string;
        };
        text: {
          color: {
            primary: string;
            secondary: string;
          };
        };
      };
      selected: {
        header: {
          background: {
            color: string;
          };
          text: {
            color: {
              primary: string;
              secondary: string;
            };
          };
        };
        background: {
          color: string;
        };
        text: {
          color: {
            primary: string;
            secondary: string;
          };
        };
      };
    };
  };
  extra: {
    exitPopup: CheckoutComponentType<CheckoutComponentExitPopupAttibutesType>;
    notification: CheckoutComponentType<CheckoutComponentNotificationAttibutesType>;
    chat: CheckoutComponentType<CheckoutComponentChatAttibutesType>;
  };
  rows: CheckoutRowType[];
};

export enum CheckoutDeviceType {
  MOBILE = 'mobile',
  DESKTOP = 'desktop'
}

export type CheckoutConfig = {
  [value in CheckoutDeviceType]: CheckoutConfigData;
};

