export type PixelStatus = 'active' | 'with_error' | 'inactive' | boolean;

export type MaybeInheritBool = boolean | 0 | 1 | '0' | '1' | null | undefined;
export type MaybeInheritNumber = number | '0' | null | undefined;

export type BasePixel = {
  id?: number | string;
  createdAt?: string;
  pixelId: string;
  name?: string | null;
  description?: string | null;
  status?: PixelStatus;
  error?: string | null;
};

export type FacebookPixel = BasePixel & {
  domain?: string | null;
  apiToken?: string | null;
  fbPixPurchaseTrigger?: MaybeInheritBool;
  fbPixConversionValue?: MaybeInheritNumber;
  fbBoletoPurchaseTrigger?: MaybeInheritBool;
  fbBoletoConversionValue?: MaybeInheritNumber;
  fbPicpayPurchaseTrigger?: MaybeInheritBool;
  fbPicpayConversionValue?: MaybeInheritNumber;
  fbNubankPurchaseTrigger?: MaybeInheritBool;
  fbNubankConversionValue?: MaybeInheritNumber;
};

export type GoogleAdsPixel = {
  id?: number | string;
  name: string;
  pixelId: string;
  conversionLabel: string | null;
  checkoutVisitTrigger?: boolean;
  cardPixApprovalTrigger?: boolean;
  boletoTrigger?: boolean;
  createdAt?: string;
};

export type TiktokPixel = BasePixel & {
  apiToken?: string | null;
  tiktokPixPurchaseTrigger?: MaybeInheritBool;
  tiktokPixConversionValue?: MaybeInheritNumber;
  tiktokBoletoPurchaseTrigger?: MaybeInheritBool;
  tiktokBoletoConversionValue?: MaybeInheritNumber;
  tiktokPicpayPurchaseTrigger?: MaybeInheritBool;
  tiktokPicpayConversionValue?: MaybeInheritNumber;
  tiktokNubankPurchaseTrigger?: MaybeInheritBool;
  tiktokNubankConversionValue?: MaybeInheritNumber;
};

export type KwaiPixel = BasePixel & {
  kwaiPixPurchaseTrigger?: MaybeInheritBool;
  kwaiPixConversionValue?: MaybeInheritNumber;
  kwaiBoletoPurchaseTrigger?: MaybeInheritBool;
  kwaiBoletoConversionValue?: MaybeInheritNumber;
  kwaiPicpayPurchaseTrigger?: MaybeInheritBool;
  kwaiPicpayConversionValue?: MaybeInheritNumber;
  kwaiNubankPurchaseTrigger?: MaybeInheritBool;
  kwaiNubankConversionValue?: MaybeInheritNumber;
};

export type TaboolaPixel = BasePixel & {
  eventName?: string;
  accountId?: string;
  checkoutVisitTrigger?: boolean;
  cardPixApprovalTrigger?: boolean;
  boletoTrigger?: boolean;
};

export type OutbrainPixel = BasePixel;

export type TrackingPixels = {
  fbPixPurchaseTrigger: boolean;
  fbPixConversionValue: number;
  fbBoletoPurchaseTrigger: boolean;
  fbBoletoConversionValue: number;
  fbPicpayPurchaseTrigger: boolean;
  fbPicpayConversionValue: number;
  fbNubankPurchaseTrigger: boolean;
  fbNubankConversionValue: number;

  tiktokPixPurchaseTrigger: boolean;
  tiktokPixConversionValue: number;
  tiktokBoletoPurchaseTrigger: boolean;
  tiktokBoletoConversionValue: number;
  tiktokPicpayPurchaseTrigger: boolean;
  tiktokPicpayConversionValue: number;
  tiktokNubankPurchaseTrigger: boolean;
  tiktokNubankConversionValue: number;

  kwaiPixPurchaseTrigger: boolean;
  kwaiPixConversionValue: number;
  kwaiBoletoPurchaseTrigger: boolean;
  kwaiBoletoConversionValue: number;
  kwaiPicpayPurchaseTrigger: boolean;
  kwaiPicpayConversionValue: number;
  kwaiNubankPurchaseTrigger: boolean;
  kwaiNubankConversionValue: number;

  facebook_pixels: FacebookPixel[];
  googleAnalyticsTrackingId: string | null;
  google_ads_pixels: GoogleAdsPixel[];
  taboola_pixels: TaboolaPixel[];
  outbrain_pixels: OutbrainPixel[];
  tiktok_pixels: TiktokPixel[];
  kwai_pixels: KwaiPixel[];
  pixel_domains: string[];
};

