export type PaymentProvider = "cakto" | "ebanx";

export type CountryCode = 
  | "BR" // Brasil
  | "MX" // México
  | "AR" // Argentina
  | "CL" // Chile
  | "CO" // Colômbia
  | "PE" // Peru
  | "US" // Estados Unidos
  | "OTHER"; // Fallback

export type DocumentType = 
  | "cpf" 
  | "cnpj" 
  | "rut" 
  | "dni" 
  | "cuit" 
  | "curp" 
  | "rfc" 
  | "cc" 
  | "ce" 
  | "nit" 
  | "ruc" 
  | "ssn";

export type LocalPaymentMethod =
  | "pix"
  | "pix_auto"
  | "boleto"
  | "spei"
  | "oxxo"
  | "rapipago"
  | "pagofacil"
  | "servipag"
  | "multicaja"
  | "pse"
  | "efecty"
  | "baloto"
  | "pagoefectivo"
  | "safetypay";

export type CountryConfig = {
  code: CountryCode;
  name: string;
  flag: string;
  language: "pt" | "es" | "en";
  currency: string;
  provider: PaymentProvider;
  paymentMethods: string[];
  documentTypes: DocumentType[];
  phoneFormat: string;
  zipCodeFormat: string;
  zipCodeLabel: string;
  documentLabel: string;
};

export type UserCountryData = {
  countryCode: CountryCode;
  detectedIp?: string;
  detectedAt?: string;
  isManualSelection?: boolean;
};

