import { CheckoutConfig } from './builder';
import { TrackingPixels } from './pixels';

export type OfferType = 'subscription' | 'unique';

export type BumpData = {
  id: string;
  product: string;
  offer: {
    id: string;
    name: string;
    price: number;
    type: OfferType;
    recurrence_period?: number;
  };
  cta: string;
  title: string;
  description: string;
  showImage: boolean;
  image: string;
  checked: boolean;
  installments: number;
  referencePrice: number | null;
  calculatedInstallments?: InstallmentsData[];
};

export type InstallmentsData = {
  installment: string;
  value: number;
};

export type InstallmentRequest = { offerId: string; total: number };

export type AffiliateCompanyStatus = 'approved' | 'blocked';

export type ProductData = {
  disable_orderbump_pixel_events: boolean;
  default: boolean;
  id: string;
  name: string;
  price: number;
  serviceFee?: number;
  checkout: {
    config: CheckoutConfig;
  };
  product: {
    short_id: string;
    name: string;
    price: number;
    description: string;
    invoiceDescription: string;
    calculatedInstallments: InstallmentsData[];
    paymentMethod: PaymentMethod;
    paymentMethods: PaymentMethodOption[];
    offers?: Array<{
      id: string;
      price: number;
    }>;
    upsellPage?: string;
    upsell?: boolean;
    redirectUpsellWithBumpFail?: boolean;
    bumps: BumpData[];
    status?: string;
    defaultPaymentMethod?: string;
    contentDelivery?: string;
    paymentsOrder: PaymentMethod[];
    image?: string;
    productPrice?: number;
    showCouponField: string;
    showAddressFields?: boolean;
    user?: {
      willBeCharged?: boolean;
    }
  };
  threeDsRetryEnabled?: boolean;
  threeDsEnabled?: boolean;
  sellerName: string;
  sellerEmail: string;
  tracking_pixels: TrackingPixels | null;
  paid?: boolean;
  pixelEventId: string;
  confirmEmail: boolean;
  affiliate?: {
    affiliateShortId: string;
    companyStatus: AffiliateCompanyStatus;
    cookieTime: number;
  };
  companyStatus: string;
  type: OfferType;
  recurrence_period?: number;
};

export type CustomerData = {
  name: string;
  email: string;
  phone: string;
  docType: 'cpf' | 'cnpj';
  docNumber: string;
  fingerprint?: string;
  address?: {
    zipCode: string;
    street: string;
    number: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
    freight?: string;
  };
};

export type CartItem = {
  id: string;
  offerType: string;
  installments?: number;
};

export type SessionMetadata = {
  ip: string;
  country: string;
  sessionid: string;
};

export type PaymentMethod =
  | 'pix'
  | 'pix_auto'
  | 'credit_card'
  | 'boleto'
  | 'googlepay'
  | 'applepay'
  | 'picpay'
  | 'openfinance_nubank'
  | 'oxxo'
  | 'spei'
  | 'threeDs';

export type ProductType = 'product' | 'subscription';

export type Order = {
  id: string;
  offerName: string;
  refId: string;
  offerId: string;
  status: PaymentStatus;
  offer_type?: string;
  amount: number;
  reason: string | null;
  paymentMethod: PaymentMethod;
  installments: number;
  createdAt: string;
};

export type PaymentPayload = {
  customer: CustomerData;
  address?: {
    zipCode: string;
    zipcode: string;
    street: string;
    number: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
    country: string;
    freight?: string;
  };
  paymentMethod: PaymentMethod;
  items: CartItem[];
  metadata: SessionMetadata;
  type: ProductType;
  deviceId?: string;
  installments?: number;
  saveCard?: boolean;
  affiliateShortId?: string;
  card?: {
    externalToken?: string | undefined;
    holderName: string | undefined;
    number: string | undefined;
    expMonth: string | undefined;
    expYear: string | undefined;
    cvv: string | undefined;
  };

  threeDSecure?: {
    Cavv: string | null | undefined;
    Xid: string | null | undefined;
    Eci: string | null | undefined;
    Version: string | null | undefined;
    ReferenceId: string | null | undefined;
    DataOnly: boolean;

    mpi?: string;
    transaction_id?: string | null;
    ds_transaction_id?: string | null;
  };
  checkoutUrl?: string;
  recaptchaToken?: string;
  captchaToken?: string;
  affiliateCookies?: Array<any>;
  refererUrl?: string;
  coupon?: string;
  antifraud_profiling_attempt_reference?: string;
  fbp?: string;
  fbc?: string;
};

export type PaymentStatus =
  | 'processing'
  | 'authorized'
  | 'paid'
  | 'refunded'
  | 'waiting_payment'
  | 'refused'
  | 'chargedback'
  | 'canceled'
  | 'in_protest'
  | 'partially_paid'
  | 'redo_payment';

type PaymentSuccessError = {
  id?: string;
  refId?: string;
  offerName?: string;
  reason?: string;
  price?: string;
  status?: string;
};

export type Payment = {
  id: string;
  refId: string;
  status: PaymentStatus;
  amount: number;
  reason?: string;
  paymentMethod: PaymentMethod;
  installments: number;
  createdAt: string;
  threeDs: boolean;
  nextStep: boolean;
  threeDsFinished: boolean;
  pix?: {
    qrCode: string;
    expirationDate: string;
  };
  pix_auto?: {
    qrCode: string;
    expirationDate: string;
    user_journey?: string;
  };
  card?: unknown;
  picpay?: {
    qrCode: string;
    paymentURL: string;
    expirationDate: string;
  };
  applepay?: {
    qrCode: string;
    paymentURL: string;
    expirationDate: string;
  };
  googlepay?: {
    qrCode: string;
    paymentURL: string;
    expirationDate: string;
  };
  boleto?: {
    boletoUrl: string;
    barcode: string;
    digitableLine: string;
    expirationDate: string;
    instructions: string;
  };
  oxxo?: {
    code: string;
    barcode: string;
    expirationDate: string;
  };
  spei?: {
    clabe: string;
    bank: string;
    reference: string;
    beneficiary: string;
  };
  refreshToken?: string;
  accessToken?: string;
  orders: Order[];
  success?: PaymentSuccessError[];
  errors?: PaymentSuccessError[];
  type?: string;
};

export type PayData = {
  payments: Payment[];
  totalAmount: number;
  refreshToken?: string;
  accessToken: string;
};

export type CreateAbandonmentPayload = {
  offerId: string;
  customerName?: string;
  customerEmail?: string;
  customerCpf?: string;
  customerCnpj?: string;
  customerCellphone?: string;
  checkoutUrl?: string;
};

export type CouponData = {
  code: string;
  discount: number;
  applyOnBumps: boolean;
  startTime: string;
  endTime?: string;
};

// Error type adapted from AxiosError - using generic error type instead
export type ErrorDetails = Error & {
  response?: {
    data?: {
      detail?: string;
    };
    status?: number;
  };
};

export type PaymentMethodOption = {
  type: PaymentMethod;
  name: string;
};

export type PaymentTab = {
  id: PaymentMethod;
  label: string;
  Icon: React.ElementType;
  Component: React.ElementType;
};

