"use client";

import { useState } from "react";
import { createPortal } from "react-dom";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";
import { getOppositeColor, applyTransparency } from "@/lib/utils/colors";
import { getBranding } from "@/lib/env";
import { LinkButton } from "@/components/common/link-button";

export function AboutCpf() {
	const [showModal, setShowModal] = useState(false);
	const { t } = useTranslation();

	return (
		<>
			<LinkButton
				onOpen={() => setShowModal(true)}
				spanClassName="-mt-1.5 -mb-3 text-xs leading-4 LinkButton text-[#36B37E]"
			>
				{t("form.helpers.cpf_required")}
			</LinkButton>

			{showModal &&
				typeof window !== "undefined" &&
				createPortal(
					<ModalContent onClose={() => setShowModal(false)} />,
					document.body,
				)}
		</>
	);
}

function ModalContent({ onClose }: { onClose: () => void }) {
	const settings = useSettings();
	const { t } = useTranslation();
	const isNommi = getBranding() === "nommi";

	return (
		<div
			style={{
				backgroundColor: applyTransparency(
					getOppositeColor(settings.backgroundColor),
					0.5,
				),
			}}
			className="fixed inset-0 z-50 flex items-center justify-center bg-opacity-50"
			onClick={onClose}
		>
			<div
				style={{
					backgroundColor: settings.form.background.color,
				}}
				className="bg-gray-800 rounded-lg shadow-lg p-6 max-w-md mx-4"
				onClick={(e) => e.stopPropagation()}
			>
				<div className="flex flex-col">
					<h2
						style={{
							color: settings.text.color.primary,
						}}
						className="text-lg font-semibold text-white mb-4"
					>
						{t("about.cpf_title")}
					</h2>
					<p
						style={{
							color: settings.text.color.secondary,
						}}
						className="text-sm text-gray-400 mb-6"
					>
						{t("about.cpf_description")}
					</p>
					<button
						onClick={onClose}
						style={{
							backgroundColor: isNommi ? "#2886B9" : settings.payButton.color,
							color: settings.payButton.text.color,
							opacity: 1,
						}}
						className="px-6 py-2 bg-[#0F7864] text-white rounded-md hover:bg-[#0b6856] hover:shadow-button-hover transition-colors w-full"
					>
						{t("common.close")}
					</button>
				</div>
			</div>
		</div>
	);
}

