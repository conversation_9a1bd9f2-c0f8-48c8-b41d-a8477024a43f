"use client";

import React, { useState } from "react";
import type { HTMLProps } from "react";
import { tv } from "tailwind-variants";
import { isNommi } from "@/lib/utils/brand";

const button = tv({
	base: `
    aling-center
    block
    duration-300
    disabled:opacity-60
    ease-in-out
    font-bold
    rounded-md
    shadow-sm
    text-white
    transition
    w-full
    hover:shadow-button-hover
  `,
	variants: {
		buttonColor: {
			green: "bg-green-500 hover:bg-green-600",
			indigo: "bg-indigo-500 hover:bg-indigo-600",
		},
		size: {
			sm: "px-3 py-2 text-sm",
			md: "px-4 py-3 text-base",
			lg: "px-6 py-4 text-lg",
		},
	},
});

type Props = Omit<HTMLProps<HTMLButtonElement>, "size"> & {
	size?: "sm" | "md" | "lg";
	type?: "button" | "submit" | "reset";
	loading?: boolean;
	backgroundColor?: string;
	textColor?: string;
	sizes?: "sm" | "md" | "lg"; // Alias para compatibilidade
};

const Button: React.FC<Props> = ({
	loading,
	size = "md",
	sizes,
	children,
	className,
	backgroundColor,
	textColor,
	type = "button",
	style,
	...props
}) => {
	const [isHovered, setIsHovered] = useState(false);

	const finalSize = sizes || size;
	const bgColor = backgroundColor || style?.backgroundColor || (isNommi ? "#2886B9" : "#0F7864");
	const txtColor = textColor || style?.color || "#fff";
	const hoverColor = isNommi ? "#1f6e94" : "#0b6856";

	return (
		<button
			type={type}
			style={{
				...style,
				backgroundColor: isHovered ? hoverColor : bgColor,
				color: txtColor,
			}}
			className={`${button({ size: finalSize })} ${className ?? ""}`}
			onMouseEnter={() => setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}
			{...props}
		>
			{loading ? (
				<div className="flex items-center justify-center">
					<svg
						className="animate-spin h-4 w-4"
						xmlns="http://www.w3.org/2000/svg"
						fill="none"
						viewBox="0 0 24 24"
					>
						<circle
							className="opacity-25"
							cx="12"
							cy="12"
							r="10"
							stroke="currentColor"
							strokeWidth="4"
						></circle>
						<path
							className="opacity-75"
							fill="currentColor"
							d="M4 12a8 8 0 018-8v8H4z"
						></path>
					</svg>
				</div>
			) : (
				children
			)}
		</button>
	);
};

export default Button;



