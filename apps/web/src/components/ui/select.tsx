"use client";

import { Controller, useFormContext } from "react-hook-form";
import { tv } from "tailwind-variants";
import useSettings from "@/hooks/useSettings";

const select = tv({
	base: `
  block
  border
  border-[#919EAB33]
  ease-in-out duration-150
  focus:ring-1
  outline-none
  rounded-md
  shadow-sm
  text-white
  transition
  w-full
  `,
	variants: {
		color: {
			primary: "focus:ring-white",
			error: "border-red-500 focus:ring-red-500 focus:border-red-500",
		},
		size: {
			sm: "px-2 py-2 text-sm",
			md: "px-2 py-3 text-base",
			lg: "px-3 py-4 text-lg",
		},
	},
	defaultVariants: {
		color: "primary",
		size: "md",
	},
});

type SelectProps = Omit<
	React.InputHTMLAttributes<HTMLSelectElement>,
	"size" | "color"
> & {
	bgColor?: string;
	name: string;
	label: string;
	color?: "primary";
	size?: "sm" | "md" | "lg";
	options: { label: string; value: string }[];
};

const Select: React.FC<SelectProps> = ({
	bgColor,
	color = "primary",
	name,
	size = "md",
	label,
	className,
	options,
	...props
}) => {
	const { control } = useFormContext();
	const { text, form } = useSettings();

	return (
		<Controller
			name={name}
			control={control}
			render={({ field, fieldState: { error } }) => (
				<div className={`flex flex-col w-full items-start gap-1 ${className}`}>
					<label
						style={{ color: text?.color?.primary }}
						className="text-slate-500 text-xs focus:ring-1 focus:ring-white"
					>
						{label}
					</label>
					<select
						className={`${select({ color: error ? "error" : color, size })}`}
						style={{
							color: text?.color.primary,
							backgroundColor: bgColor || form?.background.color,
						}}
						{...field}
						{...props}
					>
						{options.map((option) => (
							<option key={option.value} value={option.value}>
								{option.label}
							</option>
						))}
					</select>
					{error && <span className="text-red-500 text-xs">{error.message}</span>}
				</div>
			)}
		/>
	);
};

export default Select;




