"use client";

import { tv } from "tailwind-variants";

type AlertProps = {
	title: string;
	message: string;
	type: "error" | "success" | "warning";
};

const alert = tv({
	base: "border-l-4 p-4 w-full rounded",
	variants: {
		type: {
			error: "bg-red-100 border-red-500 text-red-700",
			success: "bg-green-100 border-green-500 text-green-700",
			warning: "bg-orange-100 border-orange-500 text-orange-700",
		},
	},
});

const Alert: React.FC<AlertProps> = ({ title, message, type = "error" }) => (
	<div className={alert({ type })} role="alert">
		<p className="font-bold">{title}</p>
		<p className="font-normal">{message}</p>
	</div>
);

export default Alert;

