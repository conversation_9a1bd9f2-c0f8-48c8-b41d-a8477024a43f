"use client";

import { tv } from "tailwind-variants";

const card = tv({
	base: `
  block
  p-5
  xs:p-3
  rounded-md
  shadow-lg
  w-full
  `,
	variants: {
		color: {
			primary: "bg-white",
			secondary: "bg-secondary-light bg-opacity-50",
		},
		outline: {
			true: "border border-slate-300",
		},
		shadow: {
			false: "shadow-none",
		},
	},
});

type Props = React.InputHTMLAttributes<HTMLDivElement> & {
	backgroundColor?: string;
	borderEffect?: string;
	children: React.ReactNode;
	color?: "primary" | "secondary";
	outline?: boolean;
	shadow?: boolean;
};

const Card: React.FC<Props> = ({
	backgroundColor = "#212B36",
	borderEffect = "",
	color,
	children,
	outline,
	shadow,
	style,
	...props
}) => {
	const borderWaveEffectClass =
		borderEffect === "border-wave-effect" ? "border-wave-effect" : "";

	return (
		<div
			className={`${card({ color, outline, shadow })} ${borderWaveEffectClass}`}
			style={{
				backgroundColor,
				...(borderEffect === "border-wave-effect" && {
					"--size": "6px",
					"--m": "0.5",
					"--p": "calc(var(--m) * var(--size))",
					"--R": "calc(var(--size) * sqrt(var(--m) * var(--m) + 1))",
					mask: `
            radial-gradient(var(--R) at 50% calc(100% - (var(--size) + var(--p))), #000 99%, #0000 101%)
              calc(50% - 2 * var(--size)) 0 / calc(4 * var(--size)) 100%,
            radial-gradient(var(--R) at 50% calc(100% + var(--p)), #0000 99%, #000 101%) 50%
              calc(100% - var(--size)) / calc(4 * var(--size)) 100% repeat-x
          `,
					height: "calc(100% - 20px)",
				}),
				...style,
			}}
			{...props}
		>
			{children}
		</div>
	);
};

export default Card;




