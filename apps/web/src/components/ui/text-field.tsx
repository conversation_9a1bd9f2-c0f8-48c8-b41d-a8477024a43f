"use client";

import useSettings from "@/hooks/useSettings";
import { AlertIcon } from "@/components/icons";
import React, { useEffect } from "react";
import type { ClipboardEvent, KeyboardEvent } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { useSearchParams } from "next/navigation";
import { tv } from "tailwind-variants";
import { applyMask } from "@/lib/utils/mask";

const textfield = tv({
	base: `
  block
  border
  border-[#919EAB33]
  ease-in-out duration-150
  focus:ring-1
  outline-none
  rounded-md
  shadow-sm
  transition
  w-full
  `,
	variants: {
		color: {
			primary: "focus:ring-white",
			error: "border-red-500 focus:ring-red-500 focus:border-red-500",
		},
		size: {
			sm: "px-3 py-2 text-sm",
			md: "px-4 py-3 text-base",
			lg: "px-6 py-4 text-lg",
		},
		paddingLeft: {
			none: "unset",
			withLeftIcon: "pl-[38px]",
		},
	},
	defaultVariants: {
		color: "primary",
		size: "md",
		paddingLeft: "none",
	},
});

type TextFieldProps = Omit<
	React.InputHTMLAttributes<HTMLInputElement>,
	"size" | "color"
> & {
	name: string;
	label: string;
	color?: "primary" | "error";
	size?: "sm" | "md" | "lg";
	mask?: string;
	maskChar?: string;
	normalize?: boolean;
	isEmail?: boolean;
	loading?: boolean;
	appendPlaceholder?: string;
	actions?: any;
	startIcon?: any;
	hideErrorIcon?: boolean;
	onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
	onKeyDown?: (event: KeyboardEvent<HTMLInputElement>) => void;
	onClipboardChange?: (event: ClipboardEvent<HTMLInputElement>) => void;
	cpfCnpj?: boolean;
	icon?: React.ReactNode;
	iconPosition?: "left" | "right";
};

function formatCpfCnpj(digits: string) {
	const d = digits.replace(/\D/g, "").slice(0, 14);
	if (d.length <= 11) {
		return d
			.replace(/^(\d{3})(\d)/, "$1.$2")
			.replace(/^(\d{3})\.(\d{3})(\d)/, "$1.$2.$3")
			.replace(/^(\d{3})\.(\d{3})\.(\d{3})(\d)/, "$1.$2.$3-$4");
	}
	return d
		.replace(/^(\d{2})(\d)/, "$1.$2")
		.replace(/^(\d{2})\.(\d{3})(\d)/, "$1.$2.$3")
		.replace(/^(\d{2})\.(\d{3})\.(\d{3})(\d)/, "$1.$2.$3/$4")
		.replace(/^(\d{2})\.(\d{3})\.(\d{3})\/(\d{4})(\d)/, "$1.$2.$3/$4-$5");
}

const TextField = React.forwardRef<HTMLInputElement, TextFieldProps>(
	(
		{
			color = "primary",
			mask = "",
			maskChar = "_",
			name,
			size = "md",
			label,
			className,
			normalize,
			isEmail,
			loading,
			startIcon = false,
			hideErrorIcon = false,
			actions = null,
			onChange,
			onKeyDown,
			onClipboardChange,
			cpfCnpj,
			icon,
			iconPosition = "left",
			...props
		},
		forwardedRef,
	) => {
		const { text, form } = useSettings();
		const { control, setValue } = useFormContext();
		const searchParams = useSearchParams();

		const param = searchParams.get(name);

		const transformText = (
			textVal: string,
			norm?: boolean,
			email?: boolean,
		) => {
			let pattern;
			if (email) {
				pattern = /[^a-zA-Z0-9À-ÖØ-öø-ÿÇç\s.@_-]/g;
			} else {
				pattern = /[^a-zA-Z0-9À-ÖØ-öø-ÿÇç\s]/g;
			}
			if (!norm) return textVal;
			const transformedText = textVal.replace(pattern, "");
			return transformedText.replace(
				/[\p{Emoji_Presentation}\p{Extended_Pictographic}\u200D]/gu,
				"",
			);
		};

		useEffect(() => {
			const excludeFields = ["cardNumber", "cardCvv"];
			if (param && !excludeFields.includes(name)) {
				setTimeout(() => {
					if (cpfCnpj) {
						const onlyDigits = param.replace(/\D/g, "").slice(0, 14);
						setValue(name, formatCpfCnpj(onlyDigits));
					} else {
						setValue(name, param);
					}
				}, 100);
			}
		}, [name, param, setValue, cpfCnpj]);

		return (
			<Controller
				name={name}
				control={control}
				render={({ field, fieldState: { error } }) => (
					<div className={`flex flex-col w-full items-start gap-1 ${className}`}>
						<label
							style={{ color: text?.color?.secondary }}
							className="text-slate-500 text-xs"
							htmlFor={name}
						>
							{label}
						</label>
					<div className="relative w-full">
						{startIcon}
						{/* Icon on the left */}
						{icon && iconPosition === "left" && (
							<div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
								{icon}
							</div>
						)}
						<input
							id={name}
							ref={(ref) => {
								if (typeof field.ref === "function") {
									field.ref(ref);
								} else if (field.ref) {
									(field.ref as any).current = ref;
								}
								if (typeof forwardedRef === "function") {
									forwardedRef(ref);
								} else if (forwardedRef) {
									(forwardedRef as any).current = ref;
								}
							}}
							value={field.value || ""}
							onPaste={onClipboardChange}
							onKeyDown={onKeyDown}
							onChange={(e) => {
								if (cpfCnpj) {
									const digits = e.target.value.replace(/\D/g, "").slice(0, 14);
									const formatted = formatCpfCnpj(digits);
									field.onChange(formatted);
									if (onChange) onChange(e);
									return;
								}
								
								let transformedValue = transformText(
									e.target.value,
									normalize || false,
									isEmail || false,
								);
								
								// Apply mask if provided
								if (mask && !cpfCnpj) {
									transformedValue = applyMask(transformedValue, mask, maskChar);
								}
								
								field.onChange(transformedValue);
								if (onChange) onChange(e);
							}}
							style={{
								color: text?.color?.primary,
								backgroundColor: form?.background?.color,
								paddingLeft: icon && iconPosition === "left" ? "2.5rem" : undefined,
								paddingRight: icon && iconPosition === "right" ? "2.5rem" : undefined,
							}}
							className={textfield({
								color: error && "error",
								size,
								paddingLeft: startIcon ? "withLeftIcon" : "none",
								className: props["disabled"] ? "cursor-not-allowed" : "",
							})}
							placeholder={props.placeholder}
							disabled={props.disabled}
							autoComplete={props.autoComplete}
							inputMode={cpfCnpj ? "numeric" : props.inputMode}
							type={props.type}
						/>
						{/* Icon on the right */}
						{icon && iconPosition === "right" && (
							<div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
								{icon}
							</div>
						)}
						{loading && (
							<div
								className={`${error ? "right-5" : "right-0"} absolute inset-y-0 pr-3 flex items-center pointer-events-none`}
							>
								<svg
									className="animate-spin h-5 w-5 text-gray-500"
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
								>
									<circle
										className="opacity-25"
										cx="12"
										cy="12"
										r="10"
										stroke="currentColor"
										strokeWidth="4"
									></circle>
									<path
										className="opacity-75"
										fill="currentColor"
										d="M4 12a8 8 0 018-8v8H4z"
									></path>
								</svg>
							</div>
						)}
						{error && !hideErrorIcon && (
							<AlertIcon className="absolute transform -translate-y-1/2 right-2 top-1/2 w-5 h-5 text-red-500" />
						)}
						{actions}
						{props?.appendPlaceholder && (
							<span
								style={{ color: "rgba(56, 202, 79, 1)" }}
								className="absolute top-2 right-2"
							>
								{props.appendPlaceholder}
							</span>
						)}
					</div>
						{error && <span className="text-red-500 text-xs">{error.message}</span>}
					</div>
				)}
			/>
		);
	},
);

TextField.displayName = "TextField";

export default TextField;

