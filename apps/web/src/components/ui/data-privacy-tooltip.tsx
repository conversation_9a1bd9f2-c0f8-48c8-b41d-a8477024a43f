"use client";

import { useState } from "react";
import { QuestionMarkCircleIcon } from "@heroicons/react/24/outline";
import * as Popover from "@radix-ui/react-popover";
import { useTranslation } from "@/hooks/useTranslation";
import useSettings from "@/hooks/useSettings";

interface DataPrivacyTooltipProps {
	field: "phone" | "document" | "email";
}

export function DataPrivacyTooltip({ field }: DataPrivacyTooltipProps) {
	const [open, setOpen] = useState(false);
	const { t } = useTranslation();
	const settings = useSettings();

	return (
		<Popover.Root open={open} onOpenChange={setOpen}>
			<Popover.Trigger asChild>
				<button
					type="button"
					className="inline-flex items-center gap-1 text-sm hover:opacity-80 transition-opacity"
					style={{ color: settings.text?.color.primary }}
				>
					<QuestionMarkCircleIcon className="w-4 h-4" />
					<span>{t("data_privacy.why_we_ask")}</span>
				</button>
			</Popover.Trigger>

			<Popover.Portal>
				<Popover.Content
					className="rounded-lg shadow-lg border p-4 w-80 z-50"
					style={{
						backgroundColor: settings.form?.background?.color || "#fff",
						borderColor: settings.box?.default?.background?.color || "#e5e7eb",
					}}
					sideOffset={5}
				>
					<h4
						className="font-semibold mb-2"
						style={{ color: settings.text?.color.primary }}
					>
						{t(`data_privacy.${field}.title`)}
					</h4>
					<p
						className="text-sm"
						style={{ color: settings.text?.color.secondary }}
					>
						{t(`data_privacy.${field}.content`)}
					</p>

					<Popover.Arrow
						className="fill-current"
						style={{ color: settings.form?.background?.color || "#fff" }}
					/>
				</Popover.Content>
			</Popover.Portal>
		</Popover.Root>
	);
}

