import type { SVGProps } from "react";

const CircleCheckIcon = ({ strokeWidth = 1.8, ...props }: SVGProps<SVGSVGElement>) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 24 24"
		fill="none"
		stroke="currentColor"
		strokeWidth={strokeWidth}
		strokeLinecap="round"
		strokeLinejoin="round"
		{...props}
	>
		<path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10Z" />
		<path d="m9 12 2 2 4-4" />
	</svg>
);

export default CircleCheckIcon;


