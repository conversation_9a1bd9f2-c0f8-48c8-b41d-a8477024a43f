"use client";

import type { SVGProps } from 'react';

const NubankIcon: React.FC<SVGProps<SVGSVGElement>> = (props) => (
	<svg
		width="25"
		height="24"
		viewBox="0 0 500 275.9"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		{...props}
	>
		<path
			d="M90.3 26.3C106.1 9.7 127 0 151.7 0c48 0 79.8 35 85.9 87.5 2 17.1 2 41.1 1.9 68.9v113h-66v-77.1s-.1-66-.5-78.4c-1.8-53.7-33.6-87.5-82.7-87.6-14.9 15.8-22.8 34.9-24 64.2-.2 4.1-.1 18.6-.1 37.6 0 9.9.1 21 .1 32.6v108.8H.3V150.8c0-4.1-.1-8.2-.1-12.3-.1-8.3-.3-16.6.1-24.9.7-13.8 3.1-27.4 9.6-40 14.7-28.8 44.9-47.4 77-47.4 1.1 0 2.2.1 3.4.1z"
			fill={props.fill || '#820AD1'}
		/>
		<path
			d="M499.7 162.3c.4-8.3.3-16.6.1-24.9-.1-4.1-.1-8.2-.1-12.3V6.4h-66v108.8c0 11.6 0 22.7.1 32.6.1 19.1.1 33.6-.1 37.6-1.2 29.3-9.1 48.4-23.9 64.1-49.1 0-81-33.8-82.7-87.5-.4-12.3-.6-43-.6-78.4V6.3l-66 .1v113c0 27.8 0 51.9 1.9 68.9 6.1 52.5 37.9 87.5 85.9 87.5 24.6 0 45.6-9.7 61.4-26.3 1.1 0 2.2.1 3.4.1 32.2 0 62.3-18.6 77-47.4 6.5-12.6 8.9-26.1 9.6-39.9z"
			fill={props.fill || '#820AD1'}
		/>
	</svg>
);

export default NubankIcon;

