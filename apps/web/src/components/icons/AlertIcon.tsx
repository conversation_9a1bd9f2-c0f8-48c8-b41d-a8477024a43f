"use client";

import type { SVGProps } from "react";

const AlertIcon: React.FC<SVGProps<SVGSVGElement>> = (props) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width="24"
		height="24"
		viewBox="0 0 24 24"
		{...props}
	>
		<g id="evaAlertCircleFill0">
			<g id="evaAlertCircleFill1">
				<path
					id="evaAlertCircleFill2"
					fill="currentColor"
					d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2Zm0 15a1 1 0 1 1 1-1a1 1 0 0 1-1 1Zm1-4a1 1 0 0 1-2 0V8a1 1 0 0 1 2 0Z"
				/>
			</g>
		</g>
	</svg>
);

export default AlertIcon;

