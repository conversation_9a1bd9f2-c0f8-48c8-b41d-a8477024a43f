"use client";

import type { SVGProps } from 'react';

const GooglePayIcon: React.FC<SVGProps<SVGSVGElement>> = (props) => (
	<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
		<path
			d="M21.9307 10.1977C22.0474 10.8689 22.1055 11.5489 22.1044 12.2302C22.1044 15.2726 21.0169 17.845 19.1245 19.5862H19.127C17.4721 21.1149 15.1971 21.9999 12.4997 21.9999C9.84761 21.9999 7.30415 20.9463 5.42884 19.071C3.55354 17.1957 2.5 14.6523 2.5 12.0002C2.5 9.34809 3.55354 6.80463 5.42884 4.92932C7.30415 3.05401 9.84761 2.00048 12.4997 2.00048C14.9818 1.97283 17.3786 2.90522 19.1895 4.6029L16.3346 7.45781C15.3024 6.47441 13.9252 5.93575 12.4997 5.95786C9.89103 5.95786 7.67484 7.7178 6.88487 10.0877C6.46675 11.3297 6.46675 12.6744 6.88487 13.9164H6.88862C7.68234 16.2825 9.89478 18.0425 12.5035 18.0425C13.8509 18.0425 15.0084 17.6975 15.9058 17.0875H15.9021C16.4233 16.7424 16.8692 16.2952 17.2127 15.7729C17.5562 15.2506 17.7903 14.6641 17.9008 14.0489H12.4997V10.199L21.9307 10.1977Z"
			fill={props.fill || 'currentColor'}
		/>
	</svg>
);

export default GooglePayIcon;

