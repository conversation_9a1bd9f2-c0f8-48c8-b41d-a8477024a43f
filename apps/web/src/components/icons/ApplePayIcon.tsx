"use client";

import type { SVGProps } from "react";

const ApplePayIcon: React.FC<SVGProps<SVGSVGElement>> = (props) => (
	<svg
		fill={props.fill || "#000"}
		width="32"
		height="32"
		viewBox="0 0 32 32"
		xmlns="http://www.w3.org/2000/svg"
		{...props}
	>
		<path d="M16.789 13.911c0 0.956-0.583 1.506-1.611 1.506h-1.35v-3.011h1.356c1.022 0 1.605 0.544 1.605 1.505zM19.428 17.389c0 0.461 0.4 0.761 1.028 0.761 0.8 0 1.4-0.505 1.4-1.217v-0.428l-1.306 0.083c-0.739 0.050-1.122 0.322-1.122 0.8zM32 6.167v19.556c0 1.472-1.194 2.667-2.667 2.667h-26.667c-1.472 0-2.667-1.194-2.667-2.667v-19.556c0-1.472 1.194-2.667 2.667-2.667h26.667c1.472 0 2.667 1.194 2.667 2.667zM7.1 12.733c0.467 0.039 0.933-0.233 1.228-0.578 0.289-0.356 0.478-0.833 0.428-1.317-0.411 0.017-0.922 0.272-1.217 0.628-0.267 0.306-0.494 0.8-0.439 1.267zM10.467 16.872c-0.011-0.011-1.089-0.422-1.1-1.667-0.011-1.039 0.85-1.539 0.889-1.567-0.489-0.722-1.245-0.8-1.506-0.817-0.678-0.039-1.256 0.383-1.578 0.383-0.328 0-0.817-0.367-1.35-0.355-0.694 0.011-1.344 0.406-1.694 1.033-0.728 1.256-0.189 3.111 0.517 4.133 0.344 0.505 0.761 1.061 1.306 1.039 0.516-0.022 0.722-0.333 1.344-0.333 0.628 0 0.806 0.333 1.35 0.328 0.567-0.011 0.917-0.505 1.267-1.011 0.383-0.578 0.544-1.133 0.556-1.167zM17.989 13.906c0-1.478-1.028-2.489-2.495-2.489h-2.844v7.578h1.178v-2.589h1.628c1.489 0 2.533-1.022 2.533-2.5zM22.989 15.222c0-1.094-0.878-1.8-2.222-1.8-1.25 0-2.172 0.717-2.206 1.694h1.061c0.089-0.467 0.522-0.772 1.111-0.772 0.722 0 1.122 0.333 1.122 0.956v0.417l-1.467 0.089c-1.367 0.083-2.105 0.645-2.105 1.617 0 0.983 0.761 1.633 1.855 1.633 0.739 0 1.422-0.372 1.733-0.967h0.022v0.911h1.089v-3.778zM28.667 13.494h-1.194l-1.383 4.478h-0.022l-1.383-4.478h-1.239l1.994 5.517-0.105 0.333c-0.178 0.567-0.472 0.789-0.994 0.789-0.095 0-0.272-0.011-0.345-0.017v0.911c0.067 0.022 0.361 0.028 0.45 0.028 1.15 0 1.689-0.439 2.161-1.767z" />
	</svg>
);

export default ApplePayIcon;

