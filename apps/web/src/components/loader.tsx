"use client";

import { Loader2 } from "lucide-react";

import { useTranslation } from "@/hooks/useTranslation";

type LoaderProps = {
	messageKey?: string;
};

export function Loader({ messageKey = "checkout.loading" }: LoaderProps) {
	const { t } = useTranslation();
	const translated = t(messageKey);
	const message =
		translated === messageKey ? t("common.loading") : translated;

	return (
		<div className="flex h-full flex-col items-center justify-center gap-2 py-10 text-center text-muted-foreground">
			<Loader2 className="size-6 animate-spin" />
			<p className="text-sm font-medium">{message}</p>
		</div>
	);
}

export default Loader;
