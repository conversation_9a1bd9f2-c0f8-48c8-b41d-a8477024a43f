"use client";

import { XMarkIcon, ArrowTopRightOnSquareIcon } from "@heroicons/react/20/solid";
import { useInAppBrowser } from "@/hooks/useInAppBrowser";
import { useTranslation } from "@/hooks/useTranslation";

export function InAppBrowserWarning() {
	const { showWarning, dismissWarning } = useInAppBrowser();
	const { t } = useTranslation();

	if (!showWarning) return null;

	const handleOpenInBrowser = () => {
		if (typeof window === "undefined") return;

		// Try to open in external browser
		// For iOS Safari:
		window.location.href = window.location.href.replace(
			/^https?:/,
			"x-safari-https:",
		);

		// Fallback: copy URL
		setTimeout(() => {
			if (navigator.clipboard) {
				navigator.clipboard.writeText(window.location.href);
				alert(t("in_app_browser.url_copied") || "URL copiada! Cole no navegador para continuar.");
			}
		}, 1000);
	};

	return (
		<div className="bg-yellow-50 border-b border-yellow-200 px-4 py-3">
			<div className="flex items-start gap-3 max-w-screen-lg mx-auto">
				<div className="flex-shrink-0 text-yellow-600">
					<ArrowTopRightOnSquareIcon className="w-5 h-5" />
				</div>
				<div className="flex-1 min-w-0">
					<p className="text-sm font-medium text-yellow-800">
						{t("in_app_browser.warning_title")}
					</p>
					<p className="text-sm text-yellow-700 mt-1">
						{t("in_app_browser.warning_message")}
					</p>
					<button
						onClick={handleOpenInBrowser}
						className="mt-2 text-sm font-medium text-yellow-800 underline hover:no-underline"
					>
						{t("in_app_browser.open_in_browser")} →
					</button>
				</div>
				<button
					onClick={dismissWarning}
					className="flex-shrink-0 text-yellow-600 hover:text-yellow-800"
				>
					<XMarkIcon className="w-5 h-5" />
				</button>
			</div>
		</div>
	);
}

