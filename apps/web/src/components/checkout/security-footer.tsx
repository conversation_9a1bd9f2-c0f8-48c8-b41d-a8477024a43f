"use client";

import Image from "next/image";
import { ShieldCheckIcon } from "@heroicons/react/24/solid";
import { useCheckout } from "@/contexts";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";
import { getLabel } from "@/lib/utils/brand";

export function SecurityFooter() {
	const { offer } = useCheckout();
	const settings = useSettings();
	const { t } = useTranslation();

	// Usar valores padrão se offer não estiver carregado ainda
	const sellerName = offer?.sellerName || t("security.vendor");
	const brandName = getLabel.brandName();

	return (
		<footer className="mt-8 space-y-6 text-center">
			{/* Logo e Seller Info */}
			<div className="space-y-3">
				{/* Logo Cakto */}
				<div className="flex justify-center mb-2">
					<Image
						src="/assets/cakto-logo.svg"
						alt="Cakto"
						width={100}
						height={32}
						className="h-8 w-auto"
						onError={(e) => {
							// Fallback se logo não existir
							(e.target as HTMLImageElement).style.display = "none";
						}}
					/>
				</div>

				{/* Seller Info */}
				<p
					className="text-sm"
					style={{ color: settings.text?.color.secondary }}
				>
					{t("security.processing_payment_for", {
						brand: brandName,
						seller: sellerName,
					})}
				</p>
			</div>

			{/* Security Badge */}
			<div
				className="flex items-center justify-center gap-2 py-3 px-4 rounded-lg mx-auto max-w-xs"
				style={{
					backgroundColor: settings.box?.selected?.background?.color || "#f0fdf4",
					borderColor: settings.box?.selected?.header?.background?.color || "#22c55e",
					borderWidth: "1px",
				}}
			>
				<ShieldCheckIcon
					className="w-5 h-5"
					style={{ color: settings.text?.color?.active || "#22c55e" }}
				/>
				<span
					className="text-sm font-medium"
					style={{ color: settings.text?.color?.active || "#22c55e" }}
				>
					{t("security.secure_purchase")}
				</span>
			</div>

			{/* Legal Links */}
			<div className="space-y-2">
				<p
					className="text-xs"
					style={{ color: settings.text?.color.secondary }}
				>
					{t("security.recaptcha_notice")}{" "}
					<a
						href="https://policies.google.com/privacy"
						target="_blank"
						rel="noopener noreferrer"
						className="underline hover:opacity-80 transition-opacity"
						style={{ color: settings.text?.color.primary }}
					>
						{t("security.privacy_policy")}
					</a>{" "}
					{t("common.and")}{" "}
					<a
						href="https://policies.google.com/terms"
						target="_blank"
						rel="noopener noreferrer"
						className="underline hover:opacity-80 transition-opacity"
						style={{ color: settings.text?.color.primary }}
					>
						{t("security.terms_of_service")}
					</a>{" "}
					{t("security.from_google")}
				</p>

				<div
					className="flex flex-col items-center gap-1 text-xs"
					style={{ color: settings.text?.color.secondary }}
				>
					<a
						href={`https://www.${brandName.toLowerCase()}.com.br/politica-de-privacidade`}
						target="_blank"
						rel="noopener noreferrer"
						className="hover:opacity-80 transition-opacity"
					>
						{t("security.privacy_policy")}
					</a>
					<a
						href={`https://www.${brandName.toLowerCase()}.com.br/termos-e-condicoes`}
						target="_blank"
						rel="noopener noreferrer"
						className="hover:opacity-80 transition-opacity"
					>
						{t("security.terms_of_service")}
					</a>
					<a
						href={`https://www.${brandName.toLowerCase()}.com.br/termo-de-compra-e-venda`}
						target="_blank"
						rel="noopener noreferrer"
						className="hover:opacity-80 transition-opacity"
					>
						{t("security.purchase_terms")}
					</a>
					<p className="opacity-70">{t("security.installment_with_interest")}</p>
				</div>
			</div>
		</footer>
	);
}

