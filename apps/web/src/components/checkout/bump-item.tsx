"use client";

import { useEffect, useMemo, useState } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { useCheckout } from "@/contexts";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";
import { useCouponDiscount } from "@/hooks/useCouponDiscount";
import { CircleCheckIcon } from "@/components/icons";
import { InstallmentsSelector } from "@/components/payments/InstallmentsSelector";
import { formatPrice } from "@/lib/utils/format";
import { getRecurrencePeriodLabel } from "@/lib/utils/payment";

type Props = {
	index: number;
	defaultChecked?: boolean;
};

export function BumpItem({ index, defaultChecked = false }: Props) {
	const [isChecked, setIsChecked] = useState(defaultChecked);
	const { setValue } = useFormContext();
	const settings = useSettings();
	const { t } = useTranslation();
	const { canBePaidInInstallments, hasAnySubscription } = useCheckout();
	const { applyCoupon } = useCouponDiscount();
	const form = useFormContext();

	const bump = useWatch({
		control: form.control,
		name: `bumps.${index}`,
	});

	const originalPrice = bump?.offer?.price || 0;
	const finalPrice = applyCoupon(originalPrice, true);

	const discountPercentage = useMemo(() => {
		if (bump?.referencePrice) {
			return ((bump.referencePrice - originalPrice) / bump.referencePrice) * 100;
		}
		return 0;
	}, [bump?.referencePrice, originalPrice]);

	// useEffect ANTES da validação (regras dos hooks)
	useEffect(() => {
		if (!canBePaidInInstallments) {
			setValue(`bumps.${index}.installments`, 1);
		}
	}, [canBePaidInInstallments, index, setValue]);

	// Validação: retorna null DEPOIS de todos os hooks
	if (!bump || !bump.offer) {
		return null;
	}

	const handleCheckedBump = () => {
		const newCheckedValue = !isChecked;
		setIsChecked(newCheckedValue);
		setValue(`bumps.${index}.checked`, newCheckedValue);
	};

	const getFormattedFinalPrice = () => {
		if (bump.offer.type === "subscription") {
			return `${formatPrice(finalPrice)} / ${getRecurrencePeriodLabel(
				bump.offer.recurrence_period || 30,
			)}`;
		}
		return formatPrice(finalPrice);
	};

	return (
		<div
			style={{
				backgroundColor: isChecked
					? settings.box.selected.background.color
					: settings.box.unselected.background.color,
				border: `2px ${isChecked ? "solid" : "dashed"} ${
					isChecked
						? settings.box.selected.header.background.color
						: settings.box.unselected.header.background.color
				}`,
			}}
			className="cursor-pointer ease-in-out duration-150 my-1 overflow-hidden rounded-md shadow-md transition w-full"
			onClick={handleCheckedBump}
		>
			{/* Header */}
			<div
				style={{
					backgroundColor: isChecked
						? (settings.box.selected.header.background.color as string)
						: (settings.box.unselected.header.background.color as string),
				}}
				className="ease-in-out flex justify-between py-2 px-4 transition"
			>
				<div className="flex-grow">
					<h1
						style={{
							color: isChecked
								? settings.box.selected.header.text.color.primary
								: settings.box.unselected.header.text.color.primary,
						}}
						className="text-lg xs:text-sm text-white font-semibold line-clamp-3 overflow-hidden text-ellipsis"
					>
						{bump.cta.trim()}
					</h1>
				</div>
				<div className="flex items-center shrink-0 basis-auto">
					{isChecked && (
						<span
							style={{
								color: isChecked
									? settings.box.selected.header.text.color.primary
									: settings.box.unselected.header.text.color.primary,
							}}
							className="text-white mr-1 xs:hidden"
						>
							{t("bump.selected")}
						</span>
					)}
					<CircleCheckIcon
						className={`h-4 w-4 ${isChecked ? "text-[#38C4AF]" : "text-slate-500"}`}
					/>
				</div>
			</div>

			{/* Body */}
			<div className="flex p-4 xs:p-3">
				<div className="flex flex-row gap-2 items-center w-full xs:flex-col">
					<div className="flex gap-2 w-full">
						{/* Image */}
						{bump.image && bump.showImage && (
							<div className="w-[80px] h-[80px] xs:w-[80px] xs:h-[80px] flex-shrink-0">
								<img
									src={bump.image}
									alt={bump.title || "Imagem do produto"}
									className="w-full h-full rounded-md object-cover"
								/>
							</div>
						)}

						{/* Content */}
						<div className="flex flex-col justify-center w-full gap-1">
							<div className="flex justify-between">
								<div className="flex-1">
									<p
										style={{
											color: isChecked
												? settings.box.selected.text.color.primary
												: settings.box.unselected.text.color.primary,
										}}
										className="font-bold text-white text-sm line-clamp-3 overflow-hidden text-ellipsis"
									>
										{bump.title.trim() || "Nome do seu produto"}
									</p>
								</div>
								<div className="min-w-[100px] flex justify-center items-center gap-2 xs:flex-col-reverse">
									{bump.referencePrice !== null && (
										<>
											<span
												style={{
													color: isChecked
														? settings.box.selected.text.color.secondary
														: settings.box.unselected.text.color.secondary,
												}}
												className="text-gray-400 line-through mr-1 xs:text-xs"
											>
												{formatPrice(bump.referencePrice)}
											</span>
											<span
												style={{
													color: settings.box.selected.header.text.color.primary,
												}}
												className="bg-[#0F7864] text-white text-xs font-bold px-2 py-1 rounded-full xs:px-1 xs:font-semibold"
											>
												{Math.round(discountPercentage)}% {t("bump.discount_label")}
											</span>
										</>
									)}
								</div>
							</div>

							<div className="flex flex-col sm:flex-row gap-2">
								<div className="flex-1">
									<p
										style={{
											color: isChecked
												? settings.box.selected.text.color.secondary
												: settings.box.unselected.text.color.secondary,
										}}
										className="text-white text-sm overflow-hidden"
									>
										{bump.description.trim() || "Nome do seu produto"}
									</p>
								</div>
								<div className="min-w-[100px] relative sm:text-right">
									{hasAnySubscription && canBePaidInInstallments ? (
										<InstallmentsSelector
											itsNew={false}
											offerPaid={false}
											hideLabel
											fieldName={`bumps.${index}.installments`}
											calculatedInstallmentsFieldName={`bumps.${index}.calculatedInstallments`}
											offerId={bump.offer.id}
											offerType={bump.offer.type}
											price={finalPrice}
											recurrencePeriod={bump.offer.recurrence_period || 30}
											autoCalc={false}
										/>
									) : (
										<span
											style={{
												color: settings.text.color.active,
											}}
											className="text-base font-bold px-2 py-1 rounded-full xs:px-0 xs:font-semibold"
										>
											{getFormattedFinalPrice()}
										</span>
									)}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Footer */}
			<div
				style={{
					width: "100%",
					height: 30,
					background: isChecked
						? (settings.box.selected.header.background.color as string)
						: (settings.box.unselected.header.background.color as string),
					display: "flex",
					alignItems: "center",
					padding: "0 10px",
					cursor: "pointer",
				}}
				onClick={(e) => {
					e.stopPropagation();
					setIsChecked(!isChecked);
				}}
			>
				<input
					type="checkbox"
					checked={isChecked}
					onChange={() => setIsChecked(!isChecked)}
					onClick={(e) => e.stopPropagation()}
					style={{
						marginRight: "8px",
						cursor: "pointer",
						accentColor: isChecked ? "#38C4AF" : "auto",
					}}
				/>
				<span
					style={{
						userSelect: "none",
						fontSize: "14px",
						color: isChecked
							? settings.box.selected.header.text.color.primary
							: settings.box.unselected.header.text.color.primary,
						fontWeight: "bold",
					}}
				>
					{t("bump.add_product")}
				</span>
			</div>
		</div>
	);
}

