"use client";

import Image from "next/image";
import { formatPrice } from "@/lib/utils/format";
import { useCheckout } from "@/contexts";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";
import { useMemo } from "react";

export function ProductInfoCard() {
	const { offer } = useCheckout();
	const settings = useSettings();
	const { t } = useTranslation();

	const productInfo = useMemo(() => {
		if (!offer || !offer.product) return null;

		const product = offer.product;
		const calculatedInstallments = product.calculatedInstallments || [];
		
		// Pegar o melhor parcelamento (último da lista)
		const bestInstallment = calculatedInstallments.length > 0
			? calculatedInstallments[calculatedInstallments.length - 1]
			: null;

		// Preço original para comparação
		const originalPrice = product.productPrice || 0;
		const currentPrice = offer.price;
		const hasDiscount = originalPrice > currentPrice;

		return {
			image: product.image,
			name: product.name,
			originalPrice,
			currentPrice,
			hasDiscount,
			bestInstallment,
		};
	}, [offer]);

	if (!productInfo) return null;

	return (
		<div
			className="flex items-start gap-4 p-4 rounded-lg mb-6"
			style={{
				backgroundColor: settings.form?.background.color || "#fff",
				border: `1px solid ${settings.box?.default?.background?.color || "#e5e7eb"}`,
			}}
		>
			{/* Logo do Produto */}
			{productInfo.image && (
				<div className="flex-shrink-0">
					<Image
						src={productInfo.image}
						alt={productInfo.name}
						width={80}
						height={80}
						className="rounded-md object-cover"
					/>
				</div>
			)}

			{/* Informações do Produto */}
			<div className="flex-1">
				<h3
					className="text-lg font-semibold mb-2"
					style={{ color: settings.text?.color.primary }}
				>
					{productInfo.name}
				</h3>

				<div className="space-y-1">
					{/* Preço Original (riscado) */}
					{productInfo.hasDiscount && (
						<p
							className="text-sm line-through"
							style={{ color: settings.text?.color.secondary }}
						>
							{t("common.from")} {formatPrice(productInfo.originalPrice)}
						</p>
					)}

					{/* Preço Parcelado */}
					{productInfo.bestInstallment && (
						<p
							className="text-base font-semibold"
							style={{ color: settings.text?.color.active }}
						>
							{productInfo.bestInstallment.installment} {formatPrice(productInfo.bestInstallment.value)}
						</p>
					)}

					{/* Preço à Vista */}
					<p
						className="text-sm"
						style={{ color: settings.text?.color.primary }}
					>
						{t("common.or")} {formatPrice(productInfo.currentPrice)} {t("product.cash")}
					</p>
				</div>
			</div>
		</div>
	);
}

