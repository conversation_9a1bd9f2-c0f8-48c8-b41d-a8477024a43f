"use client";

import { useMemo, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { ChevronDownIcon } from "@heroicons/react/20/solid";

import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useLocale } from "@/contexts/intl-context";

const LOCALES = [
	{ code: "pt", label: "Português", flag: "🇧🇷", country: "Brasil" },
	{ code: "en", label: "English", flag: "🇺🇸", country: "United States" },
	{ code: "es", label: "Español", flag: "🇪🇸", country: "España" },
] as const;

type LocaleOption = (typeof LOCALES)[number];

export function LocaleSelector() {
	const locale = useLocale();
	const pathname = usePathname();
	const router = useRouter();
	const [open, setOpen] = useState(false);

	const currentLocale = useMemo<LocaleOption>(() => {
		return LOCALES.find((entry) => entry.code === locale) ?? LOCALES[0];
	}, [locale]);

	const handleSelect = (code: LocaleOption["code"]) => {
		if (code === locale) {
			setOpen(false);
			return;
		}

		const nextPath = buildLocalizedPath(pathname, code);

		if (typeof document !== "undefined") {
			document.cookie = `NEXT_LOCALE=${code}; max-age=31536000; path=/; SameSite=Lax`;
		}

		router.push(nextPath as any);
		router.refresh();
		setOpen(false);
	};

	return (
		<DropdownMenu open={open} onOpenChange={setOpen}>
			<DropdownMenuTrigger className="flex items-center gap-2 rounded-md px-3 py-1.5 text-sm font-medium transition-colors hover:bg-foreground/5">
				<span className="text-lg leading-none">{currentLocale.flag}</span>
				<span className="hidden sm:inline">{currentLocale.label}</span>
				<ChevronDownIcon className="size-4 opacity-60" />
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end" className="w-48">
				{LOCALES.map((option) => (
					<DropdownMenuItem
						key={option.code}
						onSelect={(event) => {
							event.preventDefault();
							handleSelect(option.code);
						}}
						className="flex items-center gap-3"
					>
						<span className="text-lg leading-none">{option.flag}</span>
						<span className="flex-1">
							<span className="block font-medium">{option.label}</span>
							 
						</span>
						{option.code === locale && <span className="text-emerald-500">✓</span>}
					</DropdownMenuItem>
				))}
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

function buildLocalizedPath(path: string | null, nextLocale: string): string {
	if (!path) {
		return `/${nextLocale}`;
	}

	const segments = path.split("/").filter(Boolean);

	if (segments.length === 0) {
		return `/${nextLocale}`;
	}

	segments[0] = nextLocale;

	return `/${segments.join("/")}`;
}

export default LocaleSelector;


