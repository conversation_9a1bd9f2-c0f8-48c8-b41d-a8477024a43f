"use client";

import { useCheckout } from "@/contexts";
import WaitingPayment from "@/components/payments/WaitingPayment";
import SuccessPayment from "@/components/payments/SuccessPayment";
import { FormProvider, useForm } from "react-hook-form";
import CheckoutConfigRenderer from "@/components/builder/CheckoutConfigRenderer";
import CheckoutComponent from "./CheckoutComponent";
import useDevice from "@/hooks/useDevice";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";

export function CheckoutForm() {
	const { t } = useTranslation();
	const { firstPayment, isFetching, offer, countdownHeight } = useCheckout();
	const { device } = useDevice();
	const settings = useSettings();

	const form = useForm({
		defaultValues: {
			paymentMethod: "credit_card",
			name: "",
			email: "",
			cpf: "",
			phone: "",
			cardNumber: "",
			cardExpiration: "",
			cardCvv: "",
			installments: 1,
			saveCard: false,
			calculatedInstallments: offer?.product?.calculatedInstallments || [],
		},
	});

	const style = {
		backgroundColor: settings?.backgroundColor,
		backgroundImage: settings?.backgroundImage,
		backgroundSize: settings?.backgroundSize,
		backgroundPosition: settings?.backgroundPosition,
		backgroundRepeat: settings?.backgroundRepeat,
	};

	if (isFetching) {
		return (
			<div
				className="flex items-center justify-center min-h-screen"
				style={style}
			>
				<div className="text-gray-500">{t('checkout.loading_checkout')}</div>
			</div>
		);
	}

	if (firstPayment?.status === "waiting_payment") {
		return (
			<div
				className="flex flex-col items-center justify-center min-h-screen w-full md:py-5 md:px-12"
				style={style}
			>
				<div className="mx-auto max-w-[720px] w-full">
					<WaitingPayment />
				</div>
			</div>
		);
	}

	if (firstPayment?.status === "paid") {
		return (
			<div
				className="flex justify-center p-2 md:p-5 w-full min-h-screen"
				style={style}
			>
				<div className="max-sm:w-full w-[720px]">
					<SuccessPayment />
				</div>
			</div>
		);
	}

	// Renderizar com checkout builder se houver config
	if (offer?.checkout?.config) {
		return (
			<FormProvider {...form}>
				<div
					className="flex flex-col min-h-screen w-full md:py-5 md:px-12"
					style={{
						...style,
						marginTop: countdownHeight - 10,
					}}
				>
					<div className="max-w-[1080px] mx-auto">
						<CheckoutConfigRenderer config={offer.checkout.config} device={device} />
					</div>
				</div>
			</FormProvider>
		);
	}

	// Fallback: renderizar formulário simples
	return (
		<FormProvider {...form}>
			<div
				className="flex flex-col min-h-screen w-full md:py-5 md:px-12"
				style={style}
			>
				<div className="max-w-[1080px] mx-auto">
					<div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
						<div className="col-span-1 lg:col-span-2 px-3 py-5">
							<CheckoutComponent />
						</div>
					</div>
				</div>
			</div>
		</FormProvider>
	);
}

