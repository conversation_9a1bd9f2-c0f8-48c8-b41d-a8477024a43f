"use client";

import { useCheckout } from "@/contexts";
import { useFormContext } from "react-hook-form";
import { useState, useMemo } from "react";
import { LoadingPaymentForm } from "@/components/payments/LoadingPaymentForm";
import { CreditCardIcon } from "@heroicons/react/20/solid";
import { useTranslation } from "@/hooks/useTranslation";

export function PaymentMethods() {
	const { t } = useTranslation();
	const form = useFormContext();
	const { setValue } = form;
	const { offer, setPaymentMethod, paymentTabs, isFetching } = useCheckout();
	const [tabIndex, setTabIndex] = useState(0);

	const tabs = useMemo(() => {
		if (isFetching) {
			return [
				{
					id: "1",
					label: "loading",
					Icon: CreditCardIcon,
					Component: LoadingPaymentForm,
				},
				{
					id: "2",
					label: "loading",
					Icon: CreditCardIcon,
					Component: LoadingPaymentForm,
				},
				{
					id: "3",
					label: "loading",
					Icon: CreditCardIcon,
					Component: LoadingPaymentForm,
				},
			];
		}

		const paymentsOrder = offer?.product?.paymentsOrder || [];

		const sortedTabs = paymentTabs
			?.filter(({ id }) => offer?.product.paymentMethods?.some(({ type }) => type === id))
			.sort((a, b) => paymentsOrder.indexOf(a.id) - paymentsOrder.indexOf(b.id));

		return sortedTabs || [];
	}, [isFetching, paymentTabs, offer]);

	const handleTabChange = (index: number) => {
		setTabIndex(index);
		const selectedTab = tabs[index];
		if (selectedTab && selectedTab.id !== "loading") {
			setValue("paymentMethod", selectedTab.id);
			setPaymentMethod({
				type: selectedTab.id as any,
				name: selectedTab.label,
			});
		}
	};

	if (!tabs || tabs.length === 0) {
		return (
			<div className="flex items-center justify-center p-6">
				<div className="text-gray-500">{t('checkout.no_payment_methods')}</div>
			</div>
		);
	}

	const ActiveComponent = tabs[tabIndex]?.Component || (() => <div>{t('checkout.not_found')}</div>);

	return (
		<div className="w-full">
			<div className="flex gap-3 overflow-x-auto pb-2 px-1">
				{tabs.map(({ Icon, label, id }, index) => (
					<button
						key={id}
						type="button"
						onClick={() => handleTabChange(index)}
						className={`flex flex-col items-center justify-center gap-2 p-4 rounded-lg border-2 transition-all flex-1 min-w-[110px] max-w-[140px] ${
							tabIndex === index
								? "bg-brand-primary text-white border-brand-primary shadow-lg"
								: "bg-white text-gray-700 border-gray-300 hover:border-brand-primary hover:border-opacity-60 hover:shadow-md"
						}`}
					>
						<Icon 
							className={`w-7 h-7 transition-colors ${
								tabIndex === index ? "text-white" : "text-gray-700"
							}`}
						/>
						<span className="text-xs font-semibold whitespace-nowrap overflow-hidden text-ellipsis w-full text-center">
							{label === "loading" ? (
								<div className="bg-gray-200 h-[15px] rounded-full w-20 animate-pulse mx-auto" />
							) : (
								label
							)}
						</span>
					</button>
				))}
			</div>
			<div className="mt-4 w-full">
				<ActiveComponent />
			</div>
		</div>
	);
}

