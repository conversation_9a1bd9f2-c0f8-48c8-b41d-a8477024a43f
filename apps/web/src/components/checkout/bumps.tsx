"use client";

import { useEffect } from "react";
import { useFormContext } from "react-hook-form";
import { useCheckout } from "@/contexts";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";
import { BumpItem } from "./bump-item";

export function Bumps() {
	const { offer } = useCheckout();
	const settings = useSettings();
	const { t } = useTranslation();
	const { setValue } = useFormContext();

	// Verificar se offer e product existem antes de acessar bumps
	if (!offer || !offer.product) {
		return null;
	}

	const bumps = offer.product.bumps || [];

	// Inicializar bumps no form
	useEffect(() => {
		if (bumps && bumps.length > 0) {
			setValue("bumps", bumps);
		}
	}, [bumps, setValue]);

	// Não renderizar se não houver bumps
	if (bumps.length === 0) {
		return null;
	}

	return (
		<section className="mb-6">
			<h2
				className="text-lg font-bold mb-4"
				style={{ color: settings.text?.color.primary }}
			>
				{t("bump.title")}
			</h2>

			<div className="space-y-3">
				{bumps.map((_, index) => (
					<BumpItem key={index} index={index} defaultChecked={false} />
				))}
			</div>
		</section>
	);
}

