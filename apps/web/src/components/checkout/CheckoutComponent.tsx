"use client";

import { useCheckout } from "@/contexts";
import { useCountry } from "@/contexts/country-context";
import { useFormContext } from "react-hook-form";
import TextField from "@/components/ui/text-field";
import Card from "@/components/ui/card-form";
import useSettings from "@/hooks/useSettings";
import { PaymentMethods } from "./payment-methods";
import OrderSummary from "@/components/payments/OrderSummary";
import Button from "@/components/ui/button-form";
import { getLabel } from "@/lib/utils/brand";
import { useTranslation } from "@/hooks/useTranslation";
import { CountrySelector } from "./country-selector";
import { ProductInfoCard } from "./product-info-card";
import { Bumps } from "./bumps";
import { SecurityFooter } from "./security-footer";
import { DataPrivacyTooltip } from "@/components/ui/data-privacy-tooltip";
import {
	UserCircleIcon,
	CreditCardIcon,
	UserIcon,
	EnvelopeIcon,
	DocumentTextIcon,
	PhoneIcon,
} from "@heroicons/react/24/outline";

export default function CheckoutComponent() {
	const { offer, paying, creditCardError } = useCheckout();
	const { country } = useCountry();
	const settings = useSettings();
	const form = useFormContext();
	const { t } = useTranslation();

	if (!offer) {
		return null;
	}

	const handleSubmit = form.handleSubmit((data) => {
		console.log("Form data:", data);
		// TODO: Implementar lógica de pagamento
	});

	return (
		<Card
			style={{
				backgroundColor: settings.form?.background.color,
			}}
		>
			<form onSubmit={handleSubmit} className="flex flex-col gap-6 p-6">
				{/* Product Info Card */}
				<ProductInfoCard />

				{/* Título de Contato com Ícone */}
				<div className="flex items-center justify-between gap-4">
					<div className="flex items-center gap-2">
						<UserCircleIcon
							className="w-6 h-6"
							style={{ color: settings.text?.color.primary }}
						/>
						<h2
							className="text-lg font-bold"
							style={{ color: settings.text?.color.primary }}
						>
							{t("checkout.contact_info_title")}
						</h2>
					</div>
					<CountrySelector />
				</div>

				{/* Nome */}
				<TextField
					name="name"
					size="sm"
					label={t("form.labels.name")}
					placeholder={t("form.placeholders.name")}
					icon={<UserIcon className="w-5 h-5" />}
					iconPosition="left"
				/>

				{/* Email */}
				<div className="space-y-1">
					<TextField
						name="email"
						size="sm"
						label={t("form.labels.email")}
						placeholder={t("form.placeholders.email")}
						type="email"
						icon={<EnvelopeIcon className="w-5 h-5" />}
						iconPosition="left"
					/>
					<DataPrivacyTooltip field="email" />
				</div>

				{/* Documento e Telefone */}
				<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full">
					<div className="space-y-1">
						<TextField
							name="document"
							size="sm"
							label={country.documentLabel}
							placeholder={country.documentLabel}
							cpfCnpj={country.code === "BR"}
							mask={country.code !== "BR" ? country.phoneFormat : undefined}
							icon={<DocumentTextIcon className="w-5 h-5" />}
							iconPosition="left"
						/>
						<DataPrivacyTooltip field="document" />
					</div>
					<div className="space-y-1">
						<TextField
							name="phone"
							size="sm"
							label={getLabel.phone()}
							placeholder={t("form.placeholders.phone")}
							mask={country.phoneFormat}
							icon={<PhoneIcon className="w-5 h-5" />}
							iconPosition="left"
						/>
						<DataPrivacyTooltip field="phone" />
					</div>
				</div>

				{/* Título de Pagamento com Ícone */}
				<div className="flex items-center gap-2 mt-6">
					<CreditCardIcon
						className="w-6 h-6"
						style={{ color: settings.text?.color.primary }}
					/>
					<h2
						className="text-lg font-bold"
						style={{ color: settings.text?.color.primary }}
					>
						{t("checkout.payment_method_title")}
					</h2>
				</div>

				{/* Métodos de Pagamento */}
				<PaymentMethods />

				{/* Order Bumps */}
				<Bumps />

				{/* Resumo do Pedido */}
				<div className="mt-6">
					<OrderSummary />
				</div>

				{/* Erro de cartão de crédito */}
				{creditCardError && (
					<div className="p-4 bg-red-50 border border-red-200 rounded-md">
						<p className="text-red-700 text-sm">{creditCardError}</p>
					</div>
				)}

				{/* Botão de Pagar */}
				<Button
					type="submit"
					loading={paying}
					className="w-full mt-6"
					style={{
						backgroundColor: settings.payButton.color,
						color: settings.payButton.text.color,
					}}
				>
					<span className="text-[18px]">{settings.payButton.text.text}</span>
				</Button>

				{/* Security Footer */}
				<SecurityFooter />
			</form>
		</Card>
	);
}

