"use client";

import type { CheckoutData } from "@/lib/api/checkout";
import { CheckoutProvider } from "@/contexts";
import { CountryProvider } from "@/contexts/country-context";
import { CheckoutForm } from "./checkout-form";

type CheckoutClientProps = {
	initialData: CheckoutData;
	checkoutId: string;
};

export function CheckoutClient({ initialData, checkoutId }: CheckoutClientProps) {
	return (
		<CountryProvider>
			<CheckoutProvider initialData={initialData as any} checkoutId={checkoutId}>
				<div className="flex min-h-screen">
					<main className="flex-1">
						<CheckoutForm />
					</main>
				</div>
			</CheckoutProvider>
		</CountryProvider>
	);
}


