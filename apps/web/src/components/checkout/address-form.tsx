"use client";

import { useEffect, useState } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { MapPinIcon, CheckIcon, ExclamationTriangleIcon } from "@heroicons/react/20/solid";
import { useCepSearch } from "@/hooks/useCepSearch";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";
import TextField from "@/components/ui/text-field";
import Select from "@/components/ui/select";
import { Spinner } from "@/components/ui/spinner";
import { getBranding } from "@/lib/env";

const BRAZILIAN_STATES: { value: string; label: string }[] = [
	{ value: "AC", label: "Acre" },
	{ value: "AL", label: "Alagoas" },
	{ value: "AP", label: "Amapá" },
	{ value: "AM", label: "Amazonas" },
	{ value: "BA", label: "Bahia" },
	{ value: "CE", label: "Cear<PERSON>" },
	{ value: "DF", label: "Distrito Federal" },
	{ value: "ES", label: "Espírito Santo" },
	{ value: "GO", label: "Goiás" },
	{ value: "MA", label: "Maranhão" },
	{ value: "MT", label: "Mato Grosso" },
	{ value: "MS", label: "Mato Grosso do Sul" },
	{ value: "MG", label: "Minas Gerais" },
	{ value: "PA", label: "Pará" },
	{ value: "PB", label: "Paraíba" },
	{ value: "PR", label: "Paraná" },
	{ value: "PE", label: "Pernambuco" },
	{ value: "PI", label: "Piauí" },
	{ value: "RJ", label: "Rio de Janeiro" },
	{ value: "RN", label: "Rio Grande do Norte" },
	{ value: "RS", label: "Rio Grande do Sul" },
	{ value: "RO", label: "Rondônia" },
	{ value: "RR", label: "Roraima" },
	{ value: "SC", label: "Santa Catarina" },
	{ value: "SP", label: "São Paulo" },
	{ value: "SE", label: "Sergipe" },
	{ value: "TO", label: "Tocantins" },
];

export type AddressFormValues = {
	zipCode: string;
	street: string;
	number: string;
	complement?: string;
	neighborhood: string;
	city: string;
	state: string;
};

interface AddressFormProps {
	className?: string;
}

export function AddressForm({ className = "" }: AddressFormProps) {
	const settings = useSettings();
	const { t } = useTranslation();
	const { searchCep, isLoadingCep, handleManualChange, resetManualChanges } =
		useCepSearch();
	const { setValue } = useFormContext();

	const zipCode = useWatch({ name: "zipCode" });
	const city = useWatch({ name: "city" });
	const state = useWatch({ name: "state" });

	const [cepFound, setCepFound] = useState(false);
	const [cepError, setCepError] = useState(false);
	const [lastSearchedCep, setLastSearchedCep] = useState("");

	const isNommi = getBranding() === "nommi";

	// Effect to handle CEP search
	useEffect(() => {
		if (zipCode && zipCode.replace(/\D/g, "").length === 8) {
			const normalizedCep = zipCode.replace(/\D/g, "");
			if (normalizedCep !== lastSearchedCep) {
				setLastSearchedCep(normalizedCep);
				setCepError(false);
				setCepFound(false);
				resetManualChanges();
				searchCep(zipCode);
			}
		} else if (zipCode && zipCode.replace(/\D/g, "").length < 8) {
			setCepFound(false);
			setCepError(false);
			setLastSearchedCep("");
		}
	}, [zipCode, searchCep, resetManualChanges, lastSearchedCep]);

	// Effect to detect when CEP was found or failed
	useEffect(() => {
		if (
			!isLoadingCep &&
			lastSearchedCep &&
			zipCode?.replace(/\D/g, "") === lastSearchedCep
		) {
			if (city && state) {
				setCepFound(true);
				setCepError(false);
			} else if (zipCode?.replace(/\D/g, "").length === 8) {
				setCepError(true);
				setCepFound(false);
			}
		}
	}, [isLoadingCep, city, state, lastSearchedCep, zipCode]);

	const hasValidCep = zipCode && zipCode.replace(/\D/g, "").length === 8;
	const showAddressFields = hasValidCep || zipCode?.length > 0;

	return (
		<div className={`w-full ${className}`}>
			{/* Title */}
			<div className="flex items-center mb-4 font-bold">
				{!isNommi && (
					<MapPinIcon
						style={{ color: settings.text?.color.primary }}
						width={24}
						height={24}
						className="mr-2"
					/>
				)}
				<span style={{ color: settings.text?.color.primary }} className="text-lg">
					{isNommi ? t("address.delivery_title") : t("address.title")}
				</span>
			</div>

			<div className="flex flex-col gap-4">
				{/* CEP */}
				<div className="relative">
					<TextField
						name="zipCode"
						size="sm"
						label={t("address.zipcode_label")}
						placeholder={t("form.placeholders.zipcode")}
						mask="99999-999"
						inputMode="numeric"
					/>
					<div className="absolute right-3 top-8 flex items-center">
						{isLoadingCep && <Spinner className="w-4 h-4" />}
						{cepFound && !isLoadingCep && (
							<CheckIcon
								className="w-4 h-4 text-green-500"
								title={t("address.zipcode_found")}
							/>
						)}
						{cepError && !isLoadingCep && hasValidCep && (
							<ExclamationTriangleIcon className="w-4 h-4 text-yellow-500" />
						)}
					</div>
				</div>

				{/* Address Fields */}
				{showAddressFields && (
					<>
						<div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
							<div className="sm:col-span-2">
								<TextField
									name="street"
									size="sm"
									label={t("address.street_label")}
									placeholder={t("form.placeholders.street")}
									normalize
									onChange={() => handleManualChange("street")}
								/>
							</div>
							<div>
								<TextField
									name="number"
									size="sm"
									label={t("address.number_label")}
									placeholder={t("form.placeholders.number")}
									normalize
								/>
							</div>
						</div>

						<TextField
							name="complement"
							size="sm"
							label={t("address.complement_label")}
							placeholder={t("form.placeholders.complement")}
							normalize
						/>

						<TextField
							name="neighborhood"
							size="sm"
							label={t("address.neighborhood_label")}
							placeholder={t("form.placeholders.neighborhood")}
							normalize
							onChange={() => handleManualChange("neighborhood")}
						/>

						<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
							<TextField
								name="city"
								size="sm"
								label={t("address.city_label")}
								placeholder={t("form.placeholders.city")}
								normalize
								onChange={() => handleManualChange("city")}
							/>
							<Select
								name="state"
								size="sm"
								label={t("address.state_label")}
								placeholder={t("form.placeholders.state") || "Selecione o estado"}
								options={BRAZILIAN_STATES}
								onChange={(e) => {
									setValue("state", e.target.value);
									handleManualChange("state");
								}}
							/>
						</div>

						{cepError && hasValidCep && (
							<div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
								<ExclamationTriangleIcon className="w-4 h-4 text-yellow-600" />
								<span className="text-sm text-yellow-800">
									{t("address.zipcode_not_found")}
								</span>
							</div>
						)}
					</>
				)}
			</div>

			{/* Nommi Shipping */}
			{isNommi && (
				<>
					<div className="flex items-center font-bold mt-6">
						<span style={{ color: settings.text?.color.primary }} className="text-lg">
							{t("address.shipping_method")}
						</span>
					</div>
					<div className="flex flex-col gap-4">
						<Select
							name="freight"
							size="sm"
							label={t("address.shipping_options")}
							placeholder="Selecione uma opção"
							options={[
								{
									value: "15",
									label: "Frete disponível: SEDEX – R$ 15,00 – entrega em até 7 dias úteis",
								},
								{
									value: "10",
									label:
										"Frete disponível: Melhor Envio – R$ 10,00 – entrega em até 5 dias úteis",
								},
							]}
							onChange={(e) => {
								setValue("freight", e.target.value);
							}}
						/>
					</div>
				</>
			)}
		</div>
	);
}

