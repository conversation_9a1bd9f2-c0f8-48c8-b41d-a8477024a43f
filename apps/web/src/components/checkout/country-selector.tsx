"use client";

import { useState } from "react";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useCountry } from "@/contexts/country-context";
import { COUNTRY_OPTIONS } from "@/constants/countries";
import type { CountryCode } from "@/types/country";

export function CountrySelector() {
  const { country, changeCountry } = useCountry();
  const [open, setOpen] = useState(false);

  const handleSelect = (countryCode: CountryCode) => {
    if (countryCode === country.code) {
      setOpen(false);
      return;
    }

    changeCountry(countryCode);
    setOpen(false);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger className="flex items-center gap-2 rounded-md px-3 py-1.5 text-sm font-medium transition-colors hover:bg-foreground/5 border border-gray-200">
        <span className="text-lg leading-none">{country.flag}</span>
        <span className="hidden sm:inline">{country.name}</span>
        <ChevronDownIcon className="size-4 opacity-60" />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {COUNTRY_OPTIONS.map((option) => (
          <DropdownMenuItem
            key={option.code}
            onSelect={(event) => {
              event.preventDefault();
              handleSelect(option.code);
            }}
            className="flex items-center gap-3"
          >
            <span className="text-lg leading-none">{option.flag}</span>
            <span className="flex-1">
              <span className="block font-medium">{option.name}</span>
              <span className="block text-xs text-muted-foreground">
                {option.currency} • {option.language.toUpperCase()}
              </span>
            </span>
            {option.code === country.code && (
              <span className="text-emerald-500">✓</span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

