"use client";

import { XMarkIcon } from "@heroicons/react/20/solid";
import classNames from "classnames";
import { useEffect } from "react";

type ToastProps = {
	children: React.ReactNode;
	onClose: () => void;
	className?: string;
	exibitionTime?: number;
};

const Toast = ({
	children,
	onClose,
	className,
	exibitionTime,
}: ToastProps) => {
	useEffect(() => {
		if (!exibitionTime) return;
		const timer = setTimeout(() => {
			onClose();
		}, exibitionTime * 1000);

		return () => clearTimeout(timer);
	}, [onClose, exibitionTime]);

	return (
		<div
			className={classNames(
				"w-72 p-5 bg-gray-200 max-w-full rounded-xl relative shadow-lg",
				className
			)}
		>
			{children}
			<XMarkIcon
				className="w-6 h-6 text-gray-500 absolute top-2 right-2 cursor-pointer rounded-full hover:bg-gray-200 transition-all duration-150"
				onClick={onClose}
			/>
		</div>
	);
};

export default Toast;

