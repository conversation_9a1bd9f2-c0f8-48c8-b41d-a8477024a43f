"use client";

type Props = {
	onOpen: () => void;
	children: string;
	spanClassName?: string;
	isRemoveType?: boolean;
};

export function LinkButton({
	onOpen,
	isRemoveType,
	children,
	spanClassName,
}: Props) {
	return (
		<button
			type="button"
			onClick={onOpen}
			className={`flex items-center ${
				isRemoveType ? "text-red-500" : "text-blue-500"
			} hover:underline`}
		>
			<span className={`mr-2 ${spanClassName}`}>{children}</span>
		</button>
	);
}

