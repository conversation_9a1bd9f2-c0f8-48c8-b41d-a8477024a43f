"use client";

import classNames from "classnames";
import { useEffect } from "react";
import type { CheckoutComponentFacebookAttibutesType } from "@/types/builder";

const CheckoutComponentFacebookSizes = {
  original: "",
  small: "250",
  medium: "400",
  large: "600",
};

const CheckoutComponentFacebook: React.FC<
  CheckoutComponentFacebookAttibutesType
> = ({ count, options, orderBy, size, tabs, type, url }) => {
  const key = JSON.stringify({
    count,
    options,
    orderBy,
    size,
    tabs,
    type,
    url,
  });

  useEffect(() => {
    // @ts-ignore
    if (window.FB) {
      // @ts-ignore
      window.FB.XFBML.parse();
    }
  }, [key]);

  return (
    <div
      className={classNames(
        "flex flex-col items-center rounded-lg relative box-border overflow-x-auto max-w-full bg-gray-50 min-h-[150px] w-full"
      )}
    >
      <div className="inline-block max-w-full overflow-x-auto">
        {type === "page" && (
          <div
            key={key}
            className="fb-page max-w-full"
            data-href={url}
            data-tabs={tabs?.join(",") || "timeline"}
            data-small-header={options.includes("smallHeader")}
            data-hide-cover={!options.includes("coverPhoto")}
            data-show-facepile={options.includes("facePile")}
            data-hide-cta={!options.includes("callToAction")}
            data-width={CheckoutComponentFacebookSizes[size] || "500"}
          >
            <blockquote cite={url} className="fb-xfbml-parse-ignore">
              <a href={url}>Facebook</a>
            </blockquote>
          </div>
        )}
        {type === "post" && (
          <div
            key={key}
            className="fb-post max-w-full"
            data-href={url}
            data-width={CheckoutComponentFacebookSizes[size] || "500"}
            data-show-text="true"
            data-show-images="true"
          />
        )}
        {type === "commentSection" && (
          <div
            key={key}
            className="fb-comments max-w-full"
            data-href={url}
            data-width={CheckoutComponentFacebookSizes[size] || "500"}
            data-numposts={count}
            data-order-by="time"
            data-colorscheme="light"
          />
        )}
        {type === "singleComment" && (
          <div
            key={key}
            className="fb-comment-embed max-w-full"
            data-href={url}
            data-width={CheckoutComponentFacebookSizes[size] || "500"}
          />
        )}
      </div>
    </div>
  );
};

export default CheckoutComponentFacebook;

