"use client";

import type { CheckoutConfig, CheckoutDeviceType } from '@/types/builder';
import CheckoutComponentChat from './CheckoutComponentChat';
import CheckoutComponentExitPopup from './CheckoutComponentExitPopup';
import CheckoutComponentNotification from './CheckoutComponentNotification';
import CheckoutRow from './CheckoutRow';

const CheckoutConfigRenderer: React.FC<{
  config: CheckoutConfig;
  device: CheckoutDeviceType;
}> = ({ config, device }) => {
  return (
    <div className="flex flex-col p-3 gap-3">
      {config?.[device]?.rows?.map((row) => <CheckoutRow key={row.id} {...row} />)}
      {config?.[device]?.extra.exitPopup.attributes.enabled && (
        <CheckoutComponentExitPopup {...config[device].extra.exitPopup.attributes} />
      )}
      {config?.[device]?.extra.notification.attributes.enabled && (
        <CheckoutComponentNotification {...config[device].extra.notification.attributes} />
      )}
      {config?.[device]?.extra.chat.attributes.enabled && (
        <CheckoutComponentChat {...config[device].extra.chat.attributes} />
      )}
    </div>
  );
};

export default CheckoutConfigRenderer;

