"use client";

import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { useCheckout } from "@/contexts";
import { useNotificationContext } from "@/contexts/notification-context";
import useDevice from "@/hooks/useDevice";
import { getRandomNumber } from "@/lib/utils/number";
import type {
	CheckoutComponentNotificationAttibutesType,
	CheckoutComponentNotificationType,
} from "@/types/builder";
import Toast from "@/components/common/Toast";
import type { Notification } from "@/hooks/useNotifications";
import { useTranslation } from "@/hooks/useTranslation";

const Icon = {
	interested_last_24_hours: "/assets/group.png",
	interested_last_week: "/assets/growth.png",
	interested_right_now: "/assets/growth.png",
	purchased_last_24_hours: "/assets/check.png",
	purchased_last_week: "/assets/check.png",
};

const CheckoutComponentNotification: React.FC<
	CheckoutComponentNotificationAttibutesType
> = () => {
	const { notifications, notify } = useNotificationContext();
	const { offer } = useCheckout();
	const { device } = useDevice();
	const { t, locale } = useTranslation();

	const [currentNotification, setCurrentNotification] =
		useState<Notification | null>(null);
	const [queue, setQueue] = useState<Notification[]>([]);
	const [mounted, setMounted] = useState(false);

	const dismissCurrent = () => {
		setCurrentNotification(null);
	};

	useEffect(() => {
		setMounted(true);
	}, []);

	useEffect(() => {
		if (!currentNotification && queue.length > 0) {
			const [nextNotification, ...remainingQueue] = queue;
			setCurrentNotification(nextNotification);
			setQueue(remainingQueue);
		}
	}, [currentNotification, queue]);

	useEffect(() => {
		if (!!queue.length && !!notifications.length) return;
		setQueue(notifications);
	}, [notifications, queue.length]);

	useEffect(() => {
		const notificationsAttributes =
			offer?.checkout.config[device].extra.notification.attributes;

		const notificationsEntries = (
			notificationsAttributes?.enabled
				? Object.entries(notificationsAttributes)
				: []
		)
			.filter(([key]) => key !== "enabled")
			.filter(([_, notification]) => notification?.enabled);

		const mapTexts = {
			interested_last_24_hours: (min: number, max: number) => {
				const value = getRandomNumber(min, max);
				if (locale === "pt-BR") {
					return `${value} pessoa${value > 1 ? "s" : ""} interessadas nesse produto nas últimas 24 horas`;
				}
				return `${value} ${value > 1 ? "people" : "person"} interested in this product in the last 24 hours`;
			},
			interested_last_week: (min: number, max: number) => {
				const value = getRandomNumber(min, max);
				if (locale === "pt-BR") {
					return `${value} pessoa${value > 1 ? "s" : ""} interessadas nesse produto na última semana`;
				}
				return `${value} ${value > 1 ? "people" : "person"} interested in this product in the last week`;
			},
			interested_right_now: (min: number, max: number) => {
				const value = getRandomNumber(min, max);
				if (locale === "pt-BR") {
					return `${value} pessoa${value > 1 ? "s" : ""} interessadas nesse produto agora`;
				}
				return `${value} ${value > 1 ? "people" : "person"} interested in this product right now`;
			},
			purchased_last_24_hours: (min: number, max: number) => {
				const value = getRandomNumber(min, max);
				if (locale === "pt-BR") {
					return `${value} compra${value > 1 ? "s" : ""} desse produto nas últimas 24 horas`;
				}
				return `${value} ${value > 1 ? "purchases" : "purchase"} of this product in the last 24 hours`;
			},
			purchased_last_week: (min: number, max: number) => {
				const value = getRandomNumber(min, max);
				if (locale === "pt-BR") {
					return `${value} compra${value > 1 ? "s" : ""} desse produto na última semana`;
				}
				return `${value} ${value > 1 ? "purchases" : "purchase"} of this product in the last week`;
			},
		};

		notificationsEntries.forEach(([type, notification]) => {
			const { min, max, exibitionTime } = notification;
			const message = mapTexts[type as keyof typeof mapTexts](+min, +max);

			notify(message, type as CheckoutComponentNotificationType, +exibitionTime);
		});
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [offer]);

	if (!mounted) {
		return null;
	}

	return createPortal(
		<div className="fixed top-0 right-0 p-5 flex flex-col gap-3 z-50">
			{currentNotification && (
				<Toast
					key={currentNotification.id}
					exibitionTime={currentNotification.exibitionTime}
					onClose={dismissCurrent}
				>
					<div className="flex flex-row items-center gap-4 overflow-hidden">
						<img
							className="w-14 h-14 rounded-full"
							src={Icon[currentNotification.type]}
							alt={currentNotification.type}
						/>
						<span className="text-sm text-gray-700 break-words max-w-full">
							{currentNotification.message}
						</span>
					</div>
				</Toast>
			)}
		</div>,
		document.body
	);
};

export default CheckoutComponentNotification;

