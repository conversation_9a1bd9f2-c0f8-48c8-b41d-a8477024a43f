"use client";

import classNames from "classnames";
import React from "react";
import { CheckoutAlignment } from "@/types/builder";

const align = {
  [CheckoutAlignment.LEFT]: "items-start",
  [CheckoutAlignment.CENTER]: "items-center",
  [CheckoutAlignment.RIGHT]: "items-end",
};

type DivProps = React.HTMLAttributes<HTMLDivElement>;

type CheckoutComponentAlignmentContainerProps = DivProps & {
  alignment: CheckoutAlignment;
  className?: string;
};

const CheckoutComponentAlignmentContainer: React.FC<
  CheckoutComponentAlignmentContainerProps
> = ({ alignment, className, children, ...props }) => {
  return (
    <div
      {...props}
      className={
        classNames(className, "flex flex-col w-full", align?.[alignment]) || ""
      }
    >
      {children}
    </div>
  );
};

export default CheckoutComponentAlignmentContainer;

