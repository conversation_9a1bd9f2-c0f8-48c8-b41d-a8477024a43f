"use client";

import classNames from "classnames";
import { StarIcon, UserIcon } from "@heroicons/react/24/solid";
import useImagePreview from "@/hooks/useImagePreview";
import { CheckoutAlignment } from "@/types/builder";
import type { CheckoutComponentTestimonialAttibutesType } from "@/types/builder";
import CheckoutComponentAlignmentContainer from "./CheckoutComponentAlignmentContainer";

const CheckoutComponentTestimonial: React.FC<
	CheckoutComponentTestimonialAttibutesType
> = ({ author, avatar, backgroundColor, horizontal, rating, text, textColor }) => {
	const { preview } = useImagePreview({
		image: avatar,
	});

	return (
		<CheckoutComponentAlignmentContainer alignment={CheckoutAlignment.CENTER}>
			<div
				className={classNames("flex rounded-lg p-6 w-[400px] max-w-full", {
					"items-center gap-5": horizontal,
					"flex-col gap-3": !horizontal,
				})}
				style={{
					backgroundColor,
					color: textColor,
				}}
			>
				<div
					className={classNames(
						"w-20 h-20 flex items-center justify-center mx-auto",
						{
							"bg-gray-200 rounded-full": !preview,
						}
					)}
				>
					{preview ? (
						<img
							src={preview || "/assets/user.svg"}
							alt={avatar?.id}
							className="object-cover w-full h-full rounded-full"
						/>
					) : (
						<UserIcon className="w-14 h-14 text-gray-400" />
					)}
				</div>
				<div
					className={classNames("flex flex-col gap-2", {
						"items-start": horizontal,
						"items-center": !horizontal,
					})}
				>
					<span
						className={classNames("font-bold text-xl", {
							"text-center": !horizontal,
						})}
					>
						{text}
					</span>
					<div className="flex items-center mt-1">
						{Array.from({ length: rating }).map((_, index) => (
							<StarIcon
								key={index}
								className="w-5 h-5 fill-yellow-500 text-yellow-500"
							/>
						))}
					</div>
					<span
						className={classNames("text-md", {
							"text-center": !horizontal,
						})}
					>
						{author}
					</span>
				</div>
			</div>
		</CheckoutComponentAlignmentContainer>
	);
};

export default CheckoutComponentTestimonial;

