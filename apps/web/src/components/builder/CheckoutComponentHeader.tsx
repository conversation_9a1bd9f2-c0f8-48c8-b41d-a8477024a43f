"use client";

import classNames from "classnames";
import useImagePreview from "@/hooks/useImagePreview";
import type { CheckoutComponentHeaderAttibutesType } from "@/types/builder";

const CheckoutComponentHeader: React.FC<
  CheckoutComponentHeaderAttibutesType
> = ({
  backgroundType,
  backgroundColor,
  backgroundImage,
  productImage,
  productImageAlignment,
  titleFontSize,
  titleTextColor,
  titleText,
  showSubtitle,
  subtitleFontSize,
  subtitleTextColor,
  subtitleText,
}) => {
  const { preview: backgroundImagePreview } = useImagePreview({
    image: backgroundImage,
  });

  const { preview: productImagePreview } = useImagePreview({
    image: productImage,
  });

  return (
    <div
      className={classNames(
        "flex gap-5 bg-cover bg-no-repeat bg-center p-5 items-center",
        {
          "flex-row": productImageAlignment === "left",
          "flex-row-reverse justify-between": productImageAlignment === "right",
        }
      )}
      style={{
        ...(backgroundType === "image" && {
          backgroundImage: `url(${backgroundImagePreview})`,
        }),
        ...(backgroundType === "color" && {
          backgroundColor,
        }),
      }}
    >
      <img
        src={productImagePreview || "/assets/placeholder.svg"}
        alt={productImage?.id}
        className="w-40 h-40"
      />
      <div className="flex flex-col gap-2">
        <h3
          className="text-xl font-bold"
          style={{
            color: titleTextColor,
            fontSize: `${titleFontSize}px`,
          }}
        >
          {titleText}
        </h3>
        {showSubtitle && (
          <h4
            className="text-base"
            style={{
              color: subtitleTextColor,
              fontSize: `${subtitleFontSize}px`,
            }}
          >
            {subtitleText}
          </h4>
        )}
      </div>
    </div>
  );
};

export default CheckoutComponentHeader;

