"use client";

import classNames from "classnames";
import {
	AtSymbolIcon,
	ChartBarIcon,
	ChatBubbleLeftIcon,
	CheckIcon,
	CursorArrowRaysIcon,
	CloudIcon,
	ArrowDownTrayIcon,
	DocumentIcon,
	HeartIcon,
	UserGroupIcon,
	PlayIcon,
	ShieldCheckIcon,
	GlobeAltIcon,
} from "@heroicons/react/24/outline";
import type { CheckoutComponentAdvantageAttibutesType } from "@/types/builder";

const CheckoutBuilderAdvantageSizes = {
	original: "w-full",
	small: "w-1/2",
	medium: "w-2/3",
	large: "w-3/4",
};

const icons = {
	atsign: AtSymbolIcon,
	chart: ChartBarIcon,
	chat: ChatBubbleLeftIcon,
	check: CheckIcon,
	click: CursorArrowRaysIcon,
	cloud: CloudIcon,
	download: ArrowDownTrayIcon,
	file: DocumentIcon,
	heart: HeartIcon,
	people: UserGroupIcon,
	play: PlayIcon,
	verified: ShieldCheckIcon,
	web: GlobeAltIcon,
};

const CheckoutComponentAdvantage: React.FC<
	CheckoutComponentAdvantageAttibutesType
> = ({ title, subtitle, icon, darkMode, primaryColor, size, titleTextColor, vertical }) => {
	const Icon = icons[icon] || null;
	return (
		<div className="w-full flex justify-center">
			<div
				style={{
					backgroundColor: darkMode ? "#1A202C" : "#FFFFFF",
					borderColor: primaryColor,
					borderStyle: "solid",
					borderWidth: "2px",
				}}
				className={classNames(
					CheckoutBuilderAdvantageSizes[size],
					"flex items-center p-5 gap-3 rounded-lg",
					{
						"flex-col items-center": vertical,
						"flex-row": !vertical,
					}
				)}
			>
				{Icon && <Icon className="w-12 h-12" style={{ color: primaryColor }} />}
				<div
					className={classNames("flex flex-col gap-2 justify-center", {
						"items-start": !vertical,
						"items-center": vertical,
					})}
				>
					<h3
						className="text-xl font-bold"
						style={{
							color: titleTextColor,
						}}
					>
						{title}
					</h3>
					<p
						className="text-base"
						style={{
							color: primaryColor,
						}}
					>
						{subtitle}
					</p>
				</div>
			</div>
		</div>
	);
};

export default CheckoutComponentAdvantage;

