"use client";

import { useContext } from 'react';

// TODO: Migrar CheckoutModeContext na Fase 3
// import { CheckoutModeContext } from '@/contexts/CheckoutModeContext';
import type {
  CheckoutComponentAttributesTypes,
  CheckoutComponentType
} from '@/types/builder';
import { CheckoutComponentTypeNames } from '@/types/builder';
// TODO: Migrar CheckoutComponent na Fase 6
// import CheckoutComponent from '../checkout/CheckoutComponent';
import CheckoutComponentAdvantage from './CheckoutComponentAdvantage';
import CheckoutComponentCountdown from './CheckoutComponentCountdown';
import CheckoutComponentFacebook from './CheckoutComponentFacebook';
import CheckoutComponentFakeCheckout from './CheckoutComponentFakeCheckout';
import CheckoutComponentHeader from './CheckoutComponentHeader';
import CheckoutComponentImage from './CheckoutComponentImage';
import CheckoutComponentList from './CheckoutComponentList';
import CheckoutComponentMap from './CheckoutComponentMap';
import CheckoutComponentSeal from './CheckoutComponentSeal';
import CheckoutComponentTestimonial from './CheckoutComponentTestimonial';
import CheckoutComponentText from './CheckoutComponentText';
import CheckoutComponentVideo from './CheckoutComponentVideo';
import CheckoutComponent from '../checkout/CheckoutComponent';

const CheckoutComponentRenderer: React.FC<
  CheckoutComponentType<CheckoutComponentAttributesTypes>
> = ({ attributes, type }) => {
  // TODO: Migrar CheckoutModeContext na Fase 3
  // const { preview } = useContext(CheckoutModeContext);
  const preview = false; // Placeholder até migrar CheckoutModeContext

	const components = {
		[CheckoutComponentTypeNames.TEXT]: CheckoutComponentText,
		[CheckoutComponentTypeNames.IMAGE]: CheckoutComponentImage,
		[CheckoutComponentTypeNames.ADVANTAGE]: CheckoutComponentAdvantage,
		[CheckoutComponentTypeNames.SEAL]: CheckoutComponentSeal,
		[CheckoutComponentTypeNames.HEADER]: CheckoutComponentHeader,
		[CheckoutComponentTypeNames.LIST]: CheckoutComponentList,
		[CheckoutComponentTypeNames.COUNTDOWN]: CheckoutComponentCountdown,
		[CheckoutComponentTypeNames.TESTIMONIAL]: CheckoutComponentTestimonial,
		[CheckoutComponentTypeNames.VIDEO]: CheckoutComponentVideo,
		[CheckoutComponentTypeNames.CHECKOUT]: preview
			? CheckoutComponentFakeCheckout
			: CheckoutComponent,
		[CheckoutComponentTypeNames.FACEBOOK]: CheckoutComponentFacebook,
		[CheckoutComponentTypeNames.MAP]: CheckoutComponentMap
	};

  const Component = components[
    type as CheckoutComponentTypeNames
  ] as React.FC<CheckoutComponentAttributesTypes> | null;

  return (
    <div className={`flex flex-col gap-2`}>
      {Component && <Component {...(attributes as CheckoutComponentAttributesTypes)} />}
    </div>
  );
};

export default CheckoutComponentRenderer;

