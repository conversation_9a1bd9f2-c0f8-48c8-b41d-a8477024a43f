"use client";

import classNames from "classnames";
import type { CheckoutRowType } from "@/types/builder";
import CheckoutColumn from "./CheckoutColumn";

const CheckoutRow: React.FC<CheckoutRowType> = ({ layout, columns }) => {
  return (
    <div className={classNames(`grid grid-cols-12 gap-3`)}>
      {layout.map((column, index) => (
        <div
          key={index}
          className={classNames({
            "col-span-12": column === 12,
            "col-span-8": column === 8,
            "col-span-6": column === 6,
            "col-span-4": column === 4,
            "col-span-3": column === 3,
          })}
        >
          {columns[index] && <CheckoutColumn {...columns[index]} />}
        </div>
      ))}
    </div>
  );
};

export default CheckoutRow;

