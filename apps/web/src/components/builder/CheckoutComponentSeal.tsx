"use client";

import SealOneIllustration from "@/assets/builder/seal/SealOneIllustration";
import SealTwoIllustration from "@/assets/builder/seal/SealTwoIllustration";
import SealThreeIllustration from "@/assets/builder/seal/SealThreeIllustration";
import type { CheckoutComponentSealAttibutesType } from "@/types/builder";
import CheckoutComponentAlignmentContainer from "./CheckoutComponentAlignmentContainer";

const Seal = {
	one: SealOneIllustration,
	two: SealTwoIllustration,
	three: SealThreeIllustration,
};

const CheckoutComponentSeal: React.FC<CheckoutComponentSealAttibutesType> = ({
	...attributes
}) => {
	const SealComponent = Seal[attributes.type] || null;
	return (
		<CheckoutComponentAlignmentContainer alignment={attributes.alignment}>
			<div
				style={{
					width: `${attributes.width}px`,
				}}
				className="flex flex-col items-center"
			>
				{SealComponent && <SealComponent {...attributes} />}
			</div>
		</CheckoutComponentAlignmentContainer>
	);
};

export default CheckoutComponentSeal;

