"use client";

import Link from "next/link";
import useImagePreview from "@/hooks/useImagePreview";
import type { CheckoutComponentImageAttibutesType } from "@/types/builder";
import CheckoutComponentAlignmentContainer from "./CheckoutComponentAlignmentContainer";

const CheckoutComponentImage: React.FC<CheckoutComponentImageAttibutesType> = ({
  alignment,
  image,
  redirectUrl,
}) => {
  const { preview } = useImagePreview({
    image,
  });

  const Image = (
    <img
      src={preview}
      alt={image?.id}
      className="w-full h-full object-cover rounded-md"
    />
  );

  return (
    <CheckoutComponentAlignmentContainer alignment={alignment}>
      {redirectUrl ? (
        <a href={redirectUrl} target="_blank" rel="noopener noreferrer">
          {Image}
        </a>
      ) : (
        <>{Image}</>
      )}
    </CheckoutComponentAlignmentContainer>
  );
};

export default CheckoutComponentImage;

