"use client";

import classNames from "classnames";
import { CheckIcon } from "@heroicons/react/24/solid";
import type { CheckoutComponentListAttibutesType } from "@/types/builder";
import CheckoutComponentAlignmentContainer from "./CheckoutComponentAlignmentContainer";

const CheckoutComponentList: React.FC<CheckoutComponentListAttibutesType> = ({
	alignment,
	backgroundColor,
	fontSize,
	iconColor,
	items,
	showTitle,
	style,
	textColor,
	title,
}) => {
	return (
		<div
			className="flex flex-col p-5 gap-4 rounded-lg"
			style={{
				backgroundColor,
			}}
		>
			{showTitle && (
				<CheckoutComponentAlignmentContainer alignment={alignment}>
					<span
						className="font-bold text-3xl"
						style={{
							color: textColor,
						}}
					>
						{title}
					</span>
				</CheckoutComponentAlignmentContainer>
			)}
			<ul
				className={classNames("flex flex-col gap-5", {
					"list-none": ["check", "none"].includes(style),
					"list-inside list-disc": style === "disc",
					"list-inside list-decimal": style === "decimal",
				})}
				style={{
					fontSize: `${fontSize}px`,
					color: textColor,
				}}
			>
				<CheckoutComponentAlignmentContainer alignment={alignment} className="gap-5">
					{items.map((item, index) => (
						<li
							key={item.id || index}
							style={{
								color: textColor,
							}}
						>
							<div className="flex items-center gap-3">
								{style === "check" && (
									<CheckIcon
										width={fontSize || 18}
										height={fontSize || 18}
										style={{ color: iconColor }}
									/>
								)}
								<span>{item.text}</span>
							</div>
						</li>
					))}
				</CheckoutComponentAlignmentContainer>
			</ul>
		</div>
	);
};

export default CheckoutComponentList;

