"use client";

import type { CheckoutComponentTextAttibutesType } from "@/types/builder";

const CheckoutComponentText: React.FC<CheckoutComponentTextAttibutesType> = ({
  text,
  backgroundColor,
  borderColor,
  borderRadius,
  borderWidth,
}) => {

  return (
    <span
      style={{
        backgroundColor,
        borderColor,
        borderRadius: `${borderRadius}px`,
        borderWidth: `${borderWidth}px`,
      }}
      className="px-3"
      dangerouslySetInnerHTML={{ __html: text }}
    />
  );
};

export default CheckoutComponentText;

