"use client";

import type { CheckoutColumnType } from '@/types/builder';
import CheckoutComponentRenderer from './CheckoutComponentRenderer';

const CheckoutColumn: React.FC<CheckoutColumnType> = ({ components }) => {
  return (
    <div className="flex flex-col gap-3">
      {components?.map((component, index) => (
        <CheckoutComponentRenderer key={component.id} index={index} {...component} />
      ))}
    </div>
  );
};

export default CheckoutColumn;

