"use client";

import { getGoogleMapsApiKey } from "@/lib/env";
import type { CheckoutComponentMapAttibutesType } from "@/types/builder";
import CheckoutComponentAlignmentContainer from "./CheckoutComponentAlignmentContainer";

const CheckoutComponentMap: React.FC<CheckoutComponentMapAttibutesType> = ({
  address,
  alignment,
  width,
}) => {
  const apiKey = getGoogleMapsApiKey();
  
  return (
    <CheckoutComponentAlignmentContainer alignment={alignment}>
      <div
        className="aspect-video max-w-full"
        style={{
          width: `${width}px`,
        }}
      >
        <iframe
          width={"100%"}
          height={"100%"}
          loading="lazy"
          allowFullScreen
          src={`https://www.google.com/maps/embed/v1/place?key=${
            apiKey
          }&q=${encodeURIComponent(address)}`}
        />
      </div>
    </CheckoutComponentAlignmentContainer>
  );
};

export default CheckoutComponentMap;

