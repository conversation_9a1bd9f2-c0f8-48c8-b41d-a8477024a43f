"use client";

import ReactPlayer from "react-player";
import type { CheckoutComponentVideoAttibutesType } from "@/types/builder";
import CheckoutComponentAlignmentContainer from "./CheckoutComponentAlignmentContainer";

const CheckoutComponentVideo: React.FC<CheckoutComponentVideoAttibutesType> = ({
  alignment,
  width,
  hideControls,
  url,
}) => {
  return (
    <CheckoutComponentAlignmentContainer alignment={alignment}>
      <div className="w-full aspect-video max-w-full" style={{ width }}>
        <ReactPlayer
          width="100%"
          height="100%"
          url={url}
          controls={!hideControls}
        />
      </div>
    </CheckoutComponentAlignmentContainer>
  );
};

export default CheckoutComponentVideo;

