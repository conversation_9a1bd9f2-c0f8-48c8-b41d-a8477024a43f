"use client";

import Button from '@/components/ui/button-form';
import useSettings from '@/hooks/useSettings';
import { useTranslation } from '@/hooks/useTranslation';
import { BRANDING } from '@/lib/utils/brand';
import { useEffect, useState } from 'react';
import { QRCode } from 'react-qrcode-logo';



interface PixAutoPaymentProps {
  qrCode: string;
  checkingPayment: boolean;
  onCheckPayment: () => void;
}

const PixAutoPayment = ({
  qrCode,
  checkingPayment,
  onCheckPayment
}: PixAutoPaymentProps) => {
  const [isCopy, setIsCopy] = useState(false);
  const settings = useSettings();
  const { t } = useTranslation();
  const [minutes, setMinutes] = useState(10);
  const [seconds, setSeconds] = useState(0);

  const pixAutoSteps = [
    {
      id: 1,
      icon: '1',
      text: t('payment.pix.step_1')
    },
    {
      id: 2,
      icon: '2',
      text: t('payment.pix.step_2')
    },
    {
      id: 3,
      icon: '3',
      text: t('payment.pix.step_3')
    },
    {
      id: 4,
      icon: '4',
      text: t('payment.pix_auto.step_4')
    }
  ];

  const copyQrCode = async () => {
    if (typeof window === 'undefined') return;
    setIsCopy(true);
    navigator.clipboard.writeText(qrCode);

    await new Promise((r) => setTimeout(r, 2000));
    setIsCopy(false);
  };

  useEffect(() => {
    const calculateTimeLeft = () => {
      if (minutes === 0 && seconds === 0) {
        return;
      }

      if (seconds === 0) {
        setMinutes(minutes - 1);
        setSeconds(59);
      } else {
        setSeconds(seconds - 1);
      }
    };

    const interval = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(interval);
  }, [minutes, seconds]);

  return (
    <div className="flex flex-col justify-center items-center gap-4 w-full">
      <div className="flex flex-col gap-2 w-full">
        <h1 className="text-xl font-semibold text-gray-900">{t('success.pay_now_pix_auto')}</h1>
        <p className="text-gray-500 text-sm">{t('success.finalize_payment_now')}</p>
      </div>
      <div className="mx-6 my-2 w-full px-4 border border-dotted p-4 rounded-md">
        <div className="flex items-center justify-center ">
          <p className="text-gray-700 text-sm">
            {t('success.expires_in_label')}{' '}
            <span className="font-semibold">
              {minutes.toString().padStart(2, '0')}:{seconds.toString().padStart(2, '0')} minutos
            </span>
          </p>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
          <div
            className="bg-gray-600 h-2 rounded-full"
            style={{ width: `${((10 * 60 - minutes * 60 - seconds) / (10 * 60)) * 100}%` }}
          ></div>
        </div>
      </div>

      <div className="flex flex-col items-center px-14 w-fit">
        <div className="w-72 xs:w-52 border border-gray-100 rounded-lg mb-4 p-2 bg-white">
          <QRCode
            value={qrCode}
            size={256}
            logoImage={`/assets/qrcode-${BRANDING}-logo.webp`}
            logoWidth={30}
            logoPadding={5}
            logoPaddingStyle="circle"
            logoHeight={30}
            qrStyle="dots"
            eyeRadius={10}
            removeQrCodeBehindLogo={true}
            style={{ width: '100%', height: 'auto', borderRadius: '0.375rem' }}
          />
        </div>

        <div className="flex flex-col items-center justify-center py-3 px-6 my-2">
          <div className="flex items-center gap-2 text-gray-700 text-sm">
            <img src={'/assets/loader.svg'} className='w-4 h-4' />
            <span>{t('payment.pix_auto.waiting_payment')}</span>
          </div>
          <p className="text-teal-600 text-xs mt-1">{t('payment.pix.approval_time')}</p>
        </div>

        <div className="flex flex-col gap-3 w-80 xs:w-52 mb-2">
          <button
            onClick={copyQrCode}
            className="w-full py-3 px-4 border border-brand-primary bg-white hover:bg-brand-primary-light active:bg-green-100 rounded-md text-gray-700 font-medium flex items-center justify-center gap-2 transition-colors"
            disabled={false}
          >
            {isCopy ? (
              <span className="font-semibold text-brand-primary">{t('payment.pix.code_copied')}</span>
            ) : (
              <div className="flex items-center justify-center gap-2">
                <img className="w-4 h-4" src="/assets/copy-green.svg" alt="icone de copiar" />
                <span className="font-semibold text-brand-primary">{t('payment.pix_auto.copy_code')}</span>
              </div>
            )}
          </button>
          <Button
            style={{
              backgroundColor: settings.payButton.color,
              color: settings.payButton.text.color
            }}
            sizes="sm"
            onClick={onCheckPayment}
            loading={checkingPayment}
            className="hover:opacity-90 transition-opacity"
          >
            {t('success.already_paid')}
          </Button>
        </div>
      </div>

      <div
        style={{ backgroundColor: settings.form?.background.color }}
        className="text-white p-4 rounded-md w-full"
      >
        <div className="space-y-4">
          {pixAutoSteps.map((step) => (
            <div key={step.id} className="flex items-start gap-2">
              <span className="flex items-center justify-center w-7 h-7 bg-indigo-50 text-indigo-700 rounded-full text-sm font-bold shrink-0">
                {step.icon}
              </span>
              <p
                className="text-gray-700 leading-relaxed"
                dangerouslySetInnerHTML={{ __html: step.text }}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PixAutoPayment;

