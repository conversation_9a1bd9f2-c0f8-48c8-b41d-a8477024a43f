"use client";

import Button from '@/components/ui/button-form';
import useSettings from '@/hooks/useSettings';
import { useTranslation } from '@/hooks/useTranslation';
import { useEffect, useState } from 'react';
import { QRCode } from 'react-qrcode-logo';

interface PicPayPaymentProps {
  qrCode: string;
  status: string;
  checkingPayment: boolean;
  onCheckPayment: () => void;
}

const PicPayPayment = ({
  qrCode,
  status,
  checkingPayment,
  onCheckPayment
}: PicPayPaymentProps) => {
  const settings = useSettings();
  const { t } = useTranslation();
  const [minutes, setMinutes] = useState(15);
  const [seconds, setSeconds] = useState(0);

  useEffect(() => {
    const calculateTimeLeft = () => {
      if (minutes === 0 && seconds === 0) {
        return;
      }

      if (seconds === 0) {
        setMinutes(minutes - 1);
        setSeconds(59);
      } else {
        setSeconds(seconds - 1);
      }
    };

    const interval = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(interval);
  }, [minutes, seconds]);

  return (
    <div className="flex flex-col justify-center items-center gap-4 w-full">
      <div className="flex flex-col gap-2 items-center justify-center">
        <h1 className="text-2xl font-bold text-gray-900">{t('success.qrcode_generated')}</h1>
        <p className="text-gray-500 text-sm">{t('success.finalize_payment_now')}</p>
      </div>
      <div className="mx-6 my-2 w-full px-4">
        <div className="flex items-center justify-between">
          <p className="text-gray-700 font-medium">{t('success.expires_in_label')}</p>
          <div className="flex items-center">
            <div className="bg-rose-500 text-white font-bold text-xl rounded-lg w-12 h-12 flex items-center justify-center">
              {minutes}
            </div>
            <span className="mx-1 text-xl font-bold text-gray-700">:</span>
            <div className="bg-rose-500 text-white font-bold text-xl rounded-lg w-12 h-12 flex items-center justify-center">
              {seconds}
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col w-full px-14 mt-3 mb-3 text-center">
        <span style={{ color: settings.text.color.secondary }} className="text-white">
          {t('success.open_picpay_scan')}
        </span>
      </div>

      <div className="w-80 xs:w-52 border-2 border-dashed border-gray-300 rounded-md mb-4">
        <QRCode
          value={qrCode}
          size={256}
          logoImage="/assets/qrcode-cakto-logo.webp"
          logoWidth={30}
          logoPadding={5}
          logoPaddingStyle="circle"
          logoHeight={30}
          qrStyle="dots"
          removeQrCodeBehindLogo={true}
          style={{ width: '100%', height: 'auto', borderRadius: '0.375rem' }}
        />
      </div>

      {status === 'waiting_payment' && (
        <div className="mx-6 bg-gray-50 rounded-lg flex items-center justify-center space-x-1">
          <img src="/assets/loader.svg" className='w-5 h-5' />
          <span className="text-gray-700 font-medium">{t('payment.picpay.waiting_payment')}</span>
        </div>
      )}

      <div className="w-[70%] text-center">
        <span
          className="text-sm text-gray-700 leading-relaxed"
        >
          {t('success.qrcode_problem')}
        </span>
      </div>

      <div className="mt-3 flex flex-col w-80 xs:w-52">
        <Button
          style={{
            backgroundColor: settings.payButton.color,
            color: settings.payButton.text.color
          }}
          sizes="sm"
          onClick={onCheckPayment}
          loading={checkingPayment}
        >
          {t('success.already_paid')}
        </Button>
      </div>
    </div>
  );
};

export default PicPayPayment;

