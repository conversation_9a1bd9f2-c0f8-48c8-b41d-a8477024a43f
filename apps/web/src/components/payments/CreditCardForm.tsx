"use client";

import { useFormContext, useWatch } from "react-hook-form";
import { useCheckout } from "@/contexts";
import { usePrice } from "@/hooks/usePrice";
import { DefaultCreditCardType } from "@/constants/payment";
import {
	getCreditCardCvvMask,
	getCreditCardNumberMask,
	getCreditCardType,
} from "@/lib/utils/payment";
import { useEffect, useMemo } from "react";
import TextField from "@/components/ui/text-field";
import Checkbox from "@/components/ui/checkbox-form";
import Card from "@/components/ui/card-form";
import { SecurityIcon } from "@/components/icons";
import { InstallmentsSelector } from "./InstallmentsSelector";
import { useTranslation } from "@/hooks/useTranslation";

export const CreditCardForm = () => {
	const form = useFormContext();
	const { offer, hasAnySubscription } = useCheckout();
	const prices = usePrice();
	const { t } = useTranslation();

	const priceToCalculateInstallments = hasAnySubscription
		? prices.mainOfferPriceWithDiscount
		: prices.totalPriceWithDiscount;

	const { control, trigger, getFieldState, setValue, getValues, setError, clearErrors } =
		useFormContext();

	const cardNumber = useWatch({ control, name: "cardNumber", defaultValue: "" });
	const cardExpiration = useWatch({ control, name: "cardExpiration", defaultValue: "" });
	const paymentMethod = useWatch({ control, name: "paymentMethod" });
	const isCreditCard = paymentMethod === "credit_card";

	const cardType = useMemo(() => getCreditCardType(cardNumber), [cardNumber]);
	const cardNumberMask = useMemo(() => getCreditCardNumberMask(cardType), [cardType]);
	const cardCvvMask = useMemo(() => getCreditCardCvvMask(cardType), [cardType]);

	useEffect(() => {
		const { error } = getFieldState("cardCvv");
		if (error) trigger("cardCvv");
		const cardCvv = getValues("cardCvv");
		if (cardCvv && cardCvv.length > cardType.code.size) {
			setValue("cardCvv", cardCvv.slice(0, cardType.code.size));
		}
	}, [cardType, getValues, setValue, trigger, getFieldState]);

	useEffect(() => {
		if (!isCreditCard || cardExpiration == "") {
			clearErrors("cardExpiration");
			return;
		}

		const match = (cardExpiration || "").match(/^(0[1-9]|1[0-2])\/(\d{2})$/);
		if (!match && cardExpiration != null) {
			setError("cardExpiration", {
				type: "manual",
				message: t("errors.validation.invalid_date"),
			});
			return;
		}
		const month = parseInt(match[1], 10);
		const year = 2000 + parseInt(match[2], 10);
		const now = new Date();
		const y = now.getFullYear();
		const m = now.getMonth() + 1;
		if (year < y || (year === y && month < m)) {
			setError("cardExpiration", {
				type: "manual",
				message: t("errors.validation.invalid_expiry"),
			});
			return;
		}
		clearErrors("cardExpiration");
	}, [cardExpiration, isCreditCard, setError, clearErrors]);

	return (
		<>
			<Card color="secondary" outline shadow={false} className="w-full">
				<section className="flex flex-col gap-3">
					<TextField
						name="cardNumber"
						label={t("payment.credit_card.card_number")}
						placeholder={t("payment.credit_card.card_number_placeholder")}
						maskChar=""
						mask={cardNumberMask}
						size="sm"
						data-pagarmecheckout-element="number"
						inputMode="numeric"
						type="tel"
						pattern="\d*"
						autoComplete="cc-number"
						actions={
							cardType.type !== DefaultCreditCardType.type && (
								<img
									className="absolute right-2 top-2 w-10 aspect-video"
									src={`/assets/brands/${cardType.type}.svg`}
									alt={cardType.niceType}
								/>
							)
						}
					/>
					<span data-pagarmecheckout-element="brand" style={{ display: "none" }}>
						{cardType.type !== DefaultCreditCardType.type ? cardType.type : ""}
					</span>

					<div className="flex flex-wrap gap-3">
						<div className="flex flex-1 flex-col sm:flex-row gap-3 w-full">
							<div className="flex-1">
								<TextField
									className="w-full"
									name="cardExpiration"
									label={t("payment.credit_card.expiration_date")}
									placeholder={t("payment.credit_card.expiration_placeholder")}
									mask="99/99"
									size="sm"
									data-pagarmecheckout-element="exp_date"
									autoComplete="cc-exp"
								/>
							</div>

							<TextField
								name="cardCvv"
								className="flex-1"
								label={cardType ? cardType.code.name : t("payment.credit_card.cvv")}
								size="sm"
								placeholder={
									cardType
										? "".padStart(cardType.code.size, "0")
										: t("payment.credit_card.cvv_placeholder")
								}
								mask={cardCvvMask}
								maskChar=""
								data-pagarmecheckout-element="cvv"
								autoComplete="cc-csc"
							/>
						</div>

						{!!offer && !offer.paid && (
							<div className="flex w-full md:flex-1">
								<InstallmentsSelector
									itsNew={false}
									offerPaid={!!offer.paid}
									fieldName="installments"
									calculatedInstallmentsFieldName="calculatedInstallments"
									offerId={offer.id}
									offerType={offer.type}
									price={priceToCalculateInstallments}
									recurrencePeriod={offer.recurrence_period}
								/>
							</div>
						)}
					</div>
				</section>

			<Checkbox
				name="saveCard"
				label={t("payment.credit_card.save_card")}
				className="my-5"
				size="md"
			/>

			<div className="mt-1 flex flex-row items-center gap-2">
				<SecurityIcon className="h-5 w-5 text-slate-500" />
				<span className="text-xs font-thin text-slate-500">
					{t("payment.credit_card.secure_payment")}
				</span>
			</div>
		</Card>
		</>
	);
};

