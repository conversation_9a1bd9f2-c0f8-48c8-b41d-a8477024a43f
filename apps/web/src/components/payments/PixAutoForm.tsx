"use client";

import { CheckCircleIcon } from "@heroicons/react/20/solid";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";

export function PixAutoForm() {
	const settings = useSettings();
	const { t } = useTranslation();

	const pixAutoAdvantage = [
		{ message: t('payment.pix.instant_release') },
		{ message: t('payment.pix_auto.description') },
		{ message: t('payment.pix_auto.info_1') },
		{ message: t('payment.pix_auto.info_2') },
	];

	return (
		<>
			<div
				style={{
					borderWidth: "2.3px",
				}}
				className="rounded-md border border-[#0F7864] border-dashed p-3"
			>
				<ul>
					{pixAutoAdvantage.map(({ message }) => (
						<li key={message} className="flex items-center py-1 px-3 xs:px-1">
							<CheckCircleIcon
								className="text-green-600 flex-shrink-0"
								style={{ height: "18px", width: "18px" }}
							/>
							<p
								style={{ color: settings.text.color.primary }}
								className="text-[13px] text-white text-bold ml-2"
							>
								{message}
							</p>
						</li>
					))}
				</ul>
			</div>
		</>
	);
}

