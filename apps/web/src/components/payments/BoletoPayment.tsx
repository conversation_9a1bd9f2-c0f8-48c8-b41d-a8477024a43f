"use client";

import Button from '@/components/ui/button-form';
import useSettings from '@/hooks/useSettings';
import { useTranslation } from '@/hooks/useTranslation';
import { formatPrice } from '@/lib/utils/format';
import moment from 'moment';
import { useState } from 'react';

interface BoletoPaymentProps {
  barcode: string;
  boletoUrl: string;
  amount: number;
  expirationDate: string;
  onOpenBillet: () => void;
}

const BoletoPayment = ({
  barcode,
  amount,
  expirationDate,
  onOpenBillet
}: BoletoPaymentProps) => {
  const [isCopy, setIsCopy] = useState(false);
  const settings = useSettings();
  const { t } = useTranslation();

  const copyBarCode = async () => {
    if (typeof window === 'undefined') return;
    setIsCopy(true);
    navigator.clipboard.writeText(barcode);

    await new Promise((r) => setTimeout(r, 2000));
    setIsCopy(false);
  };

  return (
    <div className="flex flex-col justify-center items-center gap-3 w-full">
      <div className="flex flex-col w-full px-14 mt-3 mb-3 text-center">
        <span className="text-gray-700 leading-relaxed">
          {t('payment.boleto.digital_access_info')}
        </span>
      </div>

      <div className="flex flex-col gap-5 mb-3">
        <div className="flex flex-col px-14 mt-5 text-center w-full">
          <span style={{ color: settings.text.color.secondary }} className="text-white">
            {t('payment.boleto.amount_label')}
          </span>
          <span
            style={{ color: settings.text.color.primary }}
            className="font-bold text-white"
          >
            {formatPrice(amount)}
          </span>
        </div>
        <div className="flex flex-col justify-center text-center">
          <span style={{ color: settings.text.color.secondary }} className="text-white">
            {t('payment.boleto.due_date_label')}
          </span>
          <span
            style={{ color: settings.text.color.primary }}
            className="font-bold text-white"
          >
            {moment(expirationDate, 'YYYY/MM/DD').format('DD/MM/YYYY')}
          </span>
        </div>
        <div className="flex flex-col justify-center text-center">
          <span style={{ color: settings.text.color.secondary }} className="text-white">
            {t('payment.boleto.barcode_number')}
          </span>
          <span
            style={{ color: settings.text.color.primary }}
            className="font-bold sm:text-sm text-xs text-white"
          >
            {barcode}
          </span>
        </div>
      </div>

      <div className="flex justify-center w-full">
        <img
          src={`https://barcode.tec-it.com/barcode.ashx?data=${barcode}&code=Code128&dpi=96&dataseparator=`}
          alt="Boleto"
          className="sm:w-96 h-16 sm:h-24 mx-auto"
        />
      </div>

      <div className="flex flex-col gap-3 w-full justify-center items-center mt-3">
        <button
          onClick={copyBarCode}
          className="w-full py-3 px-4 border border-brand-primary bg-white hover:bg-brand-primary-light active:bg-green-100 rounded-md text-gray-700 font-medium flex items-center justify-center gap-2 transition-colors"
          disabled={false}
        >
          {isCopy ? (
            <span className="font-semibold text-brand-primary">{t('payment.boleto.code_copied')}</span>
          ) : (
            <div className="flex items-center justify-center gap-2">
              <img className="w-4 h-4" src="/assets/copy-green.svg" alt="icone de copiar" />
              <span className="font-semibold text-brand-primary">{t('payment.boleto.copy_code')}</span>
            </div>
          )}
        </button>
        <Button
          style={{
            backgroundColor: settings.payButton.color,
            color: settings.payButton.text.color
          }}
          sizes="sm"
          onClick={onOpenBillet}
        >
          {t('payment.boleto.print_billet')}
        </Button>
      </div>
    </div>
  );
};

export default BoletoPayment;

