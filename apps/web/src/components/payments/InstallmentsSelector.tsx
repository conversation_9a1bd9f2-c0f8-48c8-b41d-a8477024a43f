"use client";

import { useFormContext } from "react-hook-form";
import type { Path } from "react-hook-form";
import { useCheckout } from "@/contexts";
import Select from "@/components/ui/select";
import { useFetchCalculatedInstallments } from "@/hooks/useFetchCalculatedInstallments";
import type { UseFetchCalculatedInstallmentsProps } from "@/hooks/useFetchCalculatedInstallments";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";

type FormValues = {
	installments?: number;
	calculatedInstallments?: any[];
	[key: string]: any;
};

type Props = {
	fieldName: Path<FormValues>;
	calculatedInstallmentsFieldName: Path<FormValues>;
	itsNew: boolean;
	hideLabel?: boolean;
	autoCalc?: boolean;
} & UseFetchCalculatedInstallmentsProps;

export function InstallmentsSelector({
	fieldName,
	calculatedInstallmentsFieldName,
	itsNew,
	hideLabel = false,
	autoCalc = true,
	...installmentsProps
}: Props) {
	const { t } = useTranslation();
	const { setCalcInstallments } = useCheckout();

	const checkoutSettings = useSettings();

	const form = useFormContext<FormValues>();

	const { calculatedInstallments, isLoadingInstallments } =
		useFetchCalculatedInstallments({
			...installmentsProps,
			onCalculate: (calculatedInstallments) => {
				if (autoCalc) {
					setCalcInstallments(calculatedInstallments);
				}
				form.setValue(
					calculatedInstallmentsFieldName,
					calculatedInstallments.map(({ installment, value }) => ({
						installment,
						value,
					})),
				);
			},
		});

	return (
		!installmentsProps.offerPaid && (
			<div
				className="flex w-full md:flex-1 max-w-full"
				onClick={(e) => e.stopPropagation()}
			>
				<div className="w-full">
					{!hideLabel && (
						<div className="flex items-center gap-2 mb-1">
							<span
								className="text-slate-500 text-xs"
								style={{
									color: checkoutSettings.text?.color?.primary,
								}}
							>
								{installmentsProps.offerType === "subscription"
									? t("payment.subscription")
									: t("payment.installments")}
							</span>
							{itsNew && (
								<div
									style={{
										backgroundColor:
											checkoutSettings.paymentOptions.button.selected.background
												.color,
									}}
									className="rounded-md flex items-center justify-center px-1.5 py-0.5 text-[8px] font-medium"
								>
									<span
										style={{
											color:
												checkoutSettings.paymentOptions.button.selected.text.color,
										}}
									>
										{t("payment.new_badge")}
									</span>
								</div>
							)}
						</div>
					)}
					<Select
						name={fieldName}
						className="w-full"
						label=""
						size="sm"
						options={calculatedInstallments.map((installment) => ({
							value: String(installment.installment),
							label: installment.label,
						}))}
						disabled={isLoadingInstallments}
						{...(isLoadingInstallments && {
							options: [{ value: "", label: t("payment.loading_installments") }],
						})}
					/>
				</div>
			</div>
		)
	);
}



