"use client";

import { useTranslation } from "@/hooks/useTranslation";

export const BoletoForm = () => {
	const { t } = useTranslation();

	return (
		<div
			className="flex items-center bg-[#7A4100] text-[#FFF5CC] p-2 rounded-lg"
			role="alert"
		>
			<div className="flex-shrink-0">
				<svg
					fill="currentColor"
					width="32px"
					height="32px"
					viewBox="-4 0 32 32"
					version="1.1"
					xmlns="http://www.w3.org/2000/svg"
					className="box-border h-8 w-8"
				>
					<path d="M0 25.281h0.781v-18.563h-0.781v18.563zM2.344 25.281h1.531v-18.563h-1.531v18.563zM5.406 25.281h1.563v-18.563h-1.563v18.563zM8.5 25.281h3.125v-18.563h-3.125v18.563zM13.156 25.281h2.344v-18.563h-2.344v18.563zM17.031 25.281h1.563v-18.563h-1.563v18.563zM20.125 25.281h0.781v-18.563h-0.781v18.563zM22.469 25.281h1.531v-18.563h-1.531v18.563z" />
				</svg>
			</div>
			<div className="ml-3">
				<p className="text-sm">{t("payment.boleto.instruction_3")}</p>
			</div>
		</div>
	);
};

