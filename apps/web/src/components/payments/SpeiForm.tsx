"use client";

import { useTranslation } from "@/hooks/useTranslation";
import useSettings from "@/hooks/useSettings";
import { CheckCircleIcon, BanknotesIcon } from "@heroicons/react/24/outline";

export function SpeiForm() {
  const { t } = useTranslation();
  const settings = useSettings();

  return (
    <div className="space-y-4">
      {/* Info Box */}
      <div 
        className="p-4 rounded-lg flex items-start gap-3"
        style={{ backgroundColor: settings.box.default.background.color }}
      >
        <BanknotesIcon 
          className="w-6 h-6 flex-shrink-0"
          style={{ color: settings.text.color.active }}
        />
        <div>
          <h3 
            className="font-semibold mb-1 text-base"
            style={{ color: settings.text.color.primary }}
          >
            {t("payment.spei.title")}
          </h3>
          <p 
            className="text-sm"
            style={{ color: settings.text.color.secondary }}
          >
            {t("payment.spei.description")}
          </p>
        </div>
      </div>

      {/* Vantagens */}
      <div className="space-y-3">
        <p 
          className="text-sm font-semibold"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.spei.advantages")}
        </p>
        <ul className="space-y-2">
          {[1, 2, 3].map((num) => (
            <li key={num} className="flex items-start gap-2">
              <CheckCircleIcon 
                className="w-5 h-5 flex-shrink-0 mt-0.5"
                style={{ color: settings.text.color.active }}
              />
              <span 
                className="text-sm"
                style={{ color: settings.text.color.secondary }}
              >
                {t(`payment.spei.advantage_${num}`)}
              </span>
            </li>
          ))}
        </ul>
      </div>

      {/* Confirmação instantânea */}
      <div 
        className="p-4 rounded-lg flex items-center gap-3"
        style={{ 
          backgroundColor: settings.box.selected.background.color,
        }}
      >
        <span className="text-2xl">⚡</span>
        <p 
          className="text-sm font-medium"
          style={{ color: settings.text.color.active }}
        >
          {t("payment.spei.instant_confirmation")}
        </p>
      </div>
    </div>
  );
}
