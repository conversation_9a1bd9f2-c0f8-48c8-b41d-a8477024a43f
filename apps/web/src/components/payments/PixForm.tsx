"use client";

import { CheckCircleIcon } from "@heroicons/react/20/solid";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";

export function PixForm() {
	const settings = useSettings();
	const { t } = useTranslation();

	const pixAdvantage = [
		{ message: t("payment.pix.instant_release") },
		{ message: t("payment.pix.easy_to_use") },
	];

	return (
		<>
			<div
				style={{
					borderWidth: "2.3px",
				}}
				className="rounded-md border border-[#0F7864] border-dashed border border- p-3"
			>
				<ul>
					{pixAdvantage.map(({ message }) => (
						<li key={message} className="flex items-center py-1 px-3 xs:px-1">
							<CheckCircleIcon
								className="text-green-600 flex-shrink-0"
								style={{ height: "18px", width: "18px" }}
							/>
							<p
								style={{ color: settings.text.color.primary }}
								className="text-[14px] text-white text-bold leading-[24px] ml-2"
							>
								{message}
							</p>
						</li>
					))}
				</ul>
			</div>
		</>
	);
}

