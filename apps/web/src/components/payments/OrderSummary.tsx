"use client";

import CouponFormNew from "./CouponFormNew";
import OrderItemResume from "./OrderItemResume";
import Divider from "@/components/ui/divider";
import { useCheckout } from "@/contexts";
import useSettings from "@/hooks/useSettings";
import type { BumpData } from "@/types";
import { formatPrice } from "@/lib/utils/format";
import { useFormContext, useWatch } from "react-hook-form";
import { usePrice } from "@/hooks/usePrice";
import { useTranslation } from "@/hooks/useTranslation";
import type { ReactElement } from "react";

function ServiceFeeTooltip({ children }: { children: React.ReactNode }) {
	const { t } = useTranslation();
	return <span title={t("checkout.service_fee_tooltip")}>{children}</span>;
}

type FormValues = {
	bumps?: BumpData[];
	installments?: number;
	[key: string]: any;
};

export default function OrderSummary() {
	const {
		offer,
		couponData,
		applyCouponValue,
		isFetching,
		canBePaidInInstallments,
		calcInstallments,
	} = useCheckout();

	const settings = useSettings();
	const form = useFormContext<FormValues>();
	const { t } = useTranslation();

	// Validação: retorna null se offer não estiver carregado
	if (!offer || !offer.product) {
		return null;
	}

	const bumps = useWatch({
		control: form.control,
		name: "bumps",
		defaultValue: [],
	}) as BumpData[];

	const price = usePrice();

	const totalPriceWithDiscount = Number(price?.totalPriceWithDiscount ?? 0);
	const totalDiscount = Number(price?.totalDiscount ?? 0);
	const mainOfferFinalPriceWithFees = Number(price?.mainOfferFinalPriceWithFees ?? 0);
	const serviceFeeValue = Number(price?.serviceFeeValue ?? 0);
	const hasServiceFee = Boolean(price?.hasServiceFee);

	const selectedInstallments =
		Number(useWatch({ control: form.control, name: "installments", defaultValue: 1 })) ||
		1;

	const selected: any =
		(calcInstallments as any)?.find?.(
			(i: any) => Number(i.installment) === Number(selectedInstallments),
		) || {};

	const hasCheckedBump = bumps?.some((b) => b.checked) ?? false;

	const selectedTotalWithFee =
		Number(selected?.total || 0) + (hasServiceFee ? Number(serviceFeeValue || 0) : 0);

	const recalculatedLabel =
		selectedInstallments > 0
			? `${selectedInstallments}x de ${formatPrice(selectedTotalWithFee / selectedInstallments)}`
			: selected?.label;

	return (
		<>
			<div className="flex font-bold items-center my-4">
				<span style={{ color: settings.text?.color.primary }}>
					{t("checkout.order_summary")}
				</span>
			</div>

			<div
				className="p-6 rounded-t-lg w-full mx-auto relative"
				style={{ backgroundColor: settings.box.default.background.color }}
			>
				{offer?.product.showCouponField && (
					<>
						<CouponFormNew />
						<Divider />
					</>
				)}

				{offer && !!Object.keys(offer).length && (
					<OrderItemResume
						isLoading={isFetching}
					image={offer?.product?.image}
					offerPrice={offer?.price}
					title={offer?.product?.name}
					type={offer?.type}
					recurrencePeriod={offer?.recurrence_period}
					calculatedInstallments={calcInstallments as any}
					installments={selectedInstallments}
				/>
				)}

					{(hasCheckedBump || applyCouponValue > 0) && (
						<>
							{bumps?.reduce<ReactElement[]>((acc, bump, index) => {
							if (bump.checked) {
								acc.push(
									<div key={bump.id} className="my-1">
										<OrderItemResume
											isLoading={isFetching}
											image={bump.image}
											offerPrice={bump?.offer?.price}
											title={bump.title}
											type={bump?.offer?.type}
											recurrencePeriod={bump?.offer?.recurrence_period}
											calculatedInstallments={
												offer?.product.bumps.find(({ id }) => id === bump.id)
													?.calculatedInstallments
											}
											installments={form.getValues(`bumps.${index}.installments`)}
											withoutDiscount
											isBumpItem
										/>
									</div>
								);
							}
							return acc;
						}, [])}

					{applyCouponValue > 0 && (
						<div className="flex justify-between">
							<span className="text-[#38CA4F]">
								{t("checkout.coupon_applied")} ({couponData?.discount}%)
							</span>
							<span className="text-[#38CA4F]">-{formatPrice(totalDiscount)}</span>
						</div>
					)}
					</>
				)}

			{hasServiceFee && (
				<div className="flex justify-between items-center py-2 px-1 mt-2 text-xs">
					<ServiceFeeTooltip>
						<span className="flex items-center gap-1 hover:opacity-80 transition-opacity text-gray-700">
							{t("checkout.service_fee")}
							<div className="flex items-center justify-center">
								<svg
									className="w-4 h-4 text-gray-400"
									fill="currentColor"
									viewBox="0 0 20 20"
								>
									<path
										fillRule="evenodd"
										d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"
										clipRule="evenodd"
									/>
								</svg>
							</div>
						</span>
					</ServiceFeeTooltip>
					<span className="font-medium text-gray-700">
						{formatPrice(serviceFeeValue)}
					</span>
				</div>
			)}

			<Divider />

			<div className="flex justify-between mt-4">
				<span
					style={{ color: settings.box.default.text.color.primary }}
					className="text-white"
				>
					{t("checkout.total")}
				</span>
				<span
					style={{ color: settings.box.default.text.color.primary }}
					className="text-white"
				>
					{canBePaidInInstallments && selected
						? recalculatedLabel
						: formatPrice(
								Number(totalPriceWithDiscount || mainOfferFinalPriceWithFees || 0) +
									(hasServiceFee ? Number(serviceFeeValue) : 0),
							)}
				</span>
			</div>

				<div className="h-4 absolute bottom-0 left-0 overflow-hidden right-0">
					<div
						className="h-8 absolute left-0 right-0"
						style={{
							backgroundImage: `linear-gradient(135deg, transparent 50%, ${settings.form?.background.color} 50%), linear-gradient(45deg, ${settings.form?.background.color} 50%, transparent 50%)`,
							backgroundSize: "16px 16px",
							backgroundPosition: "bottom",
							transform: "translateY(4px)",
						}}
					/>
				</div>
			</div>
		</>
	);
}

