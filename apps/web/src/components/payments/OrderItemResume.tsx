"use client";

import useSettings from "@/hooks/useSettings";
import { formatPrice } from "@/lib/utils/format";
import { getRecurrencePeriodLabel } from "@/lib/utils/payment";
import { useMemo } from "react";

type OrderItemResumeProps = {
	isLoading?: boolean;
	image?: string;
	offerPrice?: number;
	title?: string;
	type?: string;
	recurrencePeriod?: number;
	calculatedInstallments?: any[];
	installments?: number;
	isBumpItem?: boolean;
	withoutDiscount?: boolean;
};

const OrderItemResume: React.FC<OrderItemResumeProps> = ({
	isLoading,
	image,
	offerPrice = 0,
	title = "Produto",
	type,
	recurrencePeriod,
}) => {
	const settings = useSettings();

	const price = useMemo(() => {
		if (type === "subscription") {
			return `${formatPrice(offerPrice)} / ${getRecurrencePeriodLabel(recurrencePeriod || 30)}`;
		}

		return `${formatPrice(offerPrice)}`;
	}, [type, recurrencePeriod, offerPrice]);

	return (
		<div
			style={{ backgroundColor: settings.box.selected.background.color }}
			className="cursor-pointer ease-in-out duration-150 rounded-md overflow-hidden shadow-md transition w-full"
		>
			{isLoading ? (
				<div className="flex items-center ">
					<div className="flex-shrink-0 p-2">
						<svg
							className="w-14 h-14 object-cover rounded-md mr-4 dark:bg-gray-300  bg-gray-200 animate-pulse"
							aria-hidden="true"
							xmlns="http://www.w3.org/2000/svg"
							fill="currentColor"
							viewBox="-1 -3 18 26"
						>
							<path d="M14.066 0H7v5a2 2 0 0 1-2 2H0v11a1.97 1.97 0 0 0 1.934 2h12.132A1.97 1.97 0 0 0 16 18V2a1.97 1.97 0 0 0-1.934-2ZM10.5 6a1.5 1.5 0 1 1 0 2.999A1.5 1.5 0 0 1 10.5 6Zm2.221 10.515a1 1 0 0 1-.858.485h-8a1 1 0 0 1-.9-1.43L5.6 10.039a.978.978 0 0 1 .936-.57 1 1 0 0 1 .9.632l1.181 2.981.541-1a.945.945 0 0 1 .883-.522 1 1 0 0 1 .879.529l1.832 3.438a1 1 0 0 1-.031.988Z" />
							<path d="M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.98 2.98 0 0 0 .13 5H5Z" />
						</svg>
					</div>
					<div className="flex justify-between items-center p-3 w-full">
						<div className="h-[15px] bg-gray-200 rounded-full dark:bg-gray-300 w-28 animate-pulse"></div>

						<div className="h-[15px] bg-gray-200 rounded-full dark:bg-gray-300 w-10 animate-pulse"></div>
					</div>
				</div>
			) : (
				<div className="flex items-center ">
					<div className="flex-shrink-0">
						{image && (
							<img
								src={image}
								alt="Imagem do produto"
								className="h-16 w-16 object-cover rounded-sm"
							/>
						)}
					</div>
					<div className="flex justify-between items-center p-3 w-full">
						<span
							style={{ color: settings.box.selected.text.color.primary }}
							className="font-bold text-xs sm:text-sm text-white"
						>
							{title.trim() || "Nome do Produto"}
						</span>
						<span
							style={{ color: settings.box.selected.text.color.primary }}
							className="font-bold text-xs sm:text-sm text-white"
						>
							{price}
						</span>
					</div>
				</div>
			)}
		</div>
	);
};

export default OrderItemResume;

