"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { useFormContext } from "react-hook-form";
import { useCheckout } from "@/contexts";
import TextField from "@/components/ui/text-field";
import { TicketIcon } from "@heroicons/react/20/solid";
import { useTranslation } from "@/hooks/useTranslation";
import { Button } from "../ui/button";

export default function CouponFormNew() {
	const searchParams = useSearchParams();
	const couponParam = searchParams.get("coupon") || undefined;

	const form = useFormContext();
	const { couponData, validateCoupon, applyCouponValue, setApplyCouponValue } =
		useCheckout();
	const { t } = useTranslation();

	const [coupon, setCoupon] = useState<string>("");

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		form.clearErrors("coupon");
		setCoupon(e.target.value);
	};

	const handleApplyCoupon = () => {
		if (coupon && !applyCouponValue) {
			validate(coupon);
		}
	};

	const handleRemoveCoupon = () => {
		setCoupon("");
		form.setValue("coupon", undefined);
		setApplyCouponValue(0);
	};

	async function validate(coupon: string) {
		try {
			await validateCoupon(coupon);
			form.clearErrors("coupon");
		} catch (error: any) {
			form.setError("coupon", {
				message: error?.response?.data?.detail || t("errors.validation.generic"),
			});
			setApplyCouponValue(0);
		}
	}

	useEffect(() => {
		if (couponData?.code) {
			setCoupon(couponData.code);
			form.setValue("coupon", couponData.code);
		}
	}, [couponData, form]);

	useEffect(() => {
		if (couponParam) {
			setCoupon(couponParam);
			form.setValue("coupon", couponParam);
			validate(couponParam);
		}
	}, [couponParam]);

	return (
		<div className="flex flex-col gap-2">
			{applyCouponValue > 0 ? (
				<div className="flex items-center justify-between p-2 bg-brand-primary-light rounded-md">
					<div className="flex items-center gap-2">
						<TicketIcon className="w-5 h-5 text-brand-primary" />
						<span className="text-sm text-brand-primary">
							{t("checkout.coupon_applied")} {couponData?.code} ({couponData?.discount}%)
						</span>
					</div>
					<button
						type="button"
						onClick={handleRemoveCoupon}
						className="text-sm text-brand-primary hover:text-brand-primary-hover"
					>
						{t("common.remove")}
					</button>
				</div>
			) : (
				<div className="flex gap-2 items-center">
					<TextField
						name="coupon"
						label={t("checkout.coupon_code")}
						placeholder={t("checkout.coupon_placeholder")}
						size="sm"
						value={coupon}
						onChange={handleChange}
					/>
					<Button
						type="button"
						onClick={handleApplyCoupon}
						className="px-4 cursor-pointer "
					>
						{t("common.apply")}
					</Button>
				</div>
			)}
		</div>
	);
}

