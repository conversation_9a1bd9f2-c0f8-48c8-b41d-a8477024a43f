"use client";

import Card from '@/components/ui/card-form';
import { useCheckout } from '@/contexts';
import useDevice from '@/hooks/useDevice';
import useSettings from '@/hooks/useSettings';
import { useTranslation } from '@/hooks/useTranslation';
import { useEffect } from 'react';
import Confetti from 'react-confetti';
import BoletoPayment from './BoletoPayment';
import PicPayPayment from './PicPayPayment';
import PixPayment from './PixPayment';
import PixAutoPayment from './PixAutoPayment';
import { getPixCheckInterval, getPixCheckDuration } from '@/lib/env';

const WaitingPayment = () => {
  const { firstPayment, checkPayment, checkingPayment } = useCheckout();
  const { device } = useDevice();
  const settings = useSettings();
  const { t } = useTranslation();

  if (!firstPayment) {
    return null;
  }

  const handleCheckPayment = () => {
    checkPayment().catch((error) => {
      console.error(t('errors.payment.check_error'), error);
    });
  };

  const openBillet = () => {
    if (firstPayment?.boleto?.boletoUrl) {
      window.location.href = firstPayment?.boleto?.boletoUrl;
    }
  };

  useEffect(() => {
    if (device === 'mobile' && firstPayment?.picpay?.paymentURL) {
      window.location.href = firstPayment?.picpay?.paymentURL;
    }
  }, [device, firstPayment]);

  useEffect(() => {
    const hasPixQrCode = (firstPayment?.paymentMethod === 'pix' && firstPayment?.pix?.qrCode) ||
                         (firstPayment?.paymentMethod === 'pix_auto' && (firstPayment?.pix_auto?.qrCode || firstPayment?.pix?.qrCode));

    if (!hasPixQrCode) return;
    if (firstPayment.status !== 'waiting_payment') return;

    const intervalTime = getPixCheckInterval();
    const totalDuration = getPixCheckDuration();

    const interval = setInterval(() => {
      checkPayment().catch((error) => {
        console.error(t('errors.payment.polling_error'), error);
      });
    }, intervalTime);

    const timeout = setTimeout(() => {
      clearInterval(interval);
    }, totalDuration);

    return () => {
      clearInterval(interval);
      clearTimeout(timeout);
    };
  }, [firstPayment?.status, firstPayment?.paymentMethod, firstPayment?.pix?.qrCode, firstPayment?.pix_auto?.qrCode, checkPayment]);

  if (typeof window === 'undefined') {
    return null;
  }

  return (
    <>
      <Confetti
        width={window.innerWidth}
        height={window.innerHeight}
        numberOfPieces={300}
        gravity={0.6}
        run={true}
        recycle={false}
        tweenDuration={2000}
      />

      <Card
        style={{
          backgroundColor: settings.form?.background.color
        }}
        borderEffect={`${((firstPayment?.paymentMethod === 'pix' && !!firstPayment?.pix?.qrCode) ||
                          (firstPayment?.paymentMethod === 'pix_auto' && !!firstPayment?.pix_auto?.qrCode)) && 'border-wave-effect'}`}
      >
        <div className="flex flex-col gap-3 items-center w-full">
          <section className="flex justify-center w-full gap-3 mt-2">
            {/* Lógica inteligente: mostrar o componente correto baseado no método, mesmo se o QR Code estiver em outro campo */}
            {firstPayment?.paymentMethod === 'pix' && !!firstPayment?.pix?.qrCode && (
              <PixPayment
                qrCode={firstPayment.pix.qrCode}
                status={firstPayment.status}
                checkingPayment={checkingPayment}
                onCheckPayment={handleCheckPayment}
              />
            )}
            {firstPayment?.paymentMethod === 'pix_auto' && (!!firstPayment?.pix_auto?.qrCode || !!firstPayment?.pix?.qrCode) && (
              <PixAutoPayment
                qrCode={(firstPayment.pix_auto?.qrCode || firstPayment.pix?.qrCode) || ''}
                checkingPayment={checkingPayment}
                onCheckPayment={handleCheckPayment}
              />
            )}
            {!!firstPayment?.boleto?.barcode && (
              <BoletoPayment
                barcode={firstPayment.boleto.barcode}
                boletoUrl={firstPayment.boleto.boletoUrl}
                amount={firstPayment.amount}
                expirationDate={firstPayment.boleto.expirationDate}
                onOpenBillet={openBillet}
              />
            )}
            {!!firstPayment?.picpay?.qrCode && (
              <PicPayPayment
                qrCode={firstPayment.picpay.qrCode}
                status={firstPayment.status}
                checkingPayment={checkingPayment}
                onCheckPayment={handleCheckPayment}
              />
            )}
          </section>
        </div>
      </Card>
    </>
  );
};

export default WaitingPayment;

