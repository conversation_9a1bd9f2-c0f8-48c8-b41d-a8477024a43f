"use client";

import { useFormContext } from "react-hook-form";
import { useCheckout } from "@/contexts";
import { usePrice } from "@/hooks/usePrice";
import { canPaymentMethodBePaidInInstallments } from "@/lib/utils/payment";
import { InstallmentsSelector } from "./InstallmentsSelector";

export function ApplePayForm() {
	const form = useFormContext();

	const { offer, hasAnySubscription } = useCheckout();

	const prices = usePrice();

	// it will be removed when the api process all products in a single payment
	const priceToCalculateInstallments = hasAnySubscription
		? prices.mainOfferPriceWithDiscount
		: prices.totalPriceWithDiscount;

	if (!canPaymentMethodBePaidInInstallments("applepay")) {
		return null;
	}

	return (
		!!offer && (
			<div className="flex w-full md:flex-1">
				<InstallmentsSelector
					itsNew
					offerPaid={!!offer.paid}
					fieldName="installments"
					calculatedInstallmentsFieldName="calculatedInstallments"
					offerId={offer.id}
					offerType={offer.type}
					price={priceToCalculateInstallments}
					recurrencePeriod={offer.recurrence_period}
				/>
			</div>
		)
	);
}

