"use client";

import { useEffect, useMemo } from 'react';
import { useCheckout } from '@/contexts';
import useSettings from '@/hooks/useSettings';
import { useTranslation } from '@/hooks/useTranslation';
import { adjustColor } from '@/lib/utils/colors';
import Button from '@/components/ui/button-form';
import Card from '@/components/ui/card-form';
import { getQueryParams } from '@/lib/utils/url';
import posthog from 'posthog-js';
import { trackEvent, trackPageView } from '@/lib/utils/analytics';
import PixAutoSuccessPayment from './PixAutoSuccessPayment';
import { getCaktoUrl } from '@/lib/env';

const SuccessPayment = () => {
  const { offer, firstPayment, setFirstPayment } = useCheckout();
  const { t } = useTranslation();
  const settings = useSettings();

  useEffect(() => {
    trackPageView('checkout_success');

    if (offer?.product && firstPayment) {
      trackEvent('purchase_success', {
        product_id: offer.product.short_id,
        product_name: offer.product.name,
        payment_method: firstPayment.paymentMethod,
        order_id: firstPayment.id,
        total_value: firstPayment.amount
      });
    }
  }, [offer, firstPayment]);

  const cacktoUrl = getCaktoUrl() || '';

  const handleAccessProduct = () => {
    let url = '';
    if (firstPayment?.accessToken) {
      url = `${cacktoUrl}/auth/login?accessToken=${firstPayment?.accessToken}&refreshToken=${firstPayment?.refreshToken ?? ''}`;
    } else {
      url = `${cacktoUrl}/auth/login`;
    }

    posthog.capture('product_access_click', {
      product_id: offer?.product.short_id,
      product_name: offer?.product.name
    });

    window.location.href = url;
  };

  const handleAccessUpsell = () => {
    if (offer?.product.upsellPage?.length) {
      const currentUrl = window.location.href;
      const queryParams = getQueryParams(currentUrl);
      const upsellUrl = new URL(offer.product.upsellPage);

      Object.keys(queryParams).forEach((key) => {
        upsellUrl.searchParams.append(key, queryParams[key]);
      });

      posthog.capture('upsell_click', {
        product_id: offer.product.short_id,
        product_name: offer.product.name,
        upsell_url: upsellUrl.toString()
      });

      window.location.href = upsellUrl.toString();
    }
  };

  const handleRedoPayment = () => {
    posthog.capture('redo_payment_click', {
      product_id: offer?.product.short_id,
      product_name: offer?.product.name,
      failed_items: firstPayment?.errors?.map(error => ({
        id: error.id,
        name: error.offerName
      }))
    });

    setFirstPayment({
      status: 'redo_payment'
    });
  };

  const hideUpsellButton = useMemo(() => {
    if (!offer?.product.upsell || offer?.product.redirectUpsellWithBumpFail) return true;
    return !firstPayment?.errors?.length;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [firstPayment, offer?.product.upsell]);

  const isExternalProduct = useMemo(() => {
    return offer?.product.contentDelivery === 'external';
  }, [offer]);

  const tableHeadStyle = {
    backgroundColor: adjustColor(settings.form.background.color, 2),
    color: settings.text.color.primary,
    borderColor: adjustColor(settings.form.background.color, 20)
  };

  const tableBodyStyle = {
    borderColor: adjustColor(settings.form.background.color, 20)
  };

  if (firstPayment?.paymentMethod === 'pix_auto') {
    return (
      <PixAutoSuccessPayment
        payment={firstPayment}
        onContinue={handleAccessProduct}
      />
    );
  }

  return (
    <Card
      style={{
        backgroundColor: settings.form?.background.color
      }}
    >
      <div
        style={{
          backgroundColor: settings.form?.background.color
        }}
        className="bg-white p-6  md:mx-auto"
      >
        <svg viewBox="0 0 24 24" className="text-green-600 w-16 h-16 mx-auto my-6">
          <path
            fill="currentColor"
            d="M12,0A12,12,0,1,0,24,12,12.014,12.014,0,0,0,12,0Zm6.927,8.2-6.845,9.289a1.011,1.011,0,0,1-1.43.188L5.764,13.769a1,1,0,1,1,1.25-1.562l4.076,3.261,6.227-8.451A1,1,0,1,1,18.927,8.2Z"
          ></path>
        </svg>
        <div className="flex w-full flex-col items-center">
          <h1 className="text-center text-2xl font-bold text-green-600 ">
            Sua compra do produto
            <br />'{offer?.product?.name}'
            {firstPayment?.paymentMethod !== 'credit_card' &&
              ` com o código: '${firstPayment?.orders?.find((item) => item)?.refId}' `}
            foi aprovada!
          </h1>
          {firstPayment?.paymentMethod === 'credit_card' && (
            <>
              <span style={{ color: settings.text.color.secondary }} className="mt-5 text-center">
                A cobrança aparecerá na sua fatura como: <br />
                <span className="font-bold">CAKTO*{offer?.product?.invoiceDescription}</span>
              </span>

              <h3
                style={{ color: settings.text.color.primary }}
                className="mt-5 text-center text-xl font-bold"
              >
                {t('success.purchase_details')}
              </h3>
            </>
          )}

          <table
            className={`mt-5 border-collapse w-full
          ${firstPayment?.paymentMethod !== 'credit_card' && 'hidden'}`}
          >
            <thead>
              <tr>
                <th
                  style={tableHeadStyle}
                  className="p-3 font-bold uppercase bg-gray-200 text-gray-600 border border-gray-300 hidden lg:table-cell"
                >
                  Produto
                </th>
                <th
                  style={tableHeadStyle}
                  className="p-3 font-bold uppercase bg-gray-200 text-gray-600 border border-gray-300 hidden lg:table-cell"
                >
                  {t('success.code_label')}
                </th>
                <th
                  style={tableHeadStyle}
                  className="p-3 font-bold uppercase bg-gray-200 text-gray-600 border border-gray-300 hidden lg:table-cell"
                >
                  Preço
                </th>
                <th
                  style={tableHeadStyle}
                  className="p-3 font-bold uppercase bg-gray-200 text-gray-600 border border-gray-300 hidden lg:table-cell"
                >
                  Status
                </th>
                <th
                  style={tableHeadStyle}
                  className="p-3 font-bold uppercase bg-gray-200 text-gray-600 border border-gray-300 hidden lg:table-cell"
                >
                  Motivo
                </th>
              </tr>
            </thead>
            <tbody>
              {firstPayment?.success?.map((offer) => (
                <tr
                  key={offer.id}
                  style={{ backgroundColor: settings.form.background.color }}
                  className="bg-white lg:hover:bg-gray-100 flex lg:table-row flex-row lg:flex-row flex-wrap lg:flex-no-wrap mb-10 lg:mb-0"
                >
                  <td
                    style={tableBodyStyle}
                    className="w-full lg:w-auto p-3 text-gray-800 text-center border border-b block lg:table-cell relative lg:static"
                  >
                    <span
                      style={tableHeadStyle}
                      className="lg:hidden absolute top-0 left-0 bg-blue-200 px-2 py-1 text-xs font-bold uppercase"
                    >
                      Produto
                    </span>
                    <p
                      style={{
                        color: settings.text.color.secondary
                      }}
                      className="max-sm:mt-4"
                    >
                      {offer.offerName}
                    </p>
                  </td>

                  <td
                    style={tableBodyStyle}
                    className="w-full lg:w-auto p-3 text-gray-800 text-center border border-b block lg:table-cell relative lg:static"
                  >
                    <span
                      style={tableHeadStyle}
                      className="lg:hidden absolute top-0 left-0 bg-blue-200 px-2 py-1 text-xs font-bold uppercase"
                    >
                      {t('success.code_label')}
                    </span>
                    <p
                      style={{
                        color: settings.text.color.secondary
                      }}
                      className="max-sm:mt-4"
                    >
                      {offer.refId}
                    </p>
                  </td>

                  <td
                    style={tableBodyStyle}
                    className="w-full lg:w-auto p-3 text-gray-800 border border-b text-center block lg:table-cell relative lg:static"
                  >
                    <span
                      style={tableHeadStyle}
                      className="lg:hidden absolute top-0 left-0 bg-blue-200 px-2 py-1 text-xs font-bold uppercase"
                    >
                      Preço
                    </span>
                    <p
                      style={{
                        color: settings.text.color.secondary
                      }}
                      className="max-sm:mt-4"
                    >
                      {offer.price}
                    </p>
                  </td>

                  <td
                    style={tableBodyStyle}
                    className="w-full lg:w-auto p-3 text-gray-800 border border-b text-center block lg:table-cell relative lg:static"
                  >
                    <span
                      style={tableHeadStyle}
                      className="lg:hidden absolute top-0 left-0 bg-blue-200 px-2 py-1 text-xs font-bold uppercase"
                    >
                      Status
                    </span>
                    <span className="rounded bg-green-400 py-1 px-3 text-xs font-bold max-sm:mt-4">
                      Aprovado
                    </span>
                  </td>

                  <td
                    style={tableBodyStyle}
                    className="w-full lg:w-auto p-3 text-gray-800 text-center border border-b block lg:table-cell relative lg:static"
                  >
                    <span
                      style={tableHeadStyle}
                      className="lg:hidden absolute top-0 left-0 bg-blue-200 px-2 py-1 text-xs font-bold uppercase"
                    >
                      Motivo
                    </span>
                    <p
                      style={{
                        color: settings.text.color.secondary
                      }}
                      className="max-sm:mt-4"
                    >
                      {offer.reason}
                    </p>
                  </td>
                </tr>
              ))}

              {firstPayment?.errors?.map((offer) => (
                <tr
                  style={{ backgroundColor: settings.form.background.color }}
                  key={offer.id}
                  className="bg-white lg:hover:bg-gray-100 flex lg:table-row flex-row lg:flex-row flex-wrap lg:flex-no-wrap mb-10 lg:mb-0"
                >
                  <td
                    style={tableBodyStyle}
                    className="w-full lg:w-auto p-3 text-gray-800 text-center border border-b block lg:table-cell relative lg:static"
                  >
                    <span
                      style={tableHeadStyle}
                      className="lg:hidden absolute top-0 left-0 bg-blue-200 px-2 py-1 text-xs font-bold uppercase"
                    >
                      Produto
                    </span>
                    <p
                      style={{
                        color: settings.text.color.secondary
                      }}
                      className="max-sm:mt-4"
                    >
                      {offer.offerName}
                    </p>
                  </td>

                  <td
                    style={tableBodyStyle}
                    className="w-full lg:w-auto p-3 text-gray-800 text-center border border-b block lg:table-cell relative lg:static"
                  >
                    <span
                      style={tableHeadStyle}
                      className="lg:hidden absolute top-0 left-0 bg-blue-200 px-2 py-1 text-xs font-bold uppercase"
                    >
                      {t('success.code_label')}
                    </span>
                    <p
                      style={{
                        color: settings.text.color.secondary
                      }}
                      className="max-sm:mt-4"
                    >
                      {offer.refId}
                    </p>
                  </td>

                  <td
                    style={tableBodyStyle}
                    className="w-full lg:w-auto p-3 text-gray-800 border border-b text-center block lg:table-cell relative lg:static"
                  >
                    <span
                      style={tableHeadStyle}
                      className="lg:hidden absolute top-0 left-0 bg-blue-200 px-2 py-1 text-xs font-bold uppercase"
                    >
                      Preço
                    </span>
                    <p
                      style={{
                        color: settings.text.color.secondary
                      }}
                      className="max-sm:mt-4"
                    >
                      {offer.price}
                    </p>
                  </td>

                  <td
                    style={tableBodyStyle}
                    className="w-full lg:w-auto p-3 text-gray-800 border border-b text-center block lg:table-cell relative lg:static"
                  >
                    <span
                      style={tableHeadStyle}
                      className="lg:hidden absolute top-0 left-0 bg-blue-200 px-2 py-1 text-xs font-bold uppercase"
                    >
                      Status
                    </span>
                    <span className="rounded bg-red-400 py-1 px-3 text-xs font-bold">Recusado</span>
                  </td>

                  <td
                    style={tableBodyStyle}
                    className="w-full lg:w-auto p-3 text-gray-800 text-center border border-b block lg:table-cell relative lg:static"
                  >
                    <span
                      style={tableHeadStyle}
                      className="lg:hidden absolute top-0 left-0 bg-blue-200 px-2 py-1 text-xs font-bold uppercase"
                    >
                      Motivo
                    </span>
                    <p
                      style={{
                        color: settings.text.color.secondary
                      }}
                      className="max-sm:mt-4"
                    >
                      {offer.reason}
                    </p>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {!!firstPayment?.errors?.length && (
            <div className="mt-4 w-[40%] max-sm:w-full ">
              <Button
                style={{
                  backgroundColor: settings.payButton.color,
                  color: settings.payButton.text.color
                }}
                onClick={handleRedoPayment}
                className="w-full"
              >
                PAGAR ITENS RECUSADOS
              </Button>
            </div>
          )}

          {!hideUpsellButton && (
            <div className="mt-4 w-[40%] max-sm:w-full">
              <Button
                style={{
                  backgroundColor: settings.payButton.color,
                  color: settings.payButton.text.color
                }}
                onClick={handleAccessUpsell}
                className="w-full"
              >
                CONTINUAR
              </Button>
            </div>
          )}

          <h3
            style={{ color: settings.text.color.primary }}
            className="mt-5 text-center text-xl font-bold"
          >
            Acesso ao produto
          </h3>

          {!isExternalProduct && (
            <>
              <span style={{ color: settings.text.color.secondary }} className="mt-1 text-center">
                Para acessar o conteúdo do produto, clique no botão abaixo:
              </span>

              <div className="mt-4 w-[40%] max-sm:w-full">
                <Button
                  style={{
                    backgroundColor: settings.payButton.color ?? '#0B6856',
                    color: settings.payButton.text.color ?? '#FFFFFF'
                  }}
                  onClick={handleAccessProduct}
                  className="w-full"
                >
                  ACESSAR MEU PRODUTO
                </Button>
              </div>

              <span
                style={{ color: settings.text.color.secondary }}
                className="mt-10 text-gray-500"
              >
                Também enviamos uma confirmação para o seu e-mail.
              </span>
            </>
          )}
          {isExternalProduct && (
            <span style={{ color: settings.text.color.secondary }} className="mt-1 text-center">
              Em breve você receberá um e-mail com o acesso para a área de membros.
            </span>
          )}
        </div>
      </div>
    </Card>
  );
};

export default SuccessPayment;

