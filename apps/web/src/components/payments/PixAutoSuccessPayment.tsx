"use client";

import Button from "@/components/ui/button-form";
import Card from "@/components/ui/card-form";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";
import type { Payment } from "@/types";
import { formatPrice } from "@/lib/utils/format";

interface PixAutoSuccessPaymentProps {
	payment: Payment;
	onContinue?: () => void;
}

const PixAutoSuccessPayment = ({
	payment,
	onContinue,
}: PixAutoSuccessPaymentProps) => {
	const settings = useSettings();
	const { t } = useTranslation();

	return (
		<Card
			style={{
				backgroundColor: settings.form?.background.color,
			}}
		>
			<div
				style={{
					backgroundColor: settings.form?.background.color,
				}}
				className="p-6 md:mx-auto"
			>
				<svg
					viewBox="0 0 24 24"
					className="text-green-600 w-16 h-16 mx-auto my-6"
				>
					<path
						fill="currentColor"
						d="M12,0A12,12,0,1,0,24,12,12.014,12.014,0,0,0,12,0Zm6.927,8.2-6.845,9.289a1.011,1.011,0,0,1-1.43.188L5.764,13.769a1,1,0,1,1,1.25-1.562l4.076,3.261,6.227-8.451A1,1,0,1,1,18.927,8.2Z"
					></path>
				</svg>

				<div className="flex w-full flex-col items-center">
					<h1 className="text-center text-2xl font-bold text-green-600 mb-4">
						{t('success.pix_auto_configured')}
					</h1>

					<p className="text-center text-gray-600 mb-6">
						{t('success.pix_auto_configured_description')}
					</p>

					<div className="bg-gray-50 rounded-lg p-4 w-full mb-6">
						<h3 className="text-lg font-semibold text-gray-900 mb-3 text-center">
							{t('success.payment_details')}
						</h3>
						<div className="space-y-2">
							<div className="flex justify-between items-center">
								<span className="text-gray-600 text-sm">
									{t('success.payment_method')}:
								</span>
								<span className="text-gray-900 font-medium text-sm">
									PIX Automático
								</span>
							</div>

							<div className="flex justify-between items-center">
								<span className="text-gray-600 text-sm">{t('success.amount')}:</span>
								<span className="text-gray-900 font-medium text-sm">
									{formatPrice(payment.amount)}
								</span>
							</div>

							<div className="flex justify-between items-center">
								<span className="text-gray-600 text-sm">{t('success.status')}:</span>
								<span className="text-green-600 font-medium text-sm">
									{t('success.approved')}
								</span>
							</div>

							<div className="flex justify-between items-center">
								<span className="text-gray-600 text-sm">{t('success.transaction_id_label')}:</span>
								<span className="text-gray-900 font-mono text-xs">
									{payment.id}
								</span>
							</div>
						</div>
					</div>

					<div className="bg-blue-50 border border-blue-200 rounded-lg p-4 w-full mb-6">
						<h4 className="text-md font-semibold text-blue-900 mb-2">
							{t('success.pix_auto_active')}
						</h4>
						<p className="text-blue-800 text-sm">
							{t('success.pix_auto_active_description')}
						</p>
					</div>

					{onContinue && (
						<div className="w-full max-w-md">
							<Button
								style={{
									backgroundColor: settings.payButton.color,
									color: settings.payButton.text.color,
								}}
								onClick={onContinue}
								className="w-full hover:opacity-90 transition-opacity"
							>
								{t('success.access_my_product')}
							</Button>
						</div>
					)}

					<span
						style={{ color: settings.text.color.secondary }}
						className="mt-6 text-gray-500 text-sm text-center"
					>
						{t('success.email_confirmation_sent')}
					</span>
				</div>
			</div>
		</Card>
	);
};

export default PixAutoSuccessPayment;



