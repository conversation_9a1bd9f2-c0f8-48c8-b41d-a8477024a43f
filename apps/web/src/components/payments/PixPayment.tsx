"use client";

import Button from '@/components/ui/button-form';
import useSettings from '@/hooks/useSettings';
import { useTranslation } from '@/hooks/useTranslation';
import { BRANDING } from '@/lib/utils/brand';
import { useEffect, useState } from 'react';
import { QRCode } from 'react-qrcode-logo';



interface PixPaymentProps {
  qrCode: string;
  status: string;
  checkingPayment: boolean;
  onCheckPayment: () => void;
}

const PixPayment = ({ qrCode, status, checkingPayment, onCheckPayment }: PixPaymentProps) => {
  const [isCopy, setIsCopy] = useState(false);
  const settings = useSettings();
  const { t } = useTranslation();
  const [minutes, setMinutes] = useState(15);
  const [seconds, setSeconds] = useState(0);

  const pixSteps = [
    {
      id: 1,
      icon: '1',
      text: t('payment.pix.step_1')
    },
    {
      id: 2,
      icon: '2',
      text: t('payment.pix.step_2')
    },
    {
      id: 3,
      icon: '3',
      text: t('payment.pix.step_3')
    },
    {
      id: 4,
      icon: '4',
      text: t('payment.pix.step_4')
    }
  ];

  const copyQrCode = async () => {
    if (typeof window === 'undefined') return;
    setIsCopy(true);
    navigator.clipboard.writeText(qrCode);

    await new Promise((r) => setTimeout(r, 2000));
    setIsCopy(false);
  };

  useEffect(() => {
    const calculateTimeLeft = () => {
      if (minutes === 0 && seconds === 0) {
        return;
      }

      if (seconds === 0) {
        setMinutes(minutes - 1);
        setSeconds(59);
      } else {
        setSeconds(seconds - 1);
      }
    };

    const interval = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(interval);
  }, [minutes, seconds]);

  return (
    <div className="flex flex-col justify-center items-center gap-4 w-full">
      <div className="flex flex-col gap-2 items-center justify-center">
        <h1 className="text-2xl font-bold text-gray-900">{t('payment.pix.generated_success')}</h1>
        <p className="text-gray-500 text-sm">{t('payment.pix.finalize_payment')}</p>
      </div>
      <div className="mx-6 my-2 w-full px-4">
        <div className="flex items-center justify-between">
          <p className="text-gray-700 font-medium">{t('payment.pix.expires_in_label')}</p>
          <div className="flex items-center">
            <div className="bg-rose-500 text-white font-bold text-xl rounded-lg w-12 h-12 flex items-center justify-center">
              {minutes}
            </div>
            <span className="mx-1 text-xl font-bold text-gray-700">:</span>
            <div className="bg-rose-500 text-white font-bold text-xl rounded-lg w-12 h-12 flex items-center justify-center">
              {seconds}
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col items-center px-14 w-fit">
        <div className="w-80 xs:w-52 border-2 border-dashed border-gray-300 rounded-md mb-4">
          <QRCode
            value={qrCode}
            size={256}
            logoImage={`/assets/qrcode-${BRANDING}-logo.webp`}
            logoWidth={30}
            logoPadding={5}
            logoPaddingStyle="circle"
            logoHeight={30}
            qrStyle="dots"
            eyeRadius={10}
            removeQrCodeBehindLogo={true}
            style={{ width: '100%', height: 'auto', borderRadius: '0.375rem' }}
          />
        </div>

        {status === 'waiting_payment' && (
          <div className="mx-6 bg-gray-50 rounded-lg flex items-center justify-center space-x-1">
            <img src={'/assets/loader.svg'} className='w-5 h-5' />
            <span className="text-gray-700 font-medium">{t('payment.pix.waiting_payment')}</span>
          </div>
        )}

        <div className="flex justify-center border-t border-gray-100 py-4 px-6 my-3">
          <div className="flex items-center gap-1.5 py-1.5 px-3 bg-brand-primary-lighter text-brand-primary border border-brand-primary border-opacity-30 rounded-full text-xs">
            <span>{t('payment.pix.approval_time')}</span>
          </div>
        </div>

        <div className="flex flex-col gap-3 w-80 xs:w-52 mb-2">
          <button
            onClick={copyQrCode}
            className="w-full py-3 px-4 border border-brand-primary bg-white hover:bg-brand-primary-light active:bg-green-100 rounded-md text-gray-700 font-medium flex items-center justify-center gap-2 transition-colors"
            disabled={false}
          >
            {isCopy ? (
              <span className="font-semibold text-brand-primary">{t('payment.pix.code_copied')}</span>
            ) : (
              <div className="flex items-center justify-center gap-2">
                <img className="w-4 h-4" src='/assets/copy-green.svg' alt="icone de copiar" />
                <span className="font-semibold text-brand-primary">{t('payment.pix.copy_code')}</span>
              </div>
            )}
          </button>
          <Button
            style={{
              backgroundColor: settings.payButton.color,
              color: settings.payButton.text.color
            }}
            sizes="sm"
            onClick={onCheckPayment}
            loading={checkingPayment}
            className="hover:opacity-90 transition-opacity"
          >
            {t('payment.pix.already_paid')}
          </Button>
        </div>
      </div>

      <div
        style={{ backgroundColor: settings.form?.background.color }}
        className="text-white p-4 rounded-md w-full"
      >
        <div className="space-y-4">
          {pixSteps.map((step) => (
            <div key={step.id} className="flex items-start gap-2">
              <span className="flex items-center justify-center w-7 h-7 bg-brand-primary-lighter text-brand-primary rounded-full text-sm font-bold shrink-0">
                {step.icon}
              </span>
              <p
                className="text-gray-700 leading-relaxed"
                dangerouslySetInnerHTML={{ __html: step.text }}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PixPayment;

