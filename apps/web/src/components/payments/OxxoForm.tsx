"use client";

import { useTranslation } from "@/hooks/useTranslation";
import useSettings from "@/hooks/useSettings";
import { InformationCircleIcon } from "@heroicons/react/24/outline";

export function OxxoForm() {
  const { t } = useTranslation();
  const settings = useSettings();

  return (
    <div className="space-y-4">
      {/* Info Box */}
      <div 
        className="p-4 rounded-lg flex items-start gap-3"
        style={{ backgroundColor: settings.box.default.background.color }}
      >
        <InformationCircleIcon 
          className="w-6 h-6 flex-shrink-0"
          style={{ color: settings.text.color.active }}
        />
        <div>
          <h3 
            className="font-semibold mb-1 text-base"
            style={{ color: settings.text.color.primary }}
          >
            {t("payment.oxxo.title")}
          </h3>
          <p 
            className="text-sm"
            style={{ color: settings.text.color.secondary }}
          >
            {t("payment.oxxo.description")}
          </p>
        </div>
      </div>

      {/* Como funciona */}
      <div className="space-y-3">
        <p 
          className="text-sm font-semibold"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.oxxo.how_it_works")}
        </p>
        <ol className="space-y-2">
          {[1, 2, 3, 4].map((step) => (
            <li key={step} className="flex items-start gap-2">
              <span 
                className="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold"
                style={{ 
                  backgroundColor: settings.box.selected.header.background.color,
                  color: settings.box.selected.header.text.color.primary
                }}
              >
                {step}
              </span>
              <span 
                className="text-sm pt-0.5"
                style={{ color: settings.text.color.secondary }}
              >
                {t(`payment.oxxo.step_${step}`)}
              </span>
            </li>
          ))}
        </ol>
      </div>

      {/* Aviso */}
      <div 
        className="p-3 rounded-lg border"
        style={{ 
          backgroundColor: settings.box.unselected.background.color,
          borderColor: settings.box.unselected.header.background.color 
        }}
      >
        <p 
          className="text-xs flex items-start gap-2"
          style={{ color: settings.text.color.secondary }}
        >
          <span className="text-base">⏱️</span>
          <span>{t("payment.oxxo.processing_time")}</span>
        </p>
      </div>
    </div>
  );
}
