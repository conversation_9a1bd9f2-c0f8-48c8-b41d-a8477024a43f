"use client";

import { createContext, useContext, type ReactNode, useMemo } from "react";

import type { Messages } from "@/types/i18n";
import type { Locale } from "@/lib/i18n/config";

type IntlContextValue = {
	locale: Locale;
	messages: Messages;
};

const IntlContext = createContext<IntlContextValue | null>(null);

export function IntlProvider({
	locale,
	messages,
	children,
}: {
	locale: Locale;
	messages: Messages;
	children: ReactNode;
}) {
	const value = useMemo(
		() => ({
			locale,
			messages,
		}),
		[locale, messages],
	);

	return <IntlContext.Provider value={value}>{children}</IntlContext.Provider>;
}

export function useLocale() {
	const context = useIntlContext();
	return context.locale;
}

export function useMessages() {
	const context = useIntlContext();
	return context.messages;
}

export function useMessage(path: string, fallback?: string): string {
	const messages = useMessages();
	return getNestedValue(messages, path) ?? fallback ?? path;
}

export function useIntlContext(): IntlContextValue {
	const context = useContext(IntlContext);
	if (!context) {
		throw new Error("IntlContext is not available. Wrap your component with IntlProvider.");
	}

	return context;
}

function getNestedValue(
	source: Messages,
	path: string,
): string | undefined {
	return path.split(".").reduce<unknown>((accumulator, key) => {
		if (typeof accumulator !== "object" || accumulator === null) {
			return undefined;
		}

		return (accumulator as Record<string, unknown>)[key];
	}, source) as string | undefined;
}


