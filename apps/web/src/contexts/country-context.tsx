"use client";

import { createContext, useContext, useState, useEffect, ReactNode, useMemo } from "react";
import { useRouter, usePathname } from "next/navigation";
import type { CountryCode, CountryConfig, UserCountryData } from "@/types/country";
import { COUNTRIES, LANGUAGE_TO_DEFAULT_COUNTRY } from "@/constants/countries";
import { useLocale } from "./intl-context";

type CountryContextType = {
  country: CountryConfig;
  userCountryData: UserCountryData;
  changeCountry: (countryCode: CountryCode) => void;
  isLoadingCountry: boolean;
};

const CountryContext = createContext<CountryContextType | undefined>(undefined);

export function CountryProvider({ children }: { children: ReactNode }) {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  
  const [userCountryData, setUserCountryData] = useState<UserCountryData>(() => {
    // 1. Tentar ler do localStorage
    if (typeof window !== "undefined") {
      const stored = localStorage.getItem("user_country");
      if (stored) {
        try {
          return JSON.parse(stored);
        } catch {}
      }
    }
    
    // 2. Fallback para país padrão do idioma
    const defaultCountry = LANGUAGE_TO_DEFAULT_COUNTRY[locale] || "BR";
    return {
      countryCode: defaultCountry,
      isManualSelection: false,
    };
  });

  const [isLoadingCountry, setIsLoadingCountry] = useState(true);

  // Detectar país via IP na primeira montagem
  useEffect(() => {
    const detectCountry = async () => {
      // Se já tem seleção manual, não sobrescrever
      if (userCountryData.isManualSelection) {
        setIsLoadingCountry(false);
        return;
      }

      try {
        // Chamar API de geolocalização
        const response = await fetch('/api/geoip');
        const data = await response.json();
        const detectedCountry = data.countryCode as CountryCode;
        
        // Verificar se o país detectado é válido
        if (COUNTRIES[detectedCountry]) {
          setUserCountryData({
            countryCode: detectedCountry,
            detectedIp: data.ip,
            detectedAt: new Date().toISOString(),
            isManualSelection: false,
          });
        } else {
          // Se país não é suportado, usar país padrão do idioma
          const defaultCountry = LANGUAGE_TO_DEFAULT_COUNTRY[locale] || "BR";
          setUserCountryData({
            countryCode: defaultCountry,
            detectedAt: new Date().toISOString(),
            isManualSelection: false,
          });
        }
      } catch (error) {
        console.error("Erro ao detectar país:", error);
        // Fallback para país padrão do idioma
        const defaultCountry = LANGUAGE_TO_DEFAULT_COUNTRY[locale] || "BR";
        setUserCountryData({
          countryCode: defaultCountry,
          isManualSelection: false,
        });
      } finally {
        setIsLoadingCountry(false);
      }
    };

    detectCountry();
  }, [locale, userCountryData.isManualSelection]);

  // Salvar no localStorage sempre que mudar
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("user_country", JSON.stringify(userCountryData));
    }
  }, [userCountryData]);

  const country = useMemo(() => {
    return COUNTRIES[userCountryData.countryCode] || COUNTRIES.BR;
  }, [userCountryData.countryCode]);

  const changeCountry = (countryCode: CountryCode) => {
    const newCountry = COUNTRIES[countryCode];
    if (!newCountry) return;

    // Atualizar estado
    setUserCountryData({
      countryCode,
      isManualSelection: true,
      detectedAt: new Date().toISOString(),
    });

    // Mudar idioma se necessário
    if (newCountry.language !== locale) {
      const nextPath = pathname?.replace(`/${locale}/`, `/${newCountry.language}/`) || "/";
      
      if (typeof document !== "undefined") {
        document.cookie = `NEXT_LOCALE=${newCountry.language}; max-age=31536000; path=/; SameSite=Lax`;
      }
      
      router.push(nextPath);
      router.refresh();
    }
  };

  const value = useMemo(
    () => ({
      country,
      userCountryData,
      changeCountry,
      isLoadingCountry,
    }),
    [country, userCountryData, isLoadingCountry]
  );

  return (
    <CountryContext.Provider value={value}>
      {children}
    </CountryContext.Provider>
  );
}

export function useCountry() {
  const context = useContext(CountryContext);
  if (context === undefined) {
    throw new Error("useCountry must be used within CountryProvider");
  }
  return context;
}

