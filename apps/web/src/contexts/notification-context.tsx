"use client";

import { createContext, useContext, type ReactNode } from "react";
import useNotifications from "@/hooks/useNotifications";

type NotificationContextData = ReturnType<typeof useNotifications>;

const NotificationContext = createContext<NotificationContextData | null>(
	null,
);

export function NotificationProvider({ children }: { children: ReactNode }) {
	const { notifications, notify, dismiss } = useNotifications();

	// Todo: listen to socket events and notify accordingly

	return (
		<NotificationContext.Provider
			value={{
				notifications,
				notify,
				dismiss,
			}}
		>
			{children}
		</NotificationContext.Provider>
	);
}

export function useNotificationContext() {
	const context = useContext(NotificationContext);
	if (!context) {
		throw new Error(
			"useNotificationContext must be used within NotificationProvider",
		);
	}
	return context;
}

