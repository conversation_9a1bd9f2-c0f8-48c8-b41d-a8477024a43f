"use client";

import { createContext, useContext, useMemo, type ReactNode } from "react";

type CheckoutModeContextValue = {
	preview: boolean;
};

const CheckoutModeContext = createContext<CheckoutModeContextValue | null>(
	null,
);

export function CheckoutModeProvider({
	children,
	preview,
}: {
	children: ReactNode;
	preview: boolean;
}) {
	const value = useMemo(() => ({ preview }), [preview]);

	return (
		<CheckoutModeContext.Provider value={value}>
			{children}
		</CheckoutModeContext.Provider>
	);
}

export function useCheckoutMode() {
	const context = useContext(CheckoutModeContext);
	if (!context) {
		throw new Error(
			"useCheckoutMode must be used within CheckoutModeProvider",
		);
	}
	return context;
}

