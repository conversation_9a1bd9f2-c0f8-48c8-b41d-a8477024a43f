"use client";

import {
	createContext,
	useContext,
	useCallback,
	useMemo,
	useState,
	useEffect,
	type ReactNode,
} from "react";
import moment from "moment";
import type {
	ProductData,
	Payment,
	PaymentPayload,
	PaymentMethod,
	PaymentMethodOption,
	PaymentTab,
	CouponData,
	Order,
} from "@/types";
import type { ErrorDetails } from "@/types";
import { errorHandler } from "@/lib/utils/error-handler";
import { PaymentApprovedStatus } from "@/constants/payment";
import { useFacebookPixels } from "@/hooks/useFacebookPixels";
import { useGoogleAds } from "@/hooks/useGoogleAds";
import useTikTokPixels from "@/hooks/useTikTokPixels";
import useKwaiPixels from "@/hooks/useKwaiPixels";
import {
	startPayment as startPaymentAPI,
	getPaymentStatus as getPaymentStatusAPI,
	validateCoupon as validateCouponAPI,
	generateFingerprint as generateFingerprintAPI,
} from "@/lib/api/checkout-client";
import {
	trackCheckoutCompleted,
	trackCheckoutStarted,
	trackFormError,
	trackPaymentSelected,
} from "@/lib/utils/analytics";
import { canPaymentMethodBePaidInInstallments } from "@/lib/utils/payment";
import { CreditCardForm } from "@/components/payments/CreditCardForm";
import { BoletoForm } from "@/components/payments/BoletoForm";
import { PixForm } from "@/components/payments/PixForm";
import { PixAutoForm } from "@/components/payments/PixAutoForm";
import { PicPayForm } from "@/components/payments/PicPayForm";
import { ApplePayForm } from "@/components/payments/ApplePayForm";
import { GooglePayForm } from "@/components/payments/GooglePayForm";
import { NubankPayForm } from "@/components/payments/NubankPayForm";
import { OxxoForm } from "@/components/payments/OxxoForm";
import { SpeiForm } from "@/components/payments/SpeiForm";
import { useCountry } from "./country-context";

// Import payment icons
import { CreditCardIcon } from "@heroicons/react/20/solid";
import {
	ApplePayIcon,
	BarcodeIcon,
	GooglePayIcon,
	NubankIcon,
	OxxoIcon,
	PicPayIcon,
	PixIcon,
	PixAutoIcon,
	SpeiIcon,
} from "@/components/icons";

type MutationStatus = "idle" | "loading" | "success" | "error";

type CheckoutContextData = {
	isFetching: boolean;
	offer?: ProductData;
	pay: (payload: PaymentPayload) => Promise<unknown>;
	setFirstPayment: (payload: Partial<Payment>) => void;
	setPayments: React.Dispatch<React.SetStateAction<Payment[]>>;
	checkPayment: () => Promise<unknown>;
	checkingPayment: boolean;
	paying: boolean;
	payments: Payment[];
	firstPayment: Payment | null;
	error: unknown;
	method: PaymentMethod | null;
	status: MutationStatus;
	creditCardError: string;
	setCreditCardError: (error: string) => void;
	setCountdownHeight: (height: number) => void;
	countdownHeight: number;
	calcInstallments: unknown;
	setCalcInstallments: (installments: unknown) => void;
	fingerprint?: string;
	couponData?: CouponData;
	validateCoupon: (coupon: string) => Promise<unknown>;
	isLoadingValidateCoupon: boolean;
	applyCouponValue: number;
	setApplyCouponValue: (number: number) => void;
	paymentMethod: PaymentMethodOption | null;
	setPaymentMethod: (payment: PaymentMethodOption) => void;
	paymentTabs: PaymentTab[];
	hasAnySubscription: boolean;
	canBePaidInInstallments: boolean;
};

const CheckoutContext = createContext<CheckoutContextData | null>(null);

export function CheckoutProvider({
	children,
	initialData,
	checkoutId,
}: {
	children: ReactNode;
	initialData: ProductData;
	checkoutId: string;
}) {
	// Country context
	const { country } = useCountry();
	
	// State
	const [offer, setOffer] = useState<ProductData | undefined>(initialData);
	const [isFetching] = useState(false);
	const [paymentMethod, setPaymentMethod] =
		useState<PaymentMethodOption | null>(null);
	const [method, setMethod] = useState<PaymentMethod | null>(null);
	const [payments, setPayments] = useState<Payment[]>([]);
	const [creditCardError, setCreditCardError] = useState("");
	const [calcInstallments, setCalcInstallments] = useState<unknown>();
	const [countdownHeight, setCountdownHeight] = useState(0);
	const [applyCouponValue, setApplyCouponValue] = useState(0);
	const [paying, setPaying] = useState(false);
	const [checkingPayment, setCheckingPayment] = useState(false);
	const [error, setError] = useState<unknown>(null);
	const [status, setStatus] = useState<MutationStatus>("idle");
	const [fingerprint, setFingerprint] = useState<string | undefined>(
		undefined,
	);
	const [couponData, setCouponData] = useState<CouponData | undefined>(
		undefined,
	);
	const [isLoadingValidateCoupon, setIsLoadingValidateCoupon] =
		useState(false);

	const firstPayment = payments[0] || null;

	// Pixel hooks
	const googleAds = useGoogleAds(offer);
	const facebookPixels = useFacebookPixels(offer);
	const tikTokPixels = useTikTokPixels(offer);
	const kwaiPixels = useKwaiPixels(offer);

	// Generate fingerprint on mount
	useEffect(() => {
		generateFingerprintAPI().then(setFingerprint);
	}, []);

	// Track checkout started
	useEffect(() => {
		if (offer?.id && offer?.product) {
			trackCheckoutStarted(offer.id, offer.product.price, [
				{
					id: offer.product.short_id,
					name: offer.product.name,
					price: offer.product.price,
					quantity: 1,
				},
			]);
		}
	}, [offer]);

	// Payment mutation
	const pay = useCallback(
		async (payload: PaymentPayload) => {
			try {
				setPaying(true);
				setStatus("loading");
				setCreditCardError("");
				setMethod(payload.paymentMethod);

				const refusedPaymentsIds = firstPayment?.errors?.map(({ id }) => id);
				const newPayload = {
					...payload,
					items: payload.items.filter(({ id }) =>
						refusedPaymentsIds?.length
							? refusedPaymentsIds?.includes(id)
							: true,
					),
				};

				if (payload.paymentMethod) {
					trackPaymentSelected(payload.paymentMethod);
				}

				const data = await startPaymentAPI(
					offer?.product.short_id as string,
					{
						...newPayload,
						checkoutUrl: window.location.href,
					},
				);

				// Preservar o paymentMethod correto baseado no método selecionado
				const firstPaymentData = data.payments[0];
				const preservedPaymentMethod =
					payload.paymentMethod === "pix_auto"
						? "pix_auto"
						: firstPaymentData?.paymentMethod;

				setFirstPayment({
					accessToken: data.accessToken,
					refreshToken: data.refreshToken,
					paymentMethod: preservedPaymentMethod,
				});
				setPayments(data.payments);
				setStatus("success");

				// Track pixels
				data.payments.forEach((payment: Payment) => {
					const paymentMethodType = payment?.orders?.[0]?.paymentMethod;
					const paymentStatus = payment?.orders?.[0]?.status;

					const ticketGenerated =
						paymentMethodType === "boleto" && paymentStatus === "waiting_payment";
					const pixGenerated =
						(paymentMethodType === "pix" || paymentMethodType === "pix_auto") &&
						paymentStatus === "waiting_payment";
					const picpayGenerated =
						paymentMethodType === "picpay" && paymentStatus === "waiting_payment";
					const nubankGenerated =
						paymentMethodType === "openfinance_nubank" &&
						paymentStatus === "waiting_payment";

					const paymentApproved = PaymentApprovedStatus.includes(paymentStatus);

					const amount: number = (payment?.orders || []).reduce(
						(acc: number, order: Order) => acc + Number(order?.amount || 0),
						0,
					);

					if (ticketGenerated) {
						googleAds.onTicketGenerated(payment);
						facebookPixels.onTicketGenerated(payment);
						tikTokPixels.onTicketGenerated(payment);
						kwaiPixels.onTicketGenerated(payment);
					}

					if (pixGenerated) {
						googleAds.onPixGenerated(payment);
						facebookPixels.onPixGenerated(payment);
						tikTokPixels.onPixGenerated(payment);
						kwaiPixels.onPixGenerated(payment);
					}

					if (picpayGenerated) {
						facebookPixels.onPicpayGenerated(payment);
						tikTokPixels.onPicpayGenerated(payment);
						kwaiPixels.onPicpayGenerated(payment);
					}

					if (nubankGenerated) {
						facebookPixels.onNubankGenerated(payment);
						tikTokPixels.onNubankGenerated(payment);
						kwaiPixels.onNubankGenerated(payment);
					}

					if (paymentApproved) {
						facebookPixels.onPaymentApproved(payment, amount);
						tikTokPixels.onPaymentApproved(payment, amount);
						kwaiPixels.onPaymentApproved(payment, amount);
						trackCheckoutCompleted(
							payment?.orders[0]?.id,
							payment?.orders[0]?.amount,
							paymentMethodType,
						);
					}
				});

				return data;
			} catch (err) {
				setStatus("error");
				setError(err);
				const userFriendlyError = errorHandler.handleAxiosError(
					err as ErrorDetails,
				);
				setCreditCardError(userFriendlyError.message);
				trackFormError("checkout", "payment", userFriendlyError.message);
				throw err;
			} finally {
				setPaying(false);
			}
		},
		[
			firstPayment,
			offer,
			googleAds,
			facebookPixels,
			tikTokPixels,
			kwaiPixels,
		],
	);

	const setFirstPayment = useCallback((payload: Partial<Payment>) => {
		setPayments(([firstPayment, ...prevPayments]) => [
			{ ...firstPayment, ...payload },
			...prevPayments,
		]);
	}, []);

	const checkPayment = useCallback(async () => {
		if (!firstPayment?.id) {
			throw new Error("No payment to check");
		}

		try {
			setCheckingPayment(true);
			const data = await getPaymentStatusAPI(firstPayment.id);

			// Preservar o paymentMethod original ao atualizar o status
			const currentPaymentMethod = firstPayment?.paymentMethod;
			setFirstPayment({
				status: data.status,
				refreshToken: data.refreshToken,
				accessToken: data.accessToken,
				paymentMethod: currentPaymentMethod,
			});

			return data;
		} catch (err) {
			throw err;
		} finally {
			setCheckingPayment(false);
		}
	}, [firstPayment, setFirstPayment]);

	const validateCoupon = useCallback(
		async (coupon: string) => {
			try {
				setIsLoadingValidateCoupon(true);
				const data = await validateCouponAPI({
					code: coupon.toLowerCase(),
					offerId: checkoutId?.split("_")[0],
				});

				const { startTime, endTime } = data;
				const now = moment();
				const lifetimeDate = moment("9999-12-31");

				if (
					now.isBetween(
						moment(startTime),
						endTime ? moment(endTime) : lifetimeDate,
					)
				) {
					setApplyCouponValue(1);
					setCouponData(data);
				} else {
					throw new Error("error");
				}

				return data;
			} catch (err) {
				throw err;
			} finally {
				setIsLoadingValidateCoupon(false);
			}
		},
		[checkoutId],
	);

	// Payment icons map
	const mapIcons: Record<string, React.ComponentType<{ className?: string }>> =
		{
			credit_card: CreditCardIcon,
			boleto: BarcodeIcon,
			pix: PixIcon,
			pix_auto: PixAutoIcon,
			picpay: PicPayIcon,
			applepay: ApplePayIcon,
			googlepay: GooglePayIcon,
			openfinance_nubank: NubankIcon,
			oxxo: OxxoIcon,
			spei: SpeiIcon,
		};

	// Payment forms map
	const mapForms: Record<string, React.ComponentType<unknown>> = {
		credit_card: CreditCardForm,
		boleto: BoletoForm,
		pix: PixForm,
		pix_auto: PixAutoForm,
		picpay: PicPayForm,
		applepay: ApplePayForm,
		googlepay: GooglePayForm,
		openfinance_nubank: NubankPayForm,
		oxxo: OxxoForm,
		spei: SpeiForm,
	};

	const paymentTabs = useMemo(() => {
		const paymentMethods = offer?.product?.paymentMethods || [];

		// Filtrar métodos disponíveis no país selecionado
		const availableInCountry = paymentMethods.filter(({ type }) => 
			country.paymentMethods.includes(type)
		);

		// Para produtos de assinatura, usar apenas os métodos configurados no produto
		// Para produtos únicos, usar apenas os métodos configurados no produto
		let allowedPaymentsMethods: PaymentMethod[] =
			offer?.type === "subscription"
				? availableInCountry?.map(({ type }) => type) || [
						"pix",
						"boleto",
						"credit_card",
					]
				: availableInCountry?.map(({ type }) => type);

		// Para produtos únicos, remover pix_auto mesmo que venha no response
		if (offer?.type === "unique") {
			allowedPaymentsMethods = allowedPaymentsMethods.filter(
				(methodType) => methodType !== "pix_auto",
			);
		}

		const paymentsOrder =
			offer?.product?.paymentsOrder?.filter((payment) =>
				allowedPaymentsMethods.includes(payment),
			) || allowedPaymentsMethods;

		return availableInCountry
			.filter(
				({ type }: PaymentMethodOption) =>
					allowedPaymentsMethods.includes(type) && type !== "threeDs",
			)
			?.sort(
				(a: PaymentMethodOption, b: PaymentMethodOption) =>
					paymentsOrder.indexOf(a.type) - paymentsOrder.indexOf(b.type),
			)
			?.map((item: PaymentMethodOption) => ({
				id: item.type,
				label: item.name,
				Icon: mapIcons[item.type] || CreditCardIcon,
				Component: mapForms[item.type] || (() => <div>Not Found</div>),
			}));
	}, [offer, country]);

	const hasAnySubscription = useMemo(() => {
		if (!offer) {
			return false;
		}
		if (offer?.type === "subscription") {
			return true;
		}
		return (
			offer?.product?.bumps?.some(
				(bump) => bump.offer?.type === "subscription",
			) || false
		);
	}, [offer]);

	const canBePaidInInstallments = useMemo(() => {
		if (!offer || !paymentMethod) {
			return false;
		}
		return canPaymentMethodBePaidInInstallments(paymentMethod.type);
	}, [offer, paymentMethod]);

	const value = useMemo(
		() => ({
			isFetching,
			offer,
			pay,
			firstPayment,
			setFirstPayment,
			paying,
			checkPayment,
			setPayments,
			checkingPayment,
			payments,
			error,
			method,
			status,
			creditCardError,
			setCreditCardError,
			setCountdownHeight,
			countdownHeight,
			calcInstallments,
			setCalcInstallments,
			fingerprint,
			couponData,
			validateCoupon,
			isLoadingValidateCoupon,
			applyCouponValue,
			setApplyCouponValue,
			paymentMethod,
			setPaymentMethod,
			paymentTabs: paymentTabs || [],
			canBePaidInInstallments,
			hasAnySubscription,
		}),
		[
			isFetching,
			offer,
			pay,
			firstPayment,
			setFirstPayment,
			paying,
			checkPayment,
			checkingPayment,
			payments,
			error,
			method,
			status,
			creditCardError,
			countdownHeight,
			calcInstallments,
			fingerprint,
			couponData,
			validateCoupon,
			isLoadingValidateCoupon,
			applyCouponValue,
			paymentMethod,
			paymentTabs,
			canBePaidInInstallments,
			hasAnySubscription,
		],
	);

	return (
		<CheckoutContext.Provider value={value}>
			{children}
		</CheckoutContext.Provider>
	);
}

export function useCheckout() {
	const context = useContext(CheckoutContext);
	if (!context) {
		throw new Error("useCheckout must be used within CheckoutProvider");
	}
	return context;
}

