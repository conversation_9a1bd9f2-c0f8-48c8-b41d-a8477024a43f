import type { NextRequest } from "next/server";

import {
	COUNTRY_LOCALE_MAP,
	DEFAULT_LOCALE,
	isSupportedLocale,
	normalizeLocale,
	LOCALE_COOKIE,
	type Locale,
} from "./config";
import { getGeoipResolverUrl, getGeoipResolverToken } from "../env";

type CountryCode = keyof typeof COUNTRY_LOCALE_MAP;

const IP_CACHE_TTL = 60 * 60 * 1000; // 1 hour

type IpCacheEntry = {
	country?: string;
	expiresAt: number;
};

const ipCache = new Map<string, IpCacheEntry>();

export function mapCountryToLocale(country?: string | null): Locale {
	if (!country) {
		return DEFAULT_LOCALE;
	}

	const upper = country.toUpperCase() as CountryCode;
	return COUNTRY_LOCALE_MAP[upper] ?? "en";
}

export async function resolveLocaleFromRequest(
	request: NextRequest,
): Promise<Locale> {
	const pathnameLocale = getLocaleFromPathname(request.nextUrl.pathname);
	if (pathnameLocale) {
		return pathnameLocale;
	}

	const cookieLocale = request.cookies.get(LOCALE_COOKIE)?.value;
	if (isSupportedLocale(cookieLocale)) {
		return cookieLocale;
	}

	const country = await getCountryForRequest(request);
	if (country) {
		return mapCountryToLocale(country);
	}

	const preferred = parseAcceptLanguageHeader(
		request.headers.get("accept-language"),
	);
	if (preferred) {
		return preferred;
	}

	return DEFAULT_LOCALE;
}

export function getLocaleFromPathname(pathname: string): Locale | undefined {
	const [, maybeLocale] = pathname.split("/");
	if (isSupportedLocale(maybeLocale)) {
		return maybeLocale;
	}

	return undefined;
}

function parseAcceptLanguageHeader(header: string | null): Locale | undefined {
	if (!header) {
		return undefined;
	}

	const parts = header.split(",");
	for (const part of parts) {
		const [localePart] = part.trim().split(";");
		const normalized = normalizeLocale(localePart);
		if (isSupportedLocale(normalized)) {
			return normalized;
		}
	}

	return undefined;
}

async function getCountryForRequest(request: NextRequest): Promise<string | undefined> {
	const ip = getClientIp(request);
	if (!ip) {
		return undefined;
	}

	const cachedCountry = getCachedCountry(ip);
	if (cachedCountry) {
		return cachedCountry;
	}

	const country = await fetchCountry(ip);
	setCachedCountry(ip, country);
	return country;
}

function getClientIp(request: NextRequest): string | undefined {
	if (request.ip) {
		return request.ip;
	}

	const forwardedFor = request.headers.get("x-forwarded-for");
	if (forwardedFor) {
		return forwardedFor.split(",")[0]?.trim();
	}

	return undefined;
}

function getCachedCountry(ip: string): string | undefined {
	const entry = ipCache.get(ip);
	if (!entry) {
		return undefined;
	}

	if (entry.expiresAt < Date.now()) {
		ipCache.delete(ip);
		return undefined;
	}

	return entry.country;
}

function setCachedCountry(ip: string, country?: string) {
	ipCache.set(ip, {
		country,
		expiresAt: Date.now() + IP_CACHE_TTL,
	});
}

async function fetchCountry(ip: string): Promise<string | undefined> {
	const endpoint = getGeoipResolverUrl();
	const token = getGeoipResolverToken();

	try {
		if (endpoint) {
			const url = endpoint.replace(":ip", encodeURIComponent(ip));
			const response = await fetch(url, {
				headers: token
					? {
							Authorization: `Bearer ${token}`,
						}
					: undefined,
				cache: "no-store",
			});

			if (!response.ok) {
				return undefined;
			}

			const data = await response.json();
			return (
				data.country ||
				data.country_code ||
				data.countryCode ||
				data.location?.country ||
				undefined
			);
		}

		const response = await fetch(`https://ipapi.co/${encodeURIComponent(ip)}/country/`, {
			cache: "no-store",
		});

		if (!response.ok) {
			return undefined;
		}

		const text = await response.text();
		return text.trim() || undefined;
	} catch (error) {
		console.error("Failed to resolve IP country", error);
		return undefined;
	}
}


