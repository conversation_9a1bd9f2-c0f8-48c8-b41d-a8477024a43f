import { interpolate } from "@/lib/utils/interpolate";

import type { Messages } from "./types";

export function getMessage(
	messages: Messages,
	key: string,
	params?: Record<string, string | number>,
): string {
	const value = resolvePath(messages, key);

	if (typeof value !== "string") {
		return key;
	}

	if (!params) {
		return value;
	}

	return interpolate(value, params);
}

export function resolvePath(source: unknown, key: string): unknown {
	return key.split(".").reduce<unknown>((accumulator, segment) => {
		if (typeof accumulator !== "object" || accumulator === null) {
			return undefined;
		}

		return (accumulator as Record<string, unknown>)[segment];
	}, source);
}


