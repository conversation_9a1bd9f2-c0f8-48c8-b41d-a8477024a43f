export const SUPPORTED_LOCALES = ["pt", "es", "en"] as const;

export type Locale = (typeof SUPPORTED_LOCALES)[number];

export const DEFAULT_LOCALE: Locale = "pt";

export const LOCALE_COOKIE = "NEXT_LOCALE";

export const COUNTRY_LOCALE_MAP: Record<string, Locale> = {
	BR: "pt",
	AR: "es",
	BO: "es",
	CL: "es",
	CO: "es",
	EC: "es",
	MX: "es",
	PE: "es",
	PY: "es",
	UY: "es",
	VE: "es",
	PA: "es",
	CR: "es",
	NI: "es",
	DO: "es",
	HN: "es",
	GT: "es",
	SV: "es",
	PR: "es",
	// Fallback to English for everything else
};

export function isSupportedLocale(value: string | null | undefined): value is Locale {
	return !!value && SUPPORTED_LOCALES.includes(value as Locale);
}

export function normalizeLocale(value: string | null | undefined): Locale {
	if (isSupportedLocale(value)) {
		return value;
	}

	const normalized = value?.split("-")[0];
	if (isSupportedLocale(normalized)) {
		return normalized;
	}

	return DEFAULT_LOCALE;
}


