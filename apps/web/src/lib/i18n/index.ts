import "server-only";

import type { Locale } from "./config";
import type { Messages } from "./types";

export type { Messages } from "./types";

const messageLoaders: Record<Locale, () => Promise<Messages>> = {
	pt: () => import("./messages/pt.json").then((mod) => mod.default),
	es: () => import("./messages/es.json").then((mod) => mod.default),
	en: () => import("./messages/en.json").then((mod) => mod.default),
};

export async function loadMessages(locale: Locale): Promise<Messages> {
	const loader = messageLoaders[locale];
	return loader();
}


