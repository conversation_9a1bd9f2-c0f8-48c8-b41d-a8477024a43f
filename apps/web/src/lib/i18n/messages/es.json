{"checkout": {"title": "Completa tu compra", "subtitle": "Completa los datos a continuación para finalizar tu pedido", "loading": "Cargando información del checkout...", "loading_checkout": "Cargando checkout...", "order_summary": "Resumen del pedido", "coupon_code": "Cupón de descuento", "coupon_placeholder": "Ingresa tu cupón", "coupon_applied": "Cupón aplicado", "coupon_success": "¡Cupón aplicado con éxito!", "service_fee": "Ta<PERSON>fa de servicio", "service_fee_tooltip": "<PERSON><PERSON><PERSON> de servicio cobrada por la plataforma", "total": "Total", "subtotal": "Subtotal", "discount": "Descuento", "secure_purchase": "Compra 100% segura", "recaptcha_notice": "Este sitio está protegido por reCAPTCHA y se aplican la Política de Privacidad y los Términos de Servicio de Google", "contact_info_title": "Información de Contacto", "payment_method_title": "Mé<PERSON><PERSON>", "processing_payment": "Procesando pago...", "no_payment_methods": "No hay métodos de pago disponibles", "not_found": "No encontrado"}, "common": {"continue": "<PERSON><PERSON><PERSON><PERSON>", "retry": "Intentar de nuevo", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "yes": "Sí", "no": "No", "loading": "Cargando...", "checkoutTitle": "Checkout seguro", "selectCheckout": "Selecciona un checkout válido para continuar.", "apply": "Aplicar", "remove": "Eliminar", "from": "de", "per": "por", "month": "mes", "year": "año", "week": "semana", "day": "día", "or": "o", "and": "y", "to": "para", "of": "de", "at": "en", "in": "en", "on": "en", "with": "con", "without": "sin", "free": "<PERSON><PERSON><PERSON>", "discount_label": "OFF"}, "errors": {"validation": {"required": "Este campo es obligatorio", "invalid_email": "Ingresa un email válido", "invalid_phone": "Ingresa un teléfono válido", "invalid_cpf": "Ingresa un CPF válido", "invalid_cnpj": "Ingresa un CNPJ válido", "invalid_cpf_cnpj": "Ingresa un CPF o CNPJ válido", "invalid_expiry": "Fecha de vencimiento inválida", "invalid_cvv": "CVV inválido", "invalid_card": "Número de tarjeta inválido", "generic": "<PERSON><PERSON>", "invalid_date": "Ingresa una fecha válida"}, "payment": {"check_error": "Error al verificar el pago:", "polling_error": "Error en el polling automático:"}}, "form": {"labels": {"name": "Nombre completo", "email": "Correo electrónico", "phone": "Teléfono", "cpf": "CPF", "cnpj": "CNPJ", "cpf_cnpj": "CPF/CNPJ", "card_number": "Número de tarjeta", "card_expiry": "<PERSON><PERSON>nc<PERSON>o", "card_cvv": "CVV", "card_holder_name": "Nombre en la tarjeta", "installments": "<PERSON><PERSON><PERSON>", "coupon": "Cupón de descuento", "zipcode": "Código postal", "street": "Calle", "number": "Número", "complement": "Complemento", "neighborhood": "Barrio", "city": "Ciudad", "state": "Estado", "shipping": "Envío"}, "placeholders": {"name": "Ingresa tu nombre completo", "email": "<EMAIL>", "phone": "(00) 00000-0000", "cpf_cnpj": "000.000.000-00", "card_number": "0000 0000 0000 0000", "card_expiry": "MM/AA", "card_cvv": "000", "card_holder_name": "Nombre tal como aparece en la tarjeta", "coupon": "Ingresa el código del cupón", "zipcode": "00000-000", "street": "Calle, avenida, etc.", "number": "123", "complement": "Apartamento, bloque, etc.", "neighborhood": "Nombre del barrio", "city": "Nombre de la ciudad", "state": "Selecciona un estado", "shipping": "Selecciona una opción de envío"}, "helpers": {"cpf_required": "¿Por qué solicitamos este dato?", "phone_format": "Formato: (AA) 00000-0000", "card_number_hint": "Ingresa solo números"}}, "payment": {"methods": {"credit_card": "Tarjeta de crédito", "pix": "PIX", "pix_auto": "PIX automático", "boleto": "Boleto", "picpay": "PicPay", "applepay": "Apple Pay", "googlepay": "Google Pay", "openfinance_nubank": "Nubank"}, "messages": {"processing": "Procesando pago...", "waiting": "Esperando pago", "success": "¡Pago confirmado!", "failed": "<PERSON><PERSON>o", "expired": "Pago vencido", "check_payment": "Verificar pago", "checking_payment": "Verificando pago..."}, "installments": "<PERSON><PERSON><PERSON>", "subscription": "Suscripción", "installments_of": "cuota de", "installments_with_interest": "con intereses", "installments_without_interest": "sin intereses", "one_time": "al contado", "new_badge": "NUEVO", "loading_installments": "Cargando...", "pix": {"title": "PIX", "subtitle": "Escanea el código QR o copia el código de pago", "qr_code_label": "Código QR PIX", "copy_code": "Copiar código PIX", "code_copied": "<PERSON>x copiado", "expires_in": "Expira en {time}", "waiting_payment": "Esperando la confirmación del pago...", "instant_release": "Liberación inmediata", "easy_to_use": "Es simple, solo usa la aplicación de tu banco para pagar con PIX", "scan_qrcode": "Escanea el código QR", "or_copy_code": "O copia el código", "scan_instruction": "Escanear código QR", "open_bank_app": "Abre la app de tu banco", "scan_code": "Escanea el código", "confirm_payment": "Confirma el pago", "step_1": "Abre la app de tu banco y ve a la opción <strong>PIX</strong>", "step_2": "Elige la opción <strong>Pagar / PIX copiar y pegar</strong>", "step_3": "Escanea el código QR. Si prefieres, copia y pega el código", "step_4": "Luego, confirma el pago", "generated_success": "¡PIX generado con éxito!", "finalize_payment": "<PERSON>ora solo finaliza el pago", "expires_in_label": "Este código expirará en:", "approval_time": "La aprobación toma un máximo de 2 minutos", "already_paid": "Ya hice el pago"}, "pix_auto": {"title": "PIX Automático", "subtitle": "Pago automático vía PIX", "description": "Autoriza el pago una sola vez y todos los cobros futuros se procesarán automáticamente", "step_4": "<PERSON><PERSON>, autoriza el pago vía <strong>PIX Automático</strong>", "waiting_payment": "Esperando <PERSON>...", "copy_code": "Copiar código PIX", "info_1": "Con el PIX Automático, las renovaciones se realizan de forma recurrente, sin preocupación", "info_2": "Autoriza una vez el pago en la aplicación de tu banco y nosotros nos encargamos de los próximos", "advantages": {"title": "Ventajas del PIX Automático", "advantage_1": "Pago recurrente sin complicaciones", "advantage_2": "No necesitas ingresar datos cada vez", "advantage_3": "Seguro y protegido por el Banco Central", "advantage_4": "Cancela cuando quieras"}}, "boleto": {"title": "Boleto", "subtitle": "Copia el código de barras o descarga el boleto", "barcode_label": "Código de <PERSON>", "copy_code": "<PERSON><PERSON>r c<PERSON>", "code_copied": "<PERSON><PERSON><PERSON> copiado", "due_date": "<PERSON><PERSON>nc<PERSON>o", "download": "<PERSON><PERSON><PERSON> boleto", "print": "<PERSON><PERSON><PERSON><PERSON> bole<PERSON>", "print_billet": "<PERSON><PERSON><PERSON><PERSON>", "pay_at_bank": "Pagar en el banco", "payer_document": "CPF/CNPJ del pagador", "instructions": "Instrucciones de pago", "instruction_1": "Imprime el boleto y paga en el banco", "instruction_2": "O paga por banca en línea", "instruction_3": "La confirmación del pago puede tardar hasta 3 días hábiles", "amount_label": "Valor del boleto", "due_date_label": "<PERSON><PERSON>nc<PERSON>o", "barcode_number": "Código del Boleto", "digital_access_info": "El acceso al producto digital se enviará por correo electrónico después de que se pague el boleto. La confirmación del pago puede tardar hasta 48 horas."}, "credit_card": {"title": "Tarjeta de crédito", "subtitle": "Completa los datos de tu tarjeta", "card_number": "Número de tarjeta", "card_number_placeholder": "0000 0000 0000 0000", "cardholder_name": "Nombre en la tarjeta", "cardholder_name_placeholder": "Nombre tal como aparece en la tarjeta", "expiration_date": "<PERSON><PERSON>nc<PERSON>o", "expiration_placeholder": "MM/AA", "cvv": "CVV", "cvv_placeholder": "000", "save_card": "Guardar datos para futuras compras", "installments_label": "Número de cu<PERSON>s", "select_installments": "Selecciona el número de cuotas", "secure_payment": "Tus datos de pago están encriptados y se procesan de forma segura."}, "picpay": {"title": "PicPay", "subtitle": "Escanea el código QR con la app PicPay", "scan_qrcode": "Escanea el código QR", "waiting_payment": "Esperando la confirmación del pago..."}, "applepay": {"title": "Apple Pay", "subtitle": "Paga con Apple Pay", "button_label": "Pagar con Apple Pay"}, "googlepay": {"title": "Google Pay", "subtitle": "Paga con Google Pay", "button_label": "Pagar con Google Pay"}, "nubank": {"title": "Nubank", "subtitle": "Paga con tu cuenta Nubank", "button_label": "<PERSON><PERSON> con <PERSON>"}}, "address": {"title": "Dirección", "delivery_title": "Dirección de entrega", "zipcode_label": "Código postal", "zipcode_not_found": "Código postal no encontrado. Completa los campos manualmente.", "zipcode_searching": "<PERSON><PERSON><PERSON>...", "zipcode_found": "¡Dirección encontrada!", "street_label": "Calle", "number_label": "Número", "complement_label": "Complemento (opcional)", "neighborhood_label": "Barrio", "city_label": "Ciudad", "state_label": "Estado", "shipping_method": "M<PERSON><PERSON><PERSON>", "shipping_options": "Opciones de envío"}, "bump": {"title": "Ofertas limitadas", "add_product": "Agregar producto", "selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "discount_label": "OFF"}, "product": {"cash": "al contado"}, "data_privacy": {"why_we_ask": "¿Por qué pedimos este dato?", "phone": {"title": "¿Por qué pedimos tu teléfono?", "content": "Tu teléfono se utiliza para enviar notificaciones importantes sobre tu pedido, como confirmación de pago y acceso al producto. Nunca compartiremos tus datos con terceros."}, "document": {"title": "¿Por qué pedimos tu documento?", "content": "El documento es necesario para la emisión de la factura y para garantizar la seguridad de tu compra. Tus datos están encriptados y protegidos."}, "email": {"title": "¿Por qué pedimos tu correo electrónico?", "content": "Tu correo electrónico se utilizará para enviar el acceso al producto adquirido e información importante sobre tu pedido."}}, "security": {"vendor": "el vendedor", "processing_payment_for": "{brand} está procesando este pago para {seller}", "secure_purchase": "Compra 100% segura", "recaptcha_notice": "Este sitio está protegido por reCAPTCHA y la", "privacy_policy": "Política de Privacidad", "terms_of_service": "Términos de Servicio", "from_google": "de <PERSON>", "purchase_terms": "Términos de Compra", "installment_with_interest": "* Cuotas con interés"}, "success": {"title": "¡Pago aprobado!", "subtitle": "Tu pedido fue confirmado", "order_number": "Número de pedido", "approved": "Aprobado", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "pending": "Pendiente", "redirecting": "Redirigiendo...", "email_sent": "Recibirás un correo electrónico con los detalles", "access_product": "Acceder al producto", "thank_you": "¡<PERSON><PERSON><PERSON> por tu compra!", "confirmation_email": "Se ha enviado un correo de confirmación a", "order_details": "Detalles del pedido", "payment_method": "Método de pago", "amount_paid": "<PERSON><PERSON> paga<PERSON>", "transaction_id": "ID de transacción", "purchase_details": "Detalles de tu compra", "code_label": "Código", "payment_details": "Detalles del Pago", "amount": "Monto", "status": "Estado", "transaction_id_label": "ID de Transacción", "pix_auto_configured": "¡PIX Automático configurado exitosamente!", "pix_auto_configured_description": "Tu pago fue aprobado y el PIX Automático fue configurado. Los futuros cobros se procesarán automáticamente.", "pix_auto_active": "PIX Automático Activo", "pix_auto_active_description": "De ahora en adelante, tus cobros se procesarán automáticamente vía PIX. No necesitarás hacer nada - ¡el sistema se encargará de todo por ti!", "access_my_product": "ACCEDER A MI PRODUCTO", "email_confirmation_sent": "También hemos enviado una confirmación a tu correo electrónico.", "qrcode_generated": "¡Código QR generado exitosamente!", "finalize_payment_now": "<PERSON>ora solo completa el pago", "expires_in_label": "Este código expirará en:", "open_picpay_scan": "Abre PicPay en tu teléfono y escanea el código a continuación:", "qrcode_problem": "Si tienes algún problema al leer el código QR, actualiza la aplicación.", "already_paid": "Ya realicé el pago", "pay_now_pix_auto": "Pagar ahora con PIX Automático"}, "about": {"cpf_title": "¿Por qué solicitamos CPF/CNPJ?", "cpf_description": "Usamos este dato para garantizar la seguridad de tu compra, emitir facturas y identificar correctamente al titular.", "pix_security_title": "Pagos PIX", "pix_security_description": "PIX es un método instantáneo y seguro del Banco Central de Brasil. Revisa los datos antes de confirmar el pago."}, "in_app_browser": {"warning_title": "Abre este checkout en tu navegador", "warning_message": "Para una mejor experiencia y seguridad, abre esta página en tu navegador predeterminado.", "open_in_browser": "Abrir en el navegador"}, "notifications": {"payment_unavailable": "Pago no disponible", "payment_not_available": "Pago aún no disponible."}, "country": {"selector": {"label": "<PERSON><PERSON>", "select_country": "Selecciona tu país", "current_country": "<PERSON><PERSON> actual"}, "names": {"BR": "Brasil", "MX": "México", "AR": "Argentina", "CL": "Chile", "CO": "Colombia", "PE": "Perú", "US": "Estados Unidos", "OTHER": "Internacional"}, "documents": {"CPF": "CPF", "CNPJ": "CNPJ", "CPF_CNPJ": "CPF/CNPJ", "RUT": "RUT", "DNI": "DNI", "CUIT": "CUIT", "CURP": "CURP", "RFC": "RFC", "CC": "CC", "CE": "CE", "NIT": "NIT", "RUC": "RUC", "SSN": "SSN", "TAX_ID": "ID Fiscal"}}}