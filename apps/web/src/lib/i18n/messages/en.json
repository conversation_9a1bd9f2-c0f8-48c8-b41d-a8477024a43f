{"checkout": {"title": "Complete your purchase", "subtitle": "Fill in the information below to finish your order", "loading": "Loading checkout information...", "loading_checkout": "Loading checkout...", "order_summary": "Order summary", "coupon_code": "Discount code", "coupon_placeholder": "Enter your coupon", "coupon_applied": "Coupon applied", "coupon_success": "Coupon applied successfully!", "service_fee": "Service fee", "service_fee_tooltip": "Service fee charged by the platform", "total": "Total", "subtotal": "Subtotal", "discount": "Discount", "secure_purchase": "100% secure purchase", "recaptcha_notice": "This site is protected by reCAPTCHA and the Google Privacy Policy and Terms of Service apply", "contact_info_title": "Contact Information", "payment_method_title": "Payment Method", "processing_payment": "Processing payment...", "no_payment_methods": "No payment methods available", "not_found": "Not found"}, "common": {"continue": "Continue", "retry": "Try again", "save": "Save", "cancel": "Cancel", "close": "Close", "yes": "Yes", "no": "No", "loading": "Loading...", "checkoutTitle": "Secure checkout", "selectCheckout": "Select a valid checkout to continue.", "apply": "Apply", "remove": "Remove", "from": "from", "per": "per", "month": "month", "year": "year", "week": "week", "day": "day", "or": "or", "and": "and", "to": "to", "of": "of", "at": "at", "in": "in", "on": "on", "with": "with", "without": "without", "free": "Free", "discount_label": "OFF"}, "errors": {"validation": {"required": "This field is required", "invalid_email": "Enter a valid email", "invalid_phone": "Enter a valid phone number", "invalid_cpf": "Enter a valid CPF", "invalid_cnpj": "Enter a valid CNPJ", "invalid_cpf_cnpj": "Enter a valid CPF or CNPJ", "invalid_expiry": "Invalid expiration date", "invalid_cvv": "Invalid CVV", "invalid_card": "Invalid card number", "generic": "Invalid data", "invalid_date": "Enter a valid date"}, "payment": {"check_error": "Error checking payment:", "polling_error": "Error in automatic polling:"}}, "form": {"labels": {"name": "Full name", "email": "Email", "phone": "Phone", "cpf": "CPF", "cnpj": "CNPJ", "cpf_cnpj": "CPF/CNPJ", "card_number": "Card number", "card_expiry": "Expiration date", "card_cvv": "CVV", "card_holder_name": "Name on card", "installments": "Installments", "coupon": "Discount code", "zipcode": "ZIP code", "street": "Street", "number": "Number", "complement": "Complement", "neighborhood": "Neighborhood", "city": "City", "state": "State", "shipping": "Shipping"}, "placeholders": {"name": "Enter your full name", "email": "<EMAIL>", "phone": "(00) 00000-0000", "cpf_cnpj": "000.000.000-00", "card_number": "0000 0000 0000 0000", "card_expiry": "MM/YY", "card_cvv": "000", "card_holder_name": "Name as shown on the card", "coupon": "Enter your coupon code", "zipcode": "00000-000", "street": "Street, avenue, etc.", "number": "123", "complement": "Apartment, suite, etc.", "neighborhood": "Neighborhood name", "city": "City name", "state": "Select a state", "shipping": "Select a shipping option"}, "helpers": {"cpf_required": "Why do we ask for this information?", "phone_format": "Format: (AA) 00000-0000", "card_number_hint": "Enter digits only"}}, "payment": {"methods": {"credit_card": "Credit card", "pix": "PIX", "pix_auto": "Automatic PIX", "boleto": "Boleto", "picpay": "PicPay", "applepay": "Apple Pay", "googlepay": "Google Pay", "openfinance_nubank": "Nubank", "oxxo": "OXXO", "spei": "SPEI"}, "messages": {"processing": "Processing payment...", "waiting": "Waiting for payment", "success": "Payment confirmed!", "failed": "Payment declined", "expired": "Payment expired", "check_payment": "Check payment", "checking_payment": "Checking payment..."}, "installments": "Installments", "subscription": "Subscription", "installments_of": "installment of", "installments_with_interest": "with interest", "installments_without_interest": "without interest", "one_time": "one-time", "new_badge": "NEW", "loading_installments": "Loading...", "pix": {"title": "PIX", "subtitle": "Scan the QR code or copy the payment code", "qr_code_label": "PIX QR Code", "copy_code": "Copy PIX code", "code_copied": "Pix copied", "expires_in": "Expires in {time}", "waiting_payment": "Waiting for payment confirmation...", "instant_release": "Instant release", "easy_to_use": "It's simple, just use your bank app to pay with PIX", "scan_qrcode": "Scan the QR Code", "or_copy_code": "Or copy the code", "scan_instruction": "Scan QR Code", "open_bank_app": "Open your bank app", "scan_code": "Scan the code", "confirm_payment": "Confirm payment", "step_1": "Open your bank app and go to the <strong>PIX</strong> option", "step_2": "Choose the <strong>Pay / PIX copy and paste</strong> option", "step_3": "Scan the QR Code. If you prefer, copy and paste the code", "step_4": "Then, confirm the payment", "generated_success": "PIX generated successfully!", "finalize_payment": "Now just finalize the payment", "expires_in_label": "This code will expire in:", "approval_time": "Approval takes a maximum of 2 minutes", "already_paid": "I already made the payment"}, "pix_auto": {"title": "Automatic PIX", "subtitle": "Automatic payment via PIX", "description": "Authorize payment once and all future charges will be processed automatically", "step_4": "Then, authorize payment via <strong>Automatic PIX</strong>", "waiting_payment": "Waiting for Payment...", "copy_code": "Copy PIX code", "info_1": "With Automatic PIX, renewals are made recurrently, without worry", "info_2": "Authorize the payment once in your bank's app and we'll take care of the next ones for you", "advantages": {"title": "Advantages of Automatic PIX", "advantage_1": "Recurring payment without hassle", "advantage_2": "No need to enter details every time", "advantage_3": "Secure and protected by the Central Bank", "advantage_4": "Cancel anytime you want"}}, "boleto": {"title": "Boleto", "subtitle": "Copy the barcode or download the boleto", "barcode_label": "Barcode", "copy_code": "Copy code", "code_copied": "Code copied", "due_date": "Due date", "download": "Download boleto", "print": "Print boleto", "print_billet": "Print Boleto", "pay_at_bank": "Pay at bank", "payer_document": "Payer CPF/CNPJ", "instructions": "Payment instructions", "instruction_1": "Print the boleto and pay at the bank", "instruction_2": "Or pay via internet banking", "instruction_3": "Payment confirmation may take up to 3 business days", "amount_label": "Boleto amount", "due_date_label": "Due date", "barcode_number": "Boleto Code", "digital_access_info": "Access to the digital product will be sent via email after the boleto is paid. Payment confirmation may take up to 48 hours."}, "credit_card": {"title": "Credit card", "subtitle": "Enter your card details", "card_number": "Card number", "card_number_placeholder": "0000 0000 0000 0000", "cardholder_name": "Cardholder name", "cardholder_name_placeholder": "Name as shown on card", "expiration_date": "Expiration date", "expiration_placeholder": "MM/YY", "cvv": "CVV", "cvv_placeholder": "000", "save_card": "Save data for future purchases", "installments_label": "Number of installments", "select_installments": "Select number of installments", "secure_payment": "Your payment data is encrypted and processed securely."}, "picpay": {"title": "PicPay", "subtitle": "Scan the QR Code with PicPay app", "scan_qrcode": "Scan the QR Code", "waiting_payment": "Waiting for payment confirmation..."}, "applepay": {"title": "Apple Pay", "subtitle": "Pay with Apple Pay", "button_label": "Pay with Apple Pay"}, "googlepay": {"title": "Google Pay", "subtitle": "Pay with Google Pay", "button_label": "Pay with Google Pay"}, "nubank": {"title": "Nubank", "subtitle": "Pay with your Nubank account", "button_label": "Pay with Nubank"}, "oxxo": {"title": "Cash payment with OXXO", "description": "Pay at any OXXO store in Mexico. It's fast, easy and secure.", "how_it_works": "How it works?", "step_1": "Complete your purchase and generate the barcode", "step_2": "Go to the nearest OXXO store with the code", "step_3": "Tell the cashier you want to make an OXXO Pay service payment", "step_4": "Make the cash payment and keep your receipt", "processing_time": "Processing can take up to 24 hours after payment"}, "spei": {"title": "SPEI bank transfer", "description": "Instant electronic transfer between Mexican banks. Available 24/7.", "advantages": "SPEI advantages", "advantage_1": "Instant transfer (less than 1 minute)", "advantage_2": "Available 24 hours, 7 days a week", "advantage_3": "Automatic and immediate payment confirmation", "instant_confirmation": "Instant confirmation - Your order is approved automatically"}}, "address": {"title": "Address", "delivery_title": "Delivery address", "zipcode_label": "ZIP code", "zipcode_not_found": "ZIP code not found. Please fill in the fields manually.", "zipcode_searching": "Searching address...", "zipcode_found": "Address found!", "street_label": "Street", "number_label": "Number", "complement_label": "Complement (optional)", "neighborhood_label": "Neighborhood", "city_label": "City", "state_label": "State", "shipping_method": "Shipping method", "shipping_options": "Shipping options"}, "bump": {"title": "Limited offers", "add_product": "Add product", "selected": "Selected", "discount_label": "OFF"}, "product": {"cash": "cash"}, "data_privacy": {"why_we_ask": "Why do we ask for this?", "phone": {"title": "Why do we need your phone number?", "content": "Your phone number is used to send important notifications about your order, such as payment confirmation and product access. We will never share your data with third parties."}, "document": {"title": "Why do we need your document?", "content": "Your document is required for invoice issuance and to ensure the security of your purchase. Your data is encrypted and protected."}, "email": {"title": "Why do we need your email?", "content": "Your email will be used to send access to the purchased product and important information about your order."}}, "security": {"vendor": "the vendor", "processing_payment_for": "{brand} is processing this payment for {seller}", "secure_purchase": "100% secure purchase", "recaptcha_notice": "This site is protected by reCAPTCHA and the", "privacy_policy": "Privacy Policy", "terms_of_service": "Terms of Service", "from_google": "from Google", "purchase_terms": "Purchase Terms", "installment_with_interest": "* Installment with interest"}, "success": {"title": "Payment approved!", "subtitle": "Your order has been confirmed", "order_number": "Order number", "approved": "Approved", "rejected": "Declined", "pending": "Pending", "redirecting": "Redirecting...", "email_sent": "You will receive an email with the details", "access_product": "Access product", "thank_you": "Thank you for your purchase!", "confirmation_email": "A confirmation email has been sent to", "order_details": "Order details", "payment_method": "Payment method", "amount_paid": "Amount paid", "transaction_id": "Transaction ID", "purchase_details": "Your purchase details", "code_label": "Code", "payment_details": "Payment Details", "amount": "Amount", "status": "Status", "transaction_id_label": "Transaction ID", "pix_auto_configured": "Automatic PIX successfully configured!", "pix_auto_configured_description": "Your payment was approved and Automatic PIX was configured. Future charges will be processed automatically.", "pix_auto_active": "Automatic PIX Active", "pix_auto_active_description": "From now on, your charges will be processed automatically via PIX. You won't need to do anything - the system will take care of everything for you!", "access_my_product": "ACCESS MY PRODUCT", "email_confirmation_sent": "We've also sent a confirmation to your email.", "qrcode_generated": "QR Code generated successfully!", "finalize_payment_now": "Now just complete the payment", "expires_in_label": "This code will expire in:", "open_picpay_scan": "Open PicPay on your phone and scan the code below:", "qrcode_problem": "If you have any problems reading the QR code, update the app.", "already_paid": "I've already paid", "pay_now_pix_auto": "Pay now with Automatic PIX"}, "about": {"cpf_title": "Why do we ask for CPF/CNPJ?", "cpf_description": "We use this information to ensure purchase security, issue invoices, and correctly identify the customer.", "pix_security_title": "PIX payments", "pix_security_description": "PIX is an instant and secure method provided by the Central Bank of Brazil. Review the details before confirming the payment."}, "in_app_browser": {"warning_title": "Open this checkout in your browser", "warning_message": "For the best experience and security, open this page in your default browser.", "open_in_browser": "Open in browser"}, "notifications": {"payment_unavailable": "Payment unavailable", "payment_not_available": "Payment not available yet."}, "country": {"selector": {"label": "Country", "select_country": "Select your country", "current_country": "Current country"}, "names": {"BR": "Brazil", "MX": "Mexico", "AR": "Argentina", "CL": "Chile", "CO": "Colombia", "PE": "Peru", "US": "United States", "OTHER": "International"}, "documents": {"CPF": "CPF", "CNPJ": "CNPJ", "CPF_CNPJ": "CPF/CNPJ", "RUT": "RUT", "DNI": "DNI", "CUIT": "CUIT", "CURP": "CURP", "RFC": "RFC", "CC": "CC", "CE": "CE", "NIT": "NIT", "RUC": "RUC", "SSN": "SSN", "TAX_ID": "Tax ID"}}}