{"checkout": {"title": "Finalize sua compra", "subtitle": "Preencha os dados abaixo para concluir o pedido", "loading": "Carregando informações do checkout...", "loading_checkout": "Carregando checkout...", "order_summary": "Resumo do pedido", "coupon_code": "Cupom de desconto", "coupon_placeholder": "Digite seu cupom", "coupon_applied": "Cupom aplicado", "coupon_success": "Cupom aplicado com sucesso!", "service_fee": "Taxa de serviço", "service_fee_tooltip": "Taxa de serviço cobrada pela plataforma", "total": "Total", "subtotal": "Subtotal", "discount": "Desconto", "secure_purchase": "Compra 100% segura", "recaptcha_notice": "Este site é protegido pelo reCAPTCHA e pela Política de Privacidade e Termos de Serviço do Google", "contact_info_title": "Seus dados", "payment_method_title": "Pagamento", "processing_payment": "<PERSON><PERSON>o pagamento...", "no_payment_methods": "Nenhum método de pagamento disponível", "not_found": "Não encontrado"}, "common": {"continue": "<PERSON><PERSON><PERSON><PERSON>", "retry": "Tentar novamente", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "yes": "<PERSON>m", "no": "Não", "loading": "Carregando...", "checkoutTitle": "Checkout seguro", "selectCheckout": "Selecione um checkout válido para continuar.", "apply": "Aplicar", "remove": "Remover", "from": "de", "per": "por", "month": "mês", "year": "ano", "week": "semana", "day": "dia", "or": "ou", "and": "e", "to": "para", "of": "de", "at": "em", "in": "no", "on": "em", "with": "com", "without": "sem", "free": "<PERSON><PERSON><PERSON><PERSON>", "discount_label": "OFF"}, "errors": {"validation": {"required": "Este campo é obrigatório", "invalid_email": "Digite um email válido", "invalid_phone": "Digite um telefone válido", "invalid_cpf": "Digite um CPF válido", "invalid_cnpj": "Digite um CNPJ válido", "invalid_cpf_cnpj": "Digite um CPF ou CNPJ válido", "invalid_expiry": "Data de validade inválida", "invalid_cvv": "CVV inválido", "invalid_card": "Número do cartão inválido", "generic": "<PERSON><PERSON>", "invalid_date": "Digite uma data válida"}, "payment": {"check_error": "Erro ao verificar pagamento:", "polling_error": "Erro no polling automático:"}}, "form": {"labels": {"name": "Nome completo", "email": "E-mail", "phone": "Telefone", "cpf": "CPF", "cnpj": "CNPJ", "cpf_cnpj": "CPF/CNPJ", "card_number": "Número do cartão", "card_expiry": "Validade", "card_cvv": "CVV", "card_holder_name": "Nome no cartão", "installments": "<PERSON><PERSON><PERSON><PERSON>", "coupon": "Cupom de desconto", "zipcode": "CEP", "street": "Logradouro", "number": "Número", "complement": "Complemento", "neighborhood": "Bairro", "city": "Cidade", "state": "Estado", "shipping": "<PERSON><PERSON>"}, "placeholders": {"name": "Digite seu nome completo", "email": "<EMAIL>", "phone": "(00) 00000-0000", "cpf_cnpj": "000.000.000-00", "card_number": "0000 0000 0000 0000", "card_expiry": "MM/AA", "card_cvv": "000", "card_holder_name": "Nome como está no cartão", "coupon": "Digite o código do cupom", "zipcode": "00000-000", "street": "Rua, avenida, etc.", "number": "123", "complement": "Apartamento, bloco, etc.", "neighborhood": "Nome do bairro", "city": "Nome da cidade", "state": "Selecione o estado", "shipping": "Selecione uma opção de envio"}, "helpers": {"cpf_required": "Por que pedimos esse dado?", "phone_format": "Formato: (DD) 00000-0000", "card_number_hint": "Digite apenas números"}}, "payment": {"methods": {"credit_card": "Cartão de crédito", "pix": "PIX", "pix_auto": "PIX Automático", "boleto": "Boleto", "picpay": "PicPay", "applepay": "Apple Pay", "googlepay": "Google Pay", "openfinance_nubank": "Nubank"}, "messages": {"processing": "<PERSON><PERSON>o pagamento...", "waiting": "<PERSON><PERSON><PERSON><PERSON>aga<PERSON>", "success": "Pagamento confirmado!", "failed": "Pagamento recusado", "expired": "Pagamento expirado", "check_payment": "Verificar pagamento", "checking_payment": "Verificando pagamento..."}, "installments": "<PERSON><PERSON><PERSON><PERSON>", "subscription": "Assinatura", "installments_of": "parcela de", "installments_with_interest": "com juros", "installments_without_interest": "sem juros", "one_time": "à vista", "new_badge": "NOVO", "loading_installments": "Carregando...", "pix": {"title": "PIX", "subtitle": "Escaneie o QR Code ou copie o código", "qr_code_label": "QR Code PIX", "copy_code": "Copiar código PIX", "code_copied": "<PERSON>x copiado", "expires_in": "Expira em {time}", "waiting_payment": "Aguardando confirmação do pagamento...", "instant_release": "Liberação imediata", "easy_to_use": "É simples, só usar o aplicativo de seu banco para pagar Pix", "scan_qrcode": "Escaneie o QR Code", "or_copy_code": "Ou copie o código", "scan_instruction": "Escanear QR Code", "open_bank_app": "Abra o app do seu banco", "scan_code": "Escaneie o código", "confirm_payment": "Confirme o pagamento", "step_1": "Abra o app do seu banco e entre na opção <strong>Pix</strong>", "step_2": "Escolha a opção <strong>Pagar / Pix copia e cola</strong>", "step_3": "Escaneie o QR Code. Se preferir, copie e cole o código", "step_4": "<PERSON><PERSON><PERSON>, confirme o pagamento", "generated_success": "PIX gerado com sucesso!", "finalize_payment": "Agora é só finalizar o pagamento", "expires_in_label": "Este código expirará em:", "approval_time": "A aprovação leva no máximo 2 minutos", "already_paid": "Já fiz o pagamento"}, "pix_auto": {"title": "PIX Automático", "subtitle": "Pagamento automático via PIX", "description": "Autorize o pagamento uma única vez e todas as cobranças futuras serão processadas automaticamente", "step_4": "<PERSON><PERSON><PERSON>, autorize o pagamento via <strong>Pix Automático</strong>", "waiting_payment": "A<PERSON><PERSON><PERSON>...", "copy_code": "<PERSON><PERSON><PERSON> c<PERSON>", "info_1": "Com o Pix Automático, as renovações são feitas de forma recorrente, sem preocupação", "info_2": "Autorize uma vez o pagamento no app do seu banco e os próximos a gente cuida para você", "advantages": {"title": "Vantagens do PIX Automático", "advantage_1": "Pagamento recorrente sem burocracia", "advantage_2": "Não precisa inserir dados toda vez", "advantage_3": "Seguro e protegido pelo Banco Central", "advantage_4": "<PERSON><PERSON>e quando quiser"}}, "boleto": {"title": "Bolet<PERSON>", "subtitle": "Copie o código de barras ou baixe o boleto", "barcode_label": "Código de <PERSON>", "copy_code": "<PERSON><PERSON>r c<PERSON>", "code_copied": "<PERSON><PERSON><PERSON> copiado", "due_date": "Vencimento", "download": "Baixar boleto", "print": "<PERSON><PERSON><PERSON><PERSON> bole<PERSON>", "print_billet": "<PERSON><PERSON><PERSON><PERSON>", "pay_at_bank": "Pagar no banco", "payer_document": "CPF/CNPJ do Pagador", "instructions": "Instruções de pagamento", "instruction_1": "Imprima o boleto e pague no banco", "instruction_2": "Ou pague pela internet banking", "instruction_3": "O pagamento pode levar até 3 dias úteis para ser confirmado", "amount_label": "Valor do boleto", "due_date_label": "Data de vencimento", "barcode_number": "Código do Boleto", "digital_access_info": "O acesso ao produto digital será enviado via e-mail depois que o boleto for pago. A confirmação do pagamento pode levar até 48 horas."}, "credit_card": {"title": "Cartão de crédito", "subtitle": "Preencha os dados do seu cartão", "card_number": "Número do cartão", "card_number_placeholder": "0000 0000 0000 0000", "cardholder_name": "Nome no cartão", "cardholder_name_placeholder": "Nome como está no cartão", "expiration_date": "Validade", "expiration_placeholder": "MM/AA", "cvv": "CVV", "cvv_placeholder": "000", "save_card": "<PERSON><PERSON> dados para as próximas compras", "installments_label": "Número de parcelas", "select_installments": "Selecione o número de parcelas", "secure_payment": "Os seus dados de pagamento são criptografados e processados de forma segura."}, "picpay": {"title": "PicPay", "subtitle": "Escaneie o QR Code com o app PicPay", "scan_qrcode": "Escaneie o QR Code", "waiting_payment": "Aguardando confirmação do pagamento..."}, "applepay": {"title": "Apple Pay", "subtitle": "Pague com Apple Pay", "button_label": "Pagar com Apple Pay"}, "googlepay": {"title": "Google Pay", "subtitle": "Pague com Google Pay", "button_label": "Pagar com Google Pay"}, "nubank": {"title": "Nubank", "subtitle": "Pague com sua conta Nubank", "button_label": "Pagar com Nubank"}}, "address": {"title": "Endereço", "delivery_title": "Endereço de entrega", "zipcode_label": "CEP", "zipcode_not_found": "CEP não encontrado. Preencha os campos manualmente.", "zipcode_searching": "<PERSON><PERSON><PERSON>...", "zipcode_found": "Endereço encontrado!", "street_label": "Logradouro", "number_label": "Número", "complement_label": "Complemento (opcional)", "neighborhood_label": "Bairro", "city_label": "Cidade", "state_label": "Estado", "shipping_method": "M<PERSON><PERSON><PERSON>", "shipping_options": "Opções de envio"}, "bump": {"title": "Ofertas limitadas", "add_product": "Ad<PERSON><PERSON><PERSON> produto", "selected": "Selecionado", "discount_label": "OFF"}, "product": {"cash": "à vista"}, "data_privacy": {"why_we_ask": "Por que pedimos esse dado?", "phone": {"title": "Por que pedimos seu telefone?", "content": "Seu telefone é usado para enviar notificações importantes sobre seu pedido, como confirmação de pagamento e acesso ao produto. Nunca compartilharemos seus dados com terceiros."}, "document": {"title": "Por que pedimos seu CPF/CNPJ?", "content": "O CPF/CNPJ é necessário para emissão da nota fiscal e para garantir a segurança da sua compra. Seus dados são criptografados e protegidos."}, "email": {"title": "Por que pedimos seu e-mail?", "content": "Seu e-mail será usado para enviar o acesso ao produto adquirido e informações importantes sobre seu pedido."}}, "security": {"vendor": "o vendedor", "processing_payment_for": "{brand} está processando este pagamento para {seller}", "secure_purchase": "Compra 100% segura", "recaptcha_notice": "Este site é protegido pelo reCAPTCHA e pela", "privacy_policy": "Política de Privacidade", "terms_of_service": "Termos de Serviço", "from_google": "do Google", "purchase_terms": "Termos de Compra", "installment_with_interest": "* Parcelamento com acréscimo"}, "success": {"title": "Pagamento aprovado!", "subtitle": "Seu pedido foi confirmado com sucesso", "order_number": "Número do pedido", "approved": "<PERSON><PERSON><PERSON>", "rejected": "Recusado", "pending": "Pendente", "redirecting": "Redirecionando...", "email_sent": "Você receberá um email com os detalhes", "access_product": "Acessar produto", "thank_you": "<PERSON><PERSON><PERSON> pela sua compra!", "confirmation_email": "Um email de confirmação foi enviado para", "order_details": "Detalhes do pedido", "payment_method": "Método de pagamento", "amount_paid": "Valor pago", "transaction_id": "ID da transação", "purchase_details": "Detalhes da sua compra", "code_label": "Código", "payment_details": "Detalhes do Pagamento", "amount": "Valor", "status": "Status", "transaction_id_label": "ID da Transação", "pix_auto_configured": "PIX Automático configurado com sucesso!", "pix_auto_configured_description": "Seu pagamento foi aprovado e o PIX Automático foi configurado. As próximas cobranças serão feitas automaticamente.", "pix_auto_active": "PIX Automático Ativo", "pix_auto_active_description": "A partir de agora, suas cobranças serão processadas automaticamente via PIX. Você não precisará fazer nada - o sistema cuidará de tudo para você!", "access_my_product": "ACESSAR MEU PRODUTO", "email_confirmation_sent": "Também enviamos uma confirmação para o seu e-mail.", "qrcode_generated": "QRCode gerado com sucesso!", "finalize_payment_now": "Agora é só finalizar o pagamento", "expires_in_label": "Este código expirará em:", "open_picpay_scan": "Abra o PicPay em seu telefone e escaneie o código abaixo:", "qrcode_problem": "Se tiver algum problema com a leitura do QR code, atualize o aplicativo.", "already_paid": "Já fiz o pagamento", "pay_now_pix_auto": "Pagar agora com o Pix Automático"}, "about": {"cpf_title": "Por que pedimos CPF/CNPJ?", "cpf_description": "Utilizamos esse dado para garantir a segurança da sua compra, emissão de nota fiscal e identificação correta do titular.", "pix_security_title": "Pagamentos PIX", "pix_security_description": "O PIX é um método instantâneo e seguro do Banco Central do Brasil. Verifique os dados antes de confirmar o pagamento."}, "in_app_browser": {"warning_title": "Abra este checkout no navegador", "warning_message": "Para melhor experiência e segurança, recomendamos abrir esta página no seu navegador padrão.", "open_in_browser": "Abrir no navegador"}, "notifications": {"payment_unavailable": "Pagamento indisponível", "payment_not_available": "Pagamento não disponível ainda."}, "country": {"selector": {"label": "<PERSON><PERSON>", "select_country": "Selecione seu país", "current_country": "<PERSON><PERSON> at<PERSON>"}, "names": {"BR": "Brasil", "MX": "México", "AR": "Argentina", "CL": "Chile", "CO": "Colômbia", "PE": "Peru", "US": "Estados Unidos", "OTHER": "Internacional"}, "documents": {"CPF": "CPF", "CNPJ": "CNPJ", "CPF_CNPJ": "CPF/CNPJ", "RUT": "RUT", "DNI": "DNI", "CUIT": "CUIT", "CURP": "CURP", "RFC": "RFC", "CC": "CC", "CE": "CE", "NIT": "NIT", "RUC": "RUC", "SSN": "SSN", "TAX_ID": "Tax ID"}}}