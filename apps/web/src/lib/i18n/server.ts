import { cookies, headers } from "next/headers";

import { LOCALE_COOKIE, type Locale, normalizeLocale, isSupportedLocale } from "./config";

export function getLocaleFromCookies(): Locale | undefined {
	const cookieStore = cookies();
	const value = cookieStore.get(LOCALE_COOKIE)?.value;
	if (isSupportedLocale(value)) {
		return value;
	}

	return undefined;
}

export function getClientPreferredLocale(): Locale | undefined {
	const acceptLanguage = headers().get("accept-language");
	if (!acceptLanguage) {
		return undefined;
	}

	const [primary] = acceptLanguage.split(",");
	if (!primary) {
		return undefined;
	}

	return normalizeLocale(primary.trim());
}


