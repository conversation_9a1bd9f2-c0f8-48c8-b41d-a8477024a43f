"use client";

import type {
	PaymentPayload,
	PayData,
	Payment,
	InstallmentsData,
	CouponData,
} from "@/types";

/**
 * Client-side API functions for checkout operations.
 * These functions use fetch and can be called from client components.
 */

/**
 * Start a payment
 */
export async function startPayment(
	productShortId: string,
	payload: PaymentPayload,
): Promise<PayData> {
	const response = await fetch(`/api/checkout/${productShortId}/`, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify(payload),
		credentials: "include",
	});

	if (!response.ok) {
		const errorData = await response.json().catch(() => ({}));
		throw {
			response: {
				status: response.status,
				data: errorData,
			},
			message: errorData.detail || "Payment failed",
		};
	}

	return response.json();
}

/**
 * Get payment status
 */
export async function getPaymentStatus(paymentId: string): Promise<Payment> {
	const response = await fetch(`/api/payment/status/${paymentId}/`, {
		method: "GET",
		credentials: "include",
	});

	if (!response.ok) {
		throw new Error(`Failed to get payment status (${response.status})`);
	}

	return response.json();
}

/**
 * Get installments for a product
 */
export async function getInstallments(
	productId: string,
	total: number,
): Promise<InstallmentsData[]> {
	const response = await fetch(
		`/api/checkout/installments/${productId}/?total=${total}`,
		{
			method: "GET",
		},
	);

	if (!response.ok) {
		throw new Error(`Failed to get installments (${response.status})`);
	}

	return response.json();
}

/**
 * Validate a coupon
 */
export async function validateCoupon({
	code,
	offerId,
}: {
	code: string;
	offerId?: string;
}): Promise<CouponData> {
	const response = await fetch("/api/coupons/validate/", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify({ code, offerId }),
	});

	if (!response.ok) {
		const errorData = await response.json().catch(() => ({}));
		throw new Error(errorData.detail || "Coupon validation failed");
	}

	return response.json();
}

/**
 * Generate device fingerprint
 */
export async function generateFingerprint(): Promise<string> {
	try {
		// Import ClientJS dynamically only on client-side
		const { ClientJS } = await import("clientjs");
		const client = new ClientJS();
		const fingerprint = client.getFingerprint();
		return String(fingerprint);
	} catch (error) {
		console.error("Error generating fingerprint", error);
		return "";
	}
}

/**
 * Generate credit card token (using NoxPay/HopyPay)
 */
export async function generateCreditCardToken(card: {
	number: string;
	holderName: string;
	expMonth: string;
	expYear: string;
	cvv: string;
}): Promise<string> {
	// @ts-ignore - NoxPay is loaded externally
	if (typeof window !== "undefined" && window.NoxPay) {
		// Get public key from environment
		const publicKey = process.env.NEXT_PUBLIC_HOPYPAY_PUBLIC_KEY;
		if (!publicKey) {
			throw new Error("HopyPay public key not configured");
		}

		// @ts-ignore
		window.NoxPay.setPublicKey(publicKey);
		// @ts-ignore
		return await window.NoxPay.encrypt(card);
	}

	throw new Error("NoxPay not loaded");
}

