export interface AddressData {
	street: string;
	neighborhood: string;
	city: string;
	state: string;
	complement?: string;
}

interface ViaCepResponse {
	cep: string;
	logradouro: string;
	complemento: string;
	bairro: string;
	localidade: string;
	uf: string;
	ibge: string;
	gia: string;
	ddd: string;
	siafi: string;
	erro?: boolean;
}

interface BrasilApiResponse {
	cep: string;
	state: string;
	city: string;
	neighborhood: string;
	street: string;
	service: string;
	location: {
		type: string;
		coordinates: {
			longitude: string;
			latitude: string;
		};
	};
}

interface OpenCepResponse {
	cep: string;
	logradouro: string;
	complemento: string;
	bairro: string;
	cidade: string;
	estado: string;
	ibge: string;
	ddd: string;
	siafi: string;
}

class CepService {
	private normalizeCep(cep: string): string {
		return cep.replace(/\D/g, '');
	}

	private async fetchViaCep(cep: string): Promise<AddressData | null> {
		try {
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), 5000);

			const response = await fetch(
				`https://viacep.com.br/ws/${cep}/json/`,
				{ signal: controller.signal }
			);

			clearTimeout(timeoutId);

			if (!response.ok) {
				return null;
			}

			const data: ViaCepResponse = await response.json();

			if (data.erro) {
				return null;
			}

			if (!data.localidade || !data.uf) {
				return null;
			}

			return {
				street: data.logradouro || '',
				neighborhood: data.bairro || '',
				city: data.localidade,
				state: data.uf,
				complement: data.complemento || undefined
			};
		} catch (error) {
			console.warn('ViaCEP API failed:', error);
			return null;
		}
	}

	private async fetchBrasilApi(cep: string): Promise<AddressData | null> {
		try {
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), 5000);

			const response = await fetch(
				`https://brasilapi.com.br/api/cep/v1/${cep}`,
				{ signal: controller.signal }
			);

			clearTimeout(timeoutId);

			if (!response.ok) {
				return null;
			}

			const data: BrasilApiResponse = await response.json();

			if (!data.city || !data.state) {
				return null;
			}

			return {
				street: data.street || '',
				neighborhood: data.neighborhood || '',
				city: data.city,
				state: data.state
			};
		} catch (error) {
			console.warn('BrasilAPI failed:', error);
			return null;
		}
	}

	private async fetchOpenCep(cep: string): Promise<AddressData | null> {
		try {
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), 5000);

			const response = await fetch(
				`https://opencep.com/v1/${cep}`,
				{ signal: controller.signal }
			);

			clearTimeout(timeoutId);

			if (!response.ok) {
				return null;
			}

			const data: OpenCepResponse = await response.json();

			if (!data.cidade || !data.estado) {
				return null;
			}

			return {
				street: data.logradouro || '',
				neighborhood: data.bairro || '',
				city: data.cidade,
				state: data.estado,
				complement: data.complemento || undefined
			};
		} catch (error) {
			console.warn('OpenCEP API failed:', error);
			return null;
		}
	}

	async searchCep(cep: string): Promise<AddressData | null> {
		const normalizedCep = this.normalizeCep(cep);
		if (normalizedCep.length !== 8) {
			return null;
		}

		const apis = [
			{ name: 'ViaCEP', fn: () => this.fetchViaCep(normalizedCep) },
			{ name: 'BrasilAPI', fn: () => this.fetchBrasilApi(normalizedCep) },
			{ name: 'OpenCEP', fn: () => this.fetchOpenCep(normalizedCep) }
		];

		for (const api of apis) {
			try {
				console.log(`Trying ${api.name} for CEP ${normalizedCep}...`);
				const address = await api.fn();
				if (address) {
					console.log(`${api.name} returned address data:`, address);
					return address;
				}
				console.log(`${api.name} returned null, trying next API...`);
			} catch (error) {
				console.warn(`${api.name} failed:`, error);
				continue;
			}
		}

		console.warn(`All APIs failed for CEP ${normalizedCep}`);
		return null;
	}

	formatCep(cep: string): string {
		const normalized = this.normalizeCep(cep);
		return normalized.replace(/(\d{5})(\d{3})/, '$1-$2');
	}

	isValidCep(cep: string): boolean {
		const normalized = this.normalizeCep(cep);
		return normalized.length === 8 && /^\d{8}$/.test(normalized);
	}
}

export const cepService = new CepService();

