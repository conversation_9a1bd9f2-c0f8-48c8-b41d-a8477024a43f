import "server-only";

import { cookies, headers } from "next/headers";

import { getCheckoutBaseUrl, getApiHeaders } from "./config";

export type CheckoutData = Record<string, unknown>;

export async function getCheckoutData(
	id: string,
	affiliateShortId?: string,
): Promise<CheckoutData> {
	const baseUrl = getCheckoutBaseUrl();

	if (!baseUrl) {
		throw new Error("Checkout API base URL is not configured.");
	}

	const url = new URL(`/api/product/checkout/${id}/`, baseUrl);

	if (affiliateShortId) {
		url.searchParams.set("affiliate", affiliateShortId);
	}

	const forwardedHeaders = await getForwardedHeaders();
	const response = await fetch(url.toString(), {
		method: "GET",
		headers: {
			...getApiHeaders(),
			...forwardedHeaders,
		},
		credentials: "include",
	});

	if (!response.ok) {
		throw new Error(`Failed to load checkout data (${response.status})`);
	}

	return response.json();
}

async function getForwardedHeaders(): Promise<HeadersInit> {
	const cookieHeader = await serializeCookies();
	const headersList = await headers();
	const forwardedHost = headersList.get("host");

	return {
		...(cookieHeader ? { cookie: cookieHeader } : {}),
		...(forwardedHost ? { "x-forwarded-host": forwardedHost } : {}),
	};
}

async function serializeCookies(): Promise<string | undefined> {
	const cookieStore = await cookies();
	const allCookies = cookieStore.getAll();
	if (!allCookies.length) {
		return undefined;
	}

	return allCookies
		.map((cookie: { name: string; value: string }) => `${cookie.name}=${cookie.value}`)
		.join("; ");
}



