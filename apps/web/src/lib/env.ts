/**
 * Environment variables configuration
 * 
 * In Next.js:
 * - Variables without NEXT_PUBLIC_ prefix are server-side only
 * - Variables with NEXT_PUBLIC_ prefix are exposed to the client
 */

// Server-side only variables
export const getCheckoutApiUrl = () => process.env.CHECKOUT_API_URL;
export const getBaseCheckoutApiUrl = () => process.env.BASE_CHECKOUT_API_URL;
export const getApiBaseUrl = () => process.env.API_BASE_URL;
export const getGeoipResolverUrl = () => process.env.GEOIP_RESOLVER_URL;
export const getGeoipResolverToken = () => process.env.GEOIP_RESOLVER_TOKEN;

// Client-side accessible variables (with NEXT_PUBLIC_ prefix)
export const getBrandingImagesUrl = () =>
	process.env.NEXT_PUBLIC_BRANDING_IMAGES_URL;
export const getBranding = () => process.env.NEXT_PUBLIC_BRANDING;
export const getCaktoUrl = () => process.env.NEXT_PUBLIC_CAKTO_URL;
export const getHopyPayPublicKey = () =>
	process.env.NEXT_PUBLIC_HOPYPAY_PUBLIC_KEY;
export const getPostHogKey = () => process.env.NEXT_PUBLIC_POSTHOG_KEY;
export const getPostHogHost = () =>
	process.env.NEXT_PUBLIC_POSTHOG_HOST || "https://us.i.posthog.com";
export const get3dsProvider = () =>
	process.env.NEXT_PUBLIC_3DS_PROVIDER || "cielo";
export const getCielo3dsApiVersion = () =>
	process.env.NEXT_PUBLIC_CIELO_3DS_API_VERSION || "v2";
export const getCielo3dsScript = () =>
	process.env.NEXT_PUBLIC_CIELO_3DS_SCRIPT;
export const getPagarme3dsScript = () =>
	process.env.NEXT_PUBLIC_PAGARME_3DS_SCRIPT;
export const getNethoneScriptSrc = () =>
	process.env.NEXT_PUBLIC_NETHONE_SCRIPT_SRC;
export const getGooglePayEnvironment = () =>
	process.env.NEXT_PUBLIC_GOOGLEPAY_ENVIRONMENT;
export const getGooglePayGateway = () =>
	process.env.NEXT_PUBLIC_GOOGLEPAY_GATEWAY;
export const getGooglePayGatewayMerchantId = () =>
	process.env.NEXT_PUBLIC_GOOGLEPAY_GATEWAY_MERCHANT_ID;
export const getGooglePayMerchantName = () =>
	process.env.NEXT_PUBLIC_GOOGLEPAY_MERCHANT_NAME;
export const getGooglePayMerchantId = () =>
	process.env.NEXT_PUBLIC_GOOGLEPAY_MERCHANT_ID;
export const getGoogleMapsApiKey = () =>
	process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
export const getPixCheckInterval = () =>
	Number(process.env.NEXT_PUBLIC_PIX_CHECK_INTERVAL || 5000);
export const getPixCheckDuration = () =>
	Number(process.env.NEXT_PUBLIC_PIX_CHECK_DURATION || 300000);
export const getMockApiUrl = () => process.env.NEXT_PUBLIC_MOCK_API_URL;
export const getEnvironment = () =>
	process.env.NEXT_PUBLIC_ENVIRONMENT || "development";
export const isDevelopment = () =>
	process.env.NODE_ENV === "development" ||
	getEnvironment() === "development";

// Helper for client-side environment check
export const isClientDevelopment = () => {
	if (typeof window === 'undefined') return false;
	return (
		window.location.hostname === 'localhost' ||
		window.location.hostname === '127.0.0.1' ||
		window.location.hostname.includes('localhost') ||
		isDevelopment()
	);
};

// Country and payment provider configuration
export const getGeoipApiUrl = () =>
	process.env.NEXT_PUBLIC_GEOIP_API_URL || "https://ipapi.co";
export const getEbanxPublicKey = () =>
	process.env.NEXT_PUBLIC_EBANX_PUBLIC_KEY || "";

