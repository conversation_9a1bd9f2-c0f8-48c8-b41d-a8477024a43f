import posthog from 'posthog-js';

const KEY = '@cakto-checkout:checkout-form';

const safeTrack = (callback: () => void) => {
  try {
    callback();
  } catch (error) {
    console.warn('PostHog tracking error:', error);
  }
};

export const trackEvent = (eventName: string, properties?: Record<string, any>) => {
  safeTrack(() => posthog.capture(eventName, properties));
};

export const trackPageView = (url?: string) => {
  safeTrack(() => posthog.capture('$pageview', { url }));
};

export const identifyUser = (userId: string, userProperties?: Record<string, any>) => {
  safeTrack(() => posthog.identify(userId, userProperties));
};

export const resetUser = () => {
  safeTrack(() => posthog.reset());
};

export const trackCheckoutStarted = (orderId: string, total: number, products: any[]) => {
  safeTrack(() => trackEvent('checkout_started', {
    order_id: orderId,
    total_value: total,
    products
  }));
};

export const trackCheckoutCompleted = (orderId: string, total: number, paymentMethod: string) => {
  safeTrack(() => trackEvent('checkout_completed', {
    order_id: orderId,
    total_value: total,
    payment_method: paymentMethod
  }));
};

export const trackPaymentSelected = (paymentMethod: string) => {
  safeTrack(() => trackEvent('payment_method_selected', {
    payment_method: paymentMethod
  }));
};

export const trackFormError = (formName: string, errorField: string, errorMessage: string) => {
  safeTrack(() => trackEvent('form_error', {
    form_name: formName,
    error_field: errorField,
    error_message: errorMessage
  }));
};

function safeParseJSON(str: any) {
  try { return JSON.parse(str); } catch { return null; }
}

function tryDecode(str: any) {
  try { return decodeURIComponent(str); } catch { return str; }
}

function normalizeBundle(b: any) {
  if (!b || typeof b !== 'object') return {};
  const out = { ...b };
  if (typeof out.calculatedInstallments === 'string') {
    const parsed = safeParseJSON(out.calculatedInstallments);
    if (parsed) out.calculatedInstallments = parsed;
  }
  return out;
}

function getFromCookie() {
  if (typeof document === 'undefined') return null;
  const raw = (document.cookie || '')
    .split('; ')
    .find((c) => c.startsWith(`${KEY}=`));
  if (!raw) return null;
  const val = raw.slice(KEY.length + 1);
  return normalizeBundle(safeParseJSON(tryDecode(val)));
}

function getFromLocalStorage() {
  if (typeof window === 'undefined') return null;
  const raw = window.localStorage?.getItem(KEY) || '';
  if (!raw) return null;
  // Alguns setups salvam URL-encoded
  const decoded = tryDecode(raw);
  return normalizeBundle(safeParseJSON(decoded));
}

function score(b: any) {
  if (!b) return -1;
  let s = 0;
  if (b.email) s += 1;
  if (b.phone) s += 1;
  if (b.name) s += 1;
  return s;
}

export function getCheckoutData() {
  const c1 = getFromLocalStorage();
  const c2 = getFromCookie();
  return score(c1) >= score(c2) ? (c1 || {}) : (c2 || {});
}

export function getCheckoutField(field: any) {
  const data = getCheckoutData();
  const v = data?.[field];
  return v !== '' && v != null ? String(v) : undefined;
}

export function filterNullish(obj: Object) {
  return Object.fromEntries(
    Object.entries(obj).filter(([, v]) => v !== undefined && v !== null && v !== '')
  );
}

export default {
  trackEvent,
  trackPageView,
  identifyUser,
  resetUser,
  trackCheckoutStarted,
  trackCheckoutCompleted,
  trackPaymentSelected,
  trackFormError
};

