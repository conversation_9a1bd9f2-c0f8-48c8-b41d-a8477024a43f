"use client";

export enum ErrorLevel {
	WARNING = "warning",
	ERROR = "error",
	CRITICAL = "critical",
}

export interface ErrorLogEntry {
	level: ErrorLevel;
	message: string;
	error?: Error | unknown;
	context?: Record<string, unknown>;
	timestamp: string;
	userAgent?: string;
	url?: string;
	userId?: string;
	sessionId?: string;
	stack?: string;
}

class ErrorLogger {
	private isDevelopment = process.env.NODE_ENV === "development";

	private createLogEntry(
		level: ErrorLevel,
		message: string,
		error?: Error | unknown,
		context?: Record<string, unknown>,
	): ErrorLogEntry {
		const entry: ErrorLogEntry = {
			level,
			message,
			error:
				error instanceof Error
					? {
							name: error.name,
							message: error.message,
							stack: error.stack,
						}
					: error,
			context,
			timestamp: new Date().toISOString(),
			userAgent:
				typeof navigator !== "undefined" ? navigator.userAgent : undefined,
			url: typeof window !== "undefined" ? window.location.href : undefined,
			userId: this.getUserId(),
			sessionId: this.getSessionId(),
			stack: error instanceof Error ? error.stack : undefined,
		};

		return entry;
	}

	private getUserId(): string | undefined {
		if (typeof window === "undefined") return undefined;
		try {
			return localStorage.getItem("user_id") || undefined;
		} catch {
			return undefined;
		}
	}

	private getSessionId(): string | undefined {
		if (typeof window === "undefined") return undefined;
		try {
			return sessionStorage.getItem("session_id") || undefined;
		} catch {
			return undefined;
		}
	}

	private logToConsole(entry: ErrorLogEntry): void {
		if (!this.isDevelopment) return;

		const style = this.getConsoleStyle(entry.level);
		const prefix = `[${entry.level.toUpperCase()}] ${entry.timestamp}`;

		console.group(`%c${prefix}`, style);
		console.log("Message:", entry.message);

		if (entry.error) {
			console.error("Error:", entry.error);
		}

		if (entry.context) {
			console.log("Context:", entry.context);
		}

		if (entry.stack) {
			console.trace("Stack trace:", entry.stack);
		}

		console.groupEnd();
	}

	private getConsoleStyle(level: ErrorLevel): string {
		switch (level) {
			case ErrorLevel.WARNING:
				return "color: orange; font-weight: bold;";
			case ErrorLevel.ERROR:
				return "color: red; font-weight: bold;";
			case ErrorLevel.CRITICAL:
				return "color: white; background-color: red; font-weight: bold; padding: 2px 4px;";
			default:
				return "color: gray;";
		}
	}

	public warning(
		message: string,
		error?: Error | unknown,
		context?: Record<string, unknown>,
	): void {
		const entry = this.createLogEntry(ErrorLevel.WARNING, message, error, context);
		this.logToConsole(entry);
	}

	public error(
		message: string,
		error?: Error | unknown,
		context?: Record<string, unknown>,
	): void {
		const entry = this.createLogEntry(ErrorLevel.ERROR, message, error, context);
		this.logToConsole(entry);
	}

	public critical(
		message: string,
		error?: Error | unknown,
		context?: Record<string, unknown>,
	): void {
		const entry = this.createLogEntry(
			ErrorLevel.CRITICAL,
			message,
			error,
			context,
		);
		this.logToConsole(entry);
	}

	public logAxiosError(
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		error: any,
		context?: Record<string, unknown>,
	): void {
		const axiosContext = {
			...context,
			status: error.response?.status,
			statusText: error.response?.statusText,
			url: error.config?.url,
			method: error.config?.method,
			data: error.config?.data,
			headers: error.config?.headers,
		};

		if (error.response?.status >= 500) {
			this.critical("Server Error (5xx)", error, axiosContext);
		} else if (error.response?.status >= 400) {
			this.error("Client Error (4xx)", error, axiosContext);
		} else {
			this.warning("Network Error", error, axiosContext);
		}
	}

	public log3DSError(
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		error: any,
		provider: string,
		context?: Record<string, unknown>,
	): void {
		const threeDSContext = {
			...context,
			provider,
			errorType: "3DS_AUTHENTICATION_ERROR",
		};

		this.error("3DS Authentication Failed", error, threeDSContext);
	}

	public logPaymentError(
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		error: any,
		paymentMethod: string,
		context?: Record<string, unknown>,
	): void {
		const paymentContext = {
			...context,
			paymentMethod,
			errorType: "PAYMENT_PROCESSING_ERROR",
		};

		this.error("Payment Processing Failed", error, paymentContext);
	}
}

export const errorLogger = new ErrorLogger();

