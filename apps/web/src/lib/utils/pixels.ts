import {
  TrackingPixels,
  FacebookPixel,
  TiktokPixel,
  KwaiPixel,
  MaybeInheritBool,
  MaybeInheritNumber
} from '@/types/pixels';
import { isNommi } from './brand';

function toBoolItem(v: MaybeInheritBool) {
  if (v === true || v === false) return v;
  if (v === 1 || v === '1') return true;
  if (v === 0 || v === '0') return false;
  return !!v;
}

function toNumberItem(v: MaybeInheritNumber) {
  const n = typeof v === 'string' ? Number(v) : v;
  return Number.isFinite(n as number) ? Number(n) : 0;
}

export function resolveFacebookConfig(root: TrackingPixels, item?: FacebookPixel) {
  if (isNommi) {
    return {
      fbPixPurchaseTrigger: toBoolItem(item?.fbPixPurchaseTrigger),
      fbPixConversionValue: toNumberItem(item?.fbPixConversionValue),
      fbBoletoPurchaseTrigger: toBoolItem(item?.fbBoletoPurchaseTrigger),
      fbBoletoConversionValue: toNumberItem(item?.fbBoletoConversionValue),
      fbPicpayPurchaseTrigger: toBoolItem(item?.fbPicpayPurchaseTrigger),
      fbPicpayConversionValue: toNumberItem(item?.fbPicpayConversionValue),
      fbNubankPurchaseTrigger: toBoolItem(item?.fbNubankPurchaseTrigger),
      fbNubankConversionValue: toNumberItem(item?.fbNubankConversionValue),
    };
  }

  return {
    fbPixPurchaseTrigger: root.fbPixPurchaseTrigger,
    fbPixConversionValue: root.fbPixConversionValue,
    fbBoletoPurchaseTrigger: root.fbBoletoPurchaseTrigger,
    fbBoletoConversionValue: root.fbBoletoConversionValue,
    fbPicpayPurchaseTrigger: root.fbPicpayPurchaseTrigger,
    fbPicpayConversionValue: root.fbPicpayConversionValue,
    fbNubankPurchaseTrigger: root.fbNubankPurchaseTrigger,
    fbNubankConversionValue: root.fbNubankConversionValue,
  };
}

export function resolveTiktokConfig(root: TrackingPixels, item?: TiktokPixel) {
  if (isNommi) {
    return {
      tiktokPixPurchaseTrigger: toBoolItem(item?.tiktokPixPurchaseTrigger),
      tiktokPixConversionValue: toNumberItem(item?.tiktokPixConversionValue),
      tiktokBoletoPurchaseTrigger: toBoolItem(item?.tiktokBoletoPurchaseTrigger),
      tiktokBoletoConversionValue: toNumberItem(item?.tiktokBoletoConversionValue),
      tiktokPicpayPurchaseTrigger: toBoolItem(item?.tiktokPicpayPurchaseTrigger),
      tiktokPicpayConversionValue: toNumberItem(item?.tiktokPicpayConversionValue),
      tiktokNubankPurchaseTrigger: toBoolItem(item?.tiktokNubankPurchaseTrigger),
      tiktokNubankConversionValue: toNumberItem(item?.tiktokNubankConversionValue),
    };
  }

  return {
    tiktokPixPurchaseTrigger: root.tiktokPixPurchaseTrigger,
    tiktokPixConversionValue: root.tiktokPixConversionValue,
    tiktokBoletoPurchaseTrigger: root.tiktokBoletoPurchaseTrigger,
    tiktokBoletoConversionValue: root.tiktokBoletoConversionValue,
    tiktokPicpayPurchaseTrigger: root.tiktokPicpayPurchaseTrigger,
    tiktokPicpayConversionValue: root.tiktokPicpayConversionValue,
    tiktokNubankPurchaseTrigger: root.tiktokNubankPurchaseTrigger,
    tiktokNubankConversionValue: root.tiktokNubankConversionValue,
  };
}

export function resolveKwaiConfig(root: TrackingPixels, item?: KwaiPixel) {
  if (isNommi) {
    return {
      kwaiPixPurchaseTrigger: toBoolItem(item?.kwaiPixPurchaseTrigger),
      kwaiPixConversionValue: toNumberItem(item?.kwaiPixConversionValue),
      kwaiBoletoPurchaseTrigger: toBoolItem(item?.kwaiBoletoPurchaseTrigger),
      kwaiBoletoConversionValue: toNumberItem(item?.kwaiBoletoConversionValue),
      kwaiPicpayPurchaseTrigger: toBoolItem(item?.kwaiPicpayPurchaseTrigger),
      kwaiPicpayConversionValue: toNumberItem(item?.kwaiPicpayConversionValue),
      kwaiNubankPurchaseTrigger: toBoolItem(item?.kwaiNubankPurchaseTrigger),
      kwaiNubankConversionValue: toNumberItem(item?.kwaiNubankConversionValue),
    };
  }

  return {
    kwaiPixPurchaseTrigger: root.kwaiPixPurchaseTrigger,
    kwaiPixConversionValue: root.kwaiPixConversionValue,
    kwaiBoletoPurchaseTrigger: root.kwaiBoletoPurchaseTrigger,
    kwaiBoletoConversionValue: root.kwaiBoletoConversionValue,
    kwaiPicpayPurchaseTrigger: root.kwaiPicpayPurchaseTrigger,
    kwaiPicpayConversionValue: root.kwaiPicpayConversionValue,
    kwaiNubankPurchaseTrigger: root.kwaiNubankPurchaseTrigger,
    kwaiNubankConversionValue: root.kwaiNubankConversionValue,
  };
}

