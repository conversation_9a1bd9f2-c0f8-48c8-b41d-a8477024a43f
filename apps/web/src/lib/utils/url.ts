import { isDevelopment } from '@/lib/env';

export const addQueryParamsToEndpoint = (endpoint: string, params: Record<string, string | undefined>): string => {
    const urlObj = new URL(endpoint, window.location.origin);

    Object.keys(params).forEach(key => {
      const value = params[key];
      if (value !== undefined) {
        urlObj.searchParams.append(key, value);
      }
    });


    return urlObj.pathname + urlObj.search;
  };

  export const getQueryParams = (url: string): Record<string, string> => {
    const params: Record<string, string> = {};
    const urlSearchParams = new URL(url).searchParams;
    urlSearchParams.forEach((value, key) => {
        params[key] = value;
    });
    return params;
  };

export const isDevelopmentEnvironment = (): boolean => {
  return (
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1' ||
    window.location.hostname.includes('localhost') ||
    isDevelopment() === true
  );
};

