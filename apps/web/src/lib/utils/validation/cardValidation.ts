import { isDevelopment } from '@/lib/env';

export const PAGARME_TEST_CARDS = {
  // Cartão principal para forçar 3DS challenge
  FORCE_3DS_CHALLENGE: '9000100811111111',

  // Cartões de sucesso
  SUCCESS_VISA: '****************',

  // Cartões específicos para testes
  PROCESSING: '****************',
  REJECTED: '****************',
} as const;

/**
 * Verifica se é um cartão de teste válido da Pagar.me
 */
export const isPagarmeTestCard = (cardNumber: string): boolean => {
  const cleanNumber = cardNumber.replace(/\D/g, '');
  return Object.values(PAGARME_TEST_CARDS).includes(cleanNumber as any);
};

/**
 * Validação básica para cartões na implementação 3DS
 */
export const validateCardFor3DS = (cardNumber: string): {
  isValid: boolean;
  isTestCard: boolean;
  error?: string;
} => {
  const cleanNumber = cardNumber.replace(/\D/g, '');

  if (cleanNumber.length < 13) {
    return {
      isValid: false,
      isTestCard: false,
      error: 'Número do cartão muito curto'
    };
  }

  const isTestCard = isPagarmeTestCard(cleanNumber);

  // Em ambiente de desenvolvimento, aceita cartões de teste
  const isDev = isDevelopment();

  if (isDev && isTestCard) {
    return {
      isValid: true,
      isTestCard: true
    };
  }

  // Validação básica de comprimento
  if (cleanNumber.length < 13 || cleanNumber.length > 19) {
    return {
      isValid: false,
      isTestCard,
      error: 'Número do cartão inválido'
    };
  }

  return {
    isValid: true,
    isTestCard
  };
};

/**
 * Obter cartão de teste recomendado para 3DS
 */
export const getRecommended3DSTestCard = (): string => {
  return PAGARME_TEST_CARDS.FORCE_3DS_CHALLENGE;
};

