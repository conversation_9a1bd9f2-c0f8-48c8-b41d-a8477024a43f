export const months = [
  { label: "Janeiro", value: "1" },
  { label: "Feve<PERSON>", value: "2" },
  { label: "Março", value: "3" },
  { label: "<PERSON><PERSON><PERSON>", value: "4" },
  { label: "<PERSON><PERSON>", value: "5" },
  { label: "<PERSON><PERSON>", value: "6" },
  { label: "<PERSON><PERSON>", value: "7" },
  { label: "Agosto", value: "8" },
  { label: "Setembro", value: "9" },
  { label: "Outubro", value: "10" },
  { label: "Novembro", value: "11" },
  { label: "Dezembro", value: "12" },
];

export const getNextYears = (count: number) => {
  return Array.from({ length: count }, (_, i) => {
    const year = (new Date().getFullYear() + i).toString();
    return { label: year, value: year };
  });
};

