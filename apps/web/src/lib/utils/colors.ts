/**
 * Adjusts the brightness of a color by a percentage.
 * For bright colors (>200): darkens the color
 * For medium colors (128-200): slightly darkens
 * For dark colors (<128): lightens the color
 * 
 * @param hex - Hex color string (with or without #)
 * @param percent - Percentage to adjust (positive number)
 * @returns Adjusted hex color with alpha channel
 */
export function adjustColor(hex: string, percent: number): string {
	hex = hex.replace(/^#/, "");
	const colorHex = hex.slice(0, 6);
	const alphaHex = hex.slice(6, 8) || "ff";

	let r = parseInt(colorHex.slice(0, 2), 16);
	let g = parseInt(colorHex.slice(2, 4), 16);
	let b = parseInt(colorHex.slice(4, 6), 16);

	// Calculate brightness using the luminance formula
	const brightness = (r * 299 + g * 587 + b * 114) / 1000;

	if (brightness > 200) {
		// Very bright colors: darken more
		r = Math.round(r * (1 - (percent + 5) / 100));
		g = Math.round(g * (1 - (percent + 5) / 100));
		b = Math.round(b * (1 - (percent + 5) / 100));
	} else if (brightness > 128) {
		// Medium brightness: darken normally
		r = Math.round(r * (1 - percent / 100));
		g = Math.round(g * (1 - percent / 100));
		b = Math.round(b * (1 - percent / 100));
	} else {
		// Dark colors: lighten
		r = Math.round(r + (255 - r) * ((percent * 5) / 100));
		g = Math.round(g + (255 - g) * ((percent * 5) / 100));
		b = Math.round(b + (255 - b) * ((percent * 5) / 100));
	}

	const rHex = r.toString(16).padStart(2, "0");
	const gHex = g.toString(16).padStart(2, "0");
	const bHex = b.toString(16).padStart(2, "0");

	return `#${rHex}${gHex}${bHex}${alphaHex}`;
}

export function getOppositeColor(hex: string): string {
	hex = hex.replace(/^#/, "");

	let r = parseInt(hex.slice(0, 2), 16);
	let g = parseInt(hex.slice(2, 4), 16);
	let b = parseInt(hex.slice(4, 6), 16);

	r = 255 - r;
	g = 255 - g;
	b = 255 - b;

	const rHex = r.toString(16).padStart(2, "0");
	const gHex = g.toString(16).padStart(2, "0");
	const bHex = b.toString(16).padStart(2, "0");

	return `#${rHex}${gHex}${bHex}`;
}

export function applyTransparency(hex: string, alpha: number): string {
	hex = hex.replace(/^#/, "");

	const r = parseInt(hex.slice(0, 2), 16);
	const g = parseInt(hex.slice(2, 4), 16);
	const b = parseInt(hex.slice(4, 6), 16);

	const clampedAlpha = Math.max(0, Math.min(1, alpha));

	return `rgba(${r}, ${g}, ${b}, ${clampedAlpha})`;
}

// ==================== BRAND COLOR UTILITIES ====================

import { BRANDING } from "./brand";

/**
 * Retorna as cores da marca atual (Cakto ou Nommi)
 */
export const getBrandColors = () => {
	if (BRANDING === "nommi") {
		return {
			primary: "#2886B9",
			primaryHover: "#1f6e94",
			primaryLight: "#f0f9ff",
			shadow: "0px 8px 16px 0px rgba(40, 134, 185, 0.24)",
		};
	}

	// Default: Cakto
	return {
		primary: "#0F7864",
		primaryHover: "#0b6856",
		primaryLight: "#f0fdf9",
		primaryLighter: "#ecfdf5",
		shadow: "0px 8px 16px 0px rgba(15, 120, 101, 0.24)",
	};
};

/**
 * Classe CSS para cor primária da marca
 */
export const brandPrimaryClass = () => {
	return BRANDING === "nommi"
		? "bg-[#2886B9] hover:bg-[#1f6e94]"
		: "bg-brand-primary hover:bg-brand-primary-hover";
};

/**
 * Retorna style object para cores da marca
 */
export const getBrandStyles = () => {
	const colors = getBrandColors();
	return {
		backgroundColor: colors.primary,
		"--hover-bg": colors.primaryHover,
	} as React.CSSProperties;
};
