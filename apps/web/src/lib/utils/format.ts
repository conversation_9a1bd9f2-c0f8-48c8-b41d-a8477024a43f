export const formatPrice = (price: number) =>
  Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(price);

/**
 * Converts a currency amount in reais to centavos (integer format)
 * @param amount - Amount in reais (ex: 19.90)
 * @returns Amount in centavos as integer (ex: 1990)
 */
export const convertToCentavos = (amount: number): number => {
 
  return Math.round(amount * 100);
};

export const formatTime = (secs: number) => {
  const sec_num = secs;
  const hours = Math.floor(sec_num / 3600);
  const minutes = Math.floor(sec_num / 60) % 60;
  const seconds = sec_num % 60;

  return [hours, minutes, seconds]
    .map((v) => (v < 10 ? "0" + v : v))
    .filter((v, i) => v !== "00" || i > 0)
    .join(":");
};

export function formatUrl(
  url: string,
  parameters: Record<string, string>
): string {
  const urlObj = new URL(url);

  for (const key in parameters) {
    urlObj.searchParams.set(key, parameters[key]);
  }

  const urlWithParameters = urlObj.toString();

  return urlWithParameters;
}

export function formatPhoneNumber(phoneNumber: string): string {
  return phoneNumber.startsWith("+55") ? phoneNumber : `+55${phoneNumber}`;
}

