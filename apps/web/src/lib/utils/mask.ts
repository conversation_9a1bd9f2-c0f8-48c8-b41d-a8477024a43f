/**
 * Apply mask to value
 * @param value - Value to be masked
 * @param mask - Mask pattern (e.g. "99/99", "999.999.999-99")
 * @param maskChar - Character to use for unfilled positions (default: "_")
 */
export function applyMask(value: string, mask: string, maskChar: string = "_"): string {
	if (!mask) return value;

	const cleanValue = value.replace(/\D/g, "");
	let result = "";
	let valueIndex = 0;

	for (let i = 0; i < mask.length && valueIndex < cleanValue.length; i++) {
		const maskChar = mask[i];
		if (maskChar === "9") {
			result += cleanValue[valueIndex];
			valueIndex++;
		} else {
			result += maskChar;
		}
	}

	return result;
}

/**
 * Remove mask from value
 */
export function removeMask(value: string): string {
	return value.replace(/\D/g, "");
}

