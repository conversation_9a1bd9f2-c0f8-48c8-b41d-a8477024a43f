export type InAppBrowserType = "tiktok" | "instagram" | "facebook" | null;

export function getInAppBrowser(): InAppBrowserType {
	if (typeof window === "undefined") return null;

	const ua = navigator.userAgent || "";

	if (/TikTok/i.test(ua)) return "tiktok";
	if (/Instagram/i.test(ua)) return "instagram";
	if (/FBAN|FBAV/i.test(ua)) return "facebook";

	return null;
}

export function isInAppBrowser(): boolean {
	return getInAppBrowser() !== null;
}

export function shouldShowOpenInBrowserWarning(): boolean {
	const browser = getInAppBrowser();
	// TikTok and Instagram have more problems with payments
	return browser === "tiktok" || browser === "instagram";
}

export function getOpenInBrowserUrl(currentUrl: string): string {
	const browser = getInAppBrowser();

	if (browser === "tiktok") {
		// TikTok: use Android intent or fallback
		return currentUrl;
	}

	if (browser === "instagram") {
		// Instagram: use Android intent or fallback
		return currentUrl;
	}

	return currentUrl;
}

