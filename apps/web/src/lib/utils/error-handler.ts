"use client";

import { errorLogger } from "./error-logger";

export interface UserFriendlyError {
	title: string;
	message: string;
	action?: string;
	canRetry?: boolean;
}

export enum ErrorType {
	NETWORK_ERROR = "NETWORK_ERROR",
	VALIDATION_ERROR = "VALIDATION_ERROR",
	PAYMENT_ERROR = "PAYMENT_ERROR",
	AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR",
	SERVER_ERROR = "SERVER_ERROR",
	UNKNOWN_ERROR = "UNKNOWN_ERROR",
	THREEDS_ERROR = "THREEDS_ERROR",
}

class ErrorHandler {
	private userFriendlyMessages: Record<ErrorType, UserFriendlyError> = {
		[ErrorType.NETWORK_ERROR]: {
			title: "Problema de conexão",
			message:
				"Não foi possível completar a operação. Tente novamente ou entre em contato com o suporte.",
			action: "Tentar novamente",
			canRetry: true,
		},
		[ErrorType.VALIDATION_ERROR]: {
			title: "Dados inválidos",
			message:
				"Por favor, verifique as informações inseridas e tente novamente.",
			action: "Corrigir dados",
			canRetry: true,
		},
		[ErrorType.PAYMENT_ERROR]: {
			title: "Pagamento recusado",
			message:
				"Não foi possível processar seu pagamento. Verifique os dados do cartão ou tente outro método de pagamento.",
			action: "Tentar novamente",
			canRetry: true,
		},
		[ErrorType.AUTHENTICATION_ERROR]: {
			title: "Erro de autenticação",
			message:
				"Houve um problema na verificação de segurança. Tente novamente.",
			action: "Tentar novamente",
			canRetry: true,
		},
		[ErrorType.SERVER_ERROR]: {
			title: "Erro interno",
			message:
				"Estamos enfrentando problemas técnicos. Nossa equipe foi notificada e está trabalhando para resolver.",
			action: "Tentar mais tarde",
			canRetry: false,
		},
		[ErrorType.UNKNOWN_ERROR]: {
			title: "Erro inesperado",
			message:
				"Algo deu errado. Por favor, tente novamente ou entre em contato com o suporte.",
			action: "Tentar novamente",
			canRetry: true,
		},
		[ErrorType.THREEDS_ERROR]: {
			title: "Erro na verificação de segurança",
			message:
				"Não foi possível completar a verificação de segurança do cartão. Tente novamente.",
			action: "Tentar novamente",
			canRetry: true,
		},
	};

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	private determineErrorType(error: any): ErrorType {
		// Check if it's a fetch error (our custom format from checkout-client.ts)
		if (error?.response) {
			const status = error.response.status;

			if (status >= 500) {
				return ErrorType.SERVER_ERROR;
			}

			if (status === 400) {
				return ErrorType.VALIDATION_ERROR;
			}

			if (status === 401 || status === 403) {
				return ErrorType.AUTHENTICATION_ERROR;
			}

			if (status === 402 || status === 422) {
				return ErrorType.PAYMENT_ERROR;
			}
		}

		if (
			error?.message?.includes("3DS") ||
			error?.message?.includes("3ds")
		) {
			return ErrorType.THREEDS_ERROR;
		}

		if (
			error?.message?.includes("network") ||
			error?.message?.includes("fetch") ||
			error?.message?.includes("Failed to fetch")
		) {
			return ErrorType.NETWORK_ERROR;
		}

		if (
			error?.message?.includes("payment") ||
			error?.message?.includes("card")
		) {
			return ErrorType.PAYMENT_ERROR;
		}

		return ErrorType.UNKNOWN_ERROR;
	}

	public async handleError(
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		error: any,
		context?: Record<string, unknown>,
		customMessage?: Partial<UserFriendlyError>,
	): Promise<UserFriendlyError> {
		const errorType = this.determineErrorType(error);

		errorLogger.error("Error handled by ErrorHandler", error, {
			...context,
			errorType,
			originalMessage: error?.message,
			stack: error?.stack,
		});

		const baseMessage = this.userFriendlyMessages[errorType];

		return {
			...baseMessage,
			...customMessage,
		};
	}

	public handleAxiosError(
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		error: any,
		context?: Record<string, unknown>,
		customMessage?: Partial<UserFriendlyError>,
	): UserFriendlyError {
		errorLogger.logAxiosError(error, context);

		const errorType = this.determineErrorType(error);
		const baseMessage = this.userFriendlyMessages[errorType];

		return {
			...baseMessage,
			...customMessage,
		};
	}

	public async handle3DSError(
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		error: any,
		provider: string,
		context?: Record<string, unknown>,
		customMessage?: Partial<UserFriendlyError>,
	): Promise<UserFriendlyError> {
		errorLogger.log3DSError(error, provider, context);

		const baseMessage = this.userFriendlyMessages[ErrorType.THREEDS_ERROR];

		return {
			...baseMessage,
			...customMessage,
		};
	}

	public async handlePaymentError(
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		error: any,
		paymentMethod: string,
		context?: Record<string, unknown>,
		customMessage?: Partial<UserFriendlyError>,
	): Promise<UserFriendlyError> {
		errorLogger.logPaymentError(error, paymentMethod, context);

		const baseMessage = this.userFriendlyMessages[ErrorType.PAYMENT_ERROR];

		return {
			...baseMessage,
			...customMessage,
		};
	}

	public handleValidationError(
		fieldErrors: Record<string, string[]>,
		context?: Record<string, unknown>,
	): { fieldErrors: Record<string, string>; generalError?: UserFriendlyError } {
		errorLogger.warning("Validation errors occurred", fieldErrors, context);

		const processedFieldErrors: Record<string, string> = {};

		for (const [field, messages] of Object.entries(fieldErrors)) {
			processedFieldErrors[field] = this.sanitizeValidationMessage(messages);
		}

		return {
			fieldErrors: processedFieldErrors,
			generalError: this.userFriendlyMessages[ErrorType.VALIDATION_ERROR],
		};
	}

	private sanitizeValidationMessage(messages: string | string[]): string {
		const messageArray = Array.isArray(messages) ? messages : [messages];

		const friendlyMessages = messageArray.map((msg) => {
			if (msg.includes("required")) {
				return "Este campo é obrigatório";
			}
			if (msg.includes("invalid")) {
				return "Formato inválido";
			}
			if (msg.includes("email")) {
				return "Email inválido";
			}
			if (msg.includes("phone")) {
				return "Telefone inválido";
			}
			if (msg.includes("cpf") || msg.includes("document")) {
				return "CPF inválido";
			}
			if (msg.includes("card")) {
				return "Dados do cartão inválidos";
			}
			if (msg.includes("cvv")) {
				return "CVV inválido";
			}
			if (msg.includes("expir")) {
				return "Data de validade inválida";
			}

			return "Dados inválidos";
		});

		return friendlyMessages.join(", ");
	}

	public createGlobalErrorHandler(): void {
		if (typeof window === "undefined") return;

		window.addEventListener("error", (event) => {
			errorLogger.critical("Uncaught JavaScript Error", event.error, {
				filename: event.filename,
				lineno: event.lineno,
				colno: event.colno,
				message: event.message,
			});
		});

		window.addEventListener("unhandledrejection", (event) => {
			errorLogger.critical("Unhandled Promise Rejection", event.reason, {
				type: "unhandledrejection",
			});
		});
	}
}

export const errorHandler = new ErrorHandler();

