import { DefaultCreditCardType, PaymentMethods, RecurrencePeriodMap } from '@/constants/payment';
import { PaymentMethod } from '@/types';
import creditCardType from 'credit-card-type';
import { CreditCardType } from 'credit-card-type/dist/types';

export const getCreditCardType = (cardNumber: string): CreditCardType => {
  if (!cardNumber) {
    return DefaultCreditCardType;
  }

  const cleanedCardNumber = cardNumber.replace(/\s/g, '');

  const cardTypes = creditCardType(cleanedCardNumber);

  if (!cardTypes.length) {
    return DefaultCreditCardType;
  }

  const [type] = cardTypes;

  return type;
};

export const getCreditCardNumberMask = (cardType: CreditCardType): string => {
  if (!cardType || !cardType.gaps || !cardType.gaps.length) {
    return '9999 9999 9999 9999';
  }

  let mask = '';

  const gaps = cardType.gaps;

  const lengths = cardType.lengths;

  for (let i = 0; i < lengths[lengths.length - 1]; i++) {
    if (gaps.includes(i)) {
      mask += ' ';
    }
    mask += '9';
  }

  return mask;
};

export const getCreditCardCvvMask = (cardType: CreditCardType): string => {
  if (!cardType || !cardType.code || !cardType.code.size) {
    return '999';
  }

  return '9'.repeat(cardType.code.size);
};

export const getRecurrencePeriodLabel = (recurrencePeriod: number) => {
  if (recurrencePeriod in RecurrencePeriodMap) {
    return RecurrencePeriodMap[recurrencePeriod].label;
  }

  return RecurrencePeriodMap[30].label;
};

export const getRecurrencePeriodDescription = (recurrencePeriod: number) => {
  if (recurrencePeriod in RecurrencePeriodMap) {
    return RecurrencePeriodMap[recurrencePeriod].description;
  }

  return RecurrencePeriodMap[30].description;
};

export const canPaymentMethodBePaidInInstallments = (paymentMethod: PaymentMethod) => {
  const paymentMethodConfig = PaymentMethods.find((method) => method.type === paymentMethod);

  if (!paymentMethodConfig) {
    return false;
  }

  return paymentMethodConfig.installments.enabled;
};

/**
 * Calcula o valor para eventos de pixel de forma consistente
 * @param orderAmount - Valor do pedido (pode estar em reais ou centavos)
 * @param conversionValue - Valor de conversão configurado (opcional)
 * @returns Valor calculado para o pixel
 */
export const calculatePixelValue = (orderAmount: number, conversionValue?: number): number => {
  if (!conversionValue || conversionValue <= 0) {
    return Number(orderAmount);
  }

  if (orderAmount > 1000) {
    return Number(orderAmount) / 100;
  }

  return Number(orderAmount);
};

/**
 * Calcula o valor para eventos de pixel com conversão opcional
 * @param orderAmount - Valor do pedido
 * @param conversionValue - Valor de conversão configurado
 * @param useConversion - Se deve usar o conversionValue como multiplicador
 * @returns Valor calculado para o pixel
 */
export const calculatePixelValueWithConversion = (
  orderAmount: number,
  conversionValue?: number,
  useConversion: boolean = false
): number => {
  const baseValue = calculatePixelValue(orderAmount, conversionValue);

  if (!useConversion || !conversionValue || conversionValue <= 0) {
    return baseValue;
  }

  return baseValue * Number(conversionValue);
};

