"use client";

import { useCheckout } from "@/contexts";
import type { BumpData, InstallmentsData } from "@/types";
import { useCouponDiscount } from "./useCouponDiscount";
import { useServiceFee } from "./useServiceFee";

type UsePriceProps = {
	bumps?: BumpData[];
	installments?: number;
	calculatedInstallments?: InstallmentsData[];
};

export const usePrice = (props: UsePriceProps = {}) => {
	const { bumps = [], installments = 1, calculatedInstallments = [] } = props;
	const { offer, canBePaidInInstallments, hasAnySubscription } = useCheckout();
	const { getDiscount } = useCouponDiscount();
	const { serviceFeeValue, hasServiceFee } = useServiceFee();

	const getBumpPrice = (bump: BumpData) => {
		if (!bump || !bump.checked || !bump.offer) {
			return 0;
		}
		return Number(bump.offer.price);
	};

	const getBumpsPrice = () =>
		bumps.reduce((sum, bump) => {
			return sum + getBumpPrice(bump);
		}, 0);

	const getBumpPriceWithDiscount = (bump: BumpData) => {
		return getBumpPrice(bump) - getDiscount(getBumpPrice(bump), true);
	};

	const getBumpsPriceWithDiscount = () => {
		return bumps.reduce((sum, bump) => {
			return sum + getBumpPriceWithDiscount(bump);
		}, 0);
	};

	const getMainOfferPrice = () => {
		if (offer?.paid) {
			return 0;
		}
		return offer?.price || 0;
	};

	const mainOfferPrice = getMainOfferPrice();
	const mainOfferDiscount = getDiscount(mainOfferPrice);
	const mainOfferPriceWithDiscount = mainOfferPrice - mainOfferDiscount;

	const getMainOfferPriceWithInstallmentsFees = () => {
		if (!canBePaidInInstallments || !calculatedInstallments.length) {
			return mainOfferPriceWithDiscount;
		}

		const installmentOption = calculatedInstallments.find(
			(option) => Number(option.installment) === Number(installments)
		);

		if (!installmentOption) {
			return mainOfferPriceWithDiscount;
		}

		return Number(installmentOption.value) * Number(installmentOption.installment);
	};

	const getBumpPriceWithInstallmentsFees = (bump: BumpData) => {
		if (!bump.checked) {
			return 0;
		}

		const bumpPriceWithDiscount = getBumpPriceWithDiscount(bump);

		if (!canBePaidInInstallments || !bump.calculatedInstallments?.length) {
			return Number(bumpPriceWithDiscount);
		}

		const installmentOption = bump.calculatedInstallments.find(
			(option) => Number(option.installment) === Number(bump.installments)
		);

		if (!installmentOption) {
			return Number(bumpPriceWithDiscount);
		}

		return Number(installmentOption.value) * Number(installmentOption.installment);
	};

	const getBumpsPriceWithInstallmentsFees = () => {
		return bumps.reduce((sum, bump) => {
			return sum + getBumpPriceWithInstallmentsFees(bump);
		}, 0);
	};

	// calculate base prices
	const bumpsPrice = getBumpsPrice();

	const totalPrice = mainOfferPrice + bumpsPrice;

	// calculate discounts
	const bumpsPriceWithDiscount = getBumpsPriceWithDiscount();
	const bumpsDiscount = bumpsPrice - bumpsPriceWithDiscount;
	const totalDiscount = mainOfferDiscount + bumpsDiscount;

	// calculate final prices
	const totalPriceWithDiscount = mainOfferPriceWithDiscount + bumpsPriceWithDiscount;
	const bumpsFinalPriceWithFees = getBumpsPriceWithInstallmentsFees();
	const mainOfferFinalPriceWithFees = getMainOfferPriceWithInstallmentsFees();

	// calculate service fee (não se aplica desconto à taxa de serviço)
	const totalPriceWithServiceFee = totalPriceWithDiscount + (hasServiceFee ? serviceFeeValue : 0);

	// it will be removed when the api process all products in a single payment
	const getFinalPrice = () => {
		if (!canBePaidInInstallments || hasAnySubscription) {
			return mainOfferFinalPriceWithFees + bumpsFinalPriceWithFees + (hasServiceFee ? serviceFeeValue : 0);
		}
		return mainOfferFinalPriceWithFees + (hasServiceFee ? serviceFeeValue : 0);
	};

	return {
		totalPrice,
		totalPriceWithDiscount,
		totalPriceWithServiceFee,
		mainOfferPrice,
		bumpsPrice,
		mainOfferDiscount,
		bumpsDiscount,
		totalDiscount,
		mainOfferPriceWithDiscount,
		bumpsPriceWithDiscount,
		mainOfferFinalPriceWithFees,
		bumpsFinalPriceWithFees,
		serviceFeeValue,
		hasServiceFee,
		finalPrice: getFinalPrice()
	};
};

