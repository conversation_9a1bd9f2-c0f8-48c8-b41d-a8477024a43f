"use client";

import { useCheckout } from "@/contexts";
import type { TrackingPixels } from "@/types/pixels";

const useTrackingPixels = () => {
	const { offer } = useCheckout();

	return (
		(offer?.tracking_pixels as TrackingPixels) || {
			facebook_pixels: [],
			google_ads_pixels: [],
			fbBoletoConversionValue: 0,
			fbBoletoPurchaseTrigger: false,
			fbPixConversionValue: 0,
			fbPixPurchaseTrigger: false,
			fbPicpayConversionValue: 0,
			fbPicpayPurchaseTrigger: false,
			fbNubankConversionValue: 0,
			fbNubankPurchaseTrigger: false,
			googleAnalyticsTrackingId: null,
			kwaiBoletoConversionValue: 0,
			kwaiBoletoPurchaseTrigger: false,
			kwaiPixConversionValue: 0,
			kwaiPixPurchaseTrigger: false,
			kwaiPicpayConversionValue: 0,
			kwaiPicpayPurchaseTrigger: false,
			kwaiNubankConversionValue: 0,
			kwaiNubankPurchaseTrigger: false,
			pixelEventId: "",
			pixel_domains: [],
			taboola_pixels: [],
			kwai_pixels: [],
			outbrain_pixels: [],
			tiktok_pixels: [],
			tiktokBoletoConversionValue: 0,
			tiktokBoletoPurchaseTrigger: false,
			tiktokPixConversionValue: 0,
			tiktokPixPurchaseTrigger: false,
			tiktokPicpayConversionValue: 0,
			tiktokPicpayPurchaseTrigger: false,
			tiktokNubankConversionValue: 0,
			tiktokNubankPurchaseTrigger: false,
		} as TrackingPixels
	);
};

export { useTrackingPixels };

