"use client";

import type { Payment, ProductData } from "@/types";
import type { TrackingPixels } from "@/types/pixels";

declare global {
	interface Window {
		gtag?: (...args: unknown[]) => void;
	}
}

export const useGoogleAds = (offer: ProductData | undefined) => {

	const pixels =
		(offer?.tracking_pixels?.google_ads_pixels as TrackingPixels['google_ads_pixels']) || [];

	const getPaymentValue = (payment: Payment): number => {
		const n = Number(payment?.amount);
		if (Number.isFinite(n)) return n;
		const orders = Array.isArray(payment?.orders) ? payment.orders : [];
		return orders.reduce((acc: number, o) => acc + Number(o?.amount || 0), 0);
	};

	const onTicketGenerated = (payment: Payment): void => {
		if (typeof window === 'undefined' || !window.gtag) return;

		const value: number = getPaymentValue(payment);
		pixels.forEach((pixel) => {
			if (!pixel.boletoTrigger) return;
			window.gtag!('event', 'conversion', {
				send_to: `${pixel.pixelId}/${pixel.conversionLabel}`,
				value,
				currency: 'BRL',
				transaction_id: payment?.id,
				event_category: 'Boleto',
				event_action: 'Gerado',
				event_label: 'Boleto Gerado',
			});
		});
	};

	const onPixGenerated = (payment: Payment): void => {
		if (typeof window === 'undefined' || !window.gtag) return;

		const value: number = getPaymentValue(payment);
		pixels.forEach((pixel) => {
			if (!pixel.cardPixApprovalTrigger) return;
			window.gtag!('event', 'conversion', {
				send_to: `${pixel.pixelId}/${pixel.conversionLabel}`,
				value,
				currency: 'BRL',
				transaction_id: payment?.id,
				event_category: 'PIX',
				event_action: 'Gerado',
				event_label: 'PIX Gerado',
			});
		});
	};

	const onCheckoutVisit = (): void => {
		if (typeof window === 'undefined' || !window.gtag) return;

		pixels.forEach((pixel) => {
			if (!pixel.checkoutVisitTrigger) return;
			window.gtag!('event', 'conversion', {
				send_to: `${pixel.pixelId}/${pixel.conversionLabel}`,
				event_category: 'Checkout',
				event_action: 'Visitado',
				event_label: 'Visita ao Checkout',
			});
		});
	};

	const onPaymentApproved = (payment: Payment, amount: number): void => {
		if (typeof window === 'undefined' || !window.gtag) return;

		const value: number = Number.isFinite(amount) ? Number(amount) : getPaymentValue(payment);
		pixels.forEach((pixel) => {
			if (!pixel.cardPixApprovalTrigger) return;
			window.gtag!('event', 'conversion', {
				send_to: `${pixel.pixelId}/${pixel.conversionLabel}`,
				value,
				currency: 'BRL',
				transaction_id: payment?.id,
				event_category: 'Pagamento',
				event_action: 'Aprovado',
				event_label: 'Pagamento Aprovado',
			});
		});
	};

	return {
		onTicketGenerated,
		onPixGenerated,
		onCheckoutVisit,
		onPaymentApproved,
	};
};

