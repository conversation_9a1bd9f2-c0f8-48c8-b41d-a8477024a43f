"use client";

import { useEffect, useState } from 'react';

declare global {
	interface Window {
		MP_DEVICE_SESSION_ID?: string;
	}
}

export const useMercadoPagoDeviceId = () => {
	const [deviceId, setDeviceId] = useState<string | null>(null);

	useEffect(() => {
		if (typeof window === 'undefined') return;

		if (document.querySelector('script[src*="mercadopago.com/v2/security.js"]')) {
			if (window.MP_DEVICE_SESSION_ID) {
				setDeviceId(window.MP_DEVICE_SESSION_ID);
			}
			return;
		}

		const script = document.createElement('script');
		script.src = 'https://www.mercadopago.com/v2/security.js';
		script.setAttribute('view', 'checkout');
		script.async = true;

		script.onload = () => {
			const checkId = () => {
				if (window.MP_DEVICE_SESSION_ID) {
					setDeviceId(window.MP_DEVICE_SESSION_ID);
				} else {
					setTimeout(checkId, 100);
				}
			};
			checkId();
		};

		document.body.appendChild(script);

		return () => {
			if (document.body.contains(script)) {
				document.body.removeChild(script);
			}
		};
	}, []);

	return deviceId;
};

