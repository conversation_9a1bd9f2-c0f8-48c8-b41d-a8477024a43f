"use client";

import { useState, useEffect, useMemo } from 'react';
import { PaidInstallmentOption } from '@/constants/payment';
import { getInstallments } from '@/lib/api/checkout-client';
import type { InstallmentsData, OfferType } from '@/types';
import { formatPrice } from '@/lib/utils/format';
import { getRecurrencePeriodLabel } from '@/lib/utils/payment';

type CalculatedInstallmentOption = InstallmentsData & {
	label: string;
	total: number;
};

export type UseFetchCalculatedInstallmentsProps = {
	offerId: string;
	offerPaid: boolean;
	offerType: OfferType;
	price: number;
	recurrencePeriod?: number;
	onCalculate?: (calculatedInstallments: CalculatedInstallmentOption[]) => void;
};

export const useFetchCalculatedInstallments = ({
	offerId,
	offerPaid = false,
	offerType,
	price,
	recurrencePeriod,
	onCalculate = () => {}
}: UseFetchCalculatedInstallmentsProps) => {
	const [calculatedInstallments, setCalculatedInstallments] = useState<CalculatedInstallmentOption[]>([]);
	const [isLoadingInstallments, setIsLoadingInstallments] = useState(false);

	const getSubscriptionInstallmentLabel = (installment: string, value: number) => {
		return `${installment}x de ${formatPrice(value)} / ${getRecurrencePeriodLabel(recurrencePeriod || 30)}`;
	};

	const getRegularInstallmentLabel = (installment: string, value: number) => {
		return `${installment}x de ${formatPrice(value)}`;
	};

	const getInstallmentLabel = (installment: string, value: number) => {
		return offerType === 'subscription'
			? getSubscriptionInstallmentLabel(installment, value)
			: getRegularInstallmentLabel(installment, value);
	};

	useEffect(() => {
		if (offerPaid || price === 0 || !offerId) {
			setCalculatedInstallments([]);
			return;
		}

		if (offerPaid) {
			setCalculatedInstallments([PaidInstallmentOption]);
			return;
		}

		const fetchInstallments = async () => {
			setIsLoadingInstallments(true);
			try {
				const data = await getInstallments(offerId, price);
				const processed = data
					.sort((a, b) => Number(a.installment) - Number(b.installment))
					.map((installment) => ({
						...installment,
						label: getInstallmentLabel(installment.installment, installment.value),
						total: Number(installment.value) * Number(installment.installment)
					}));
				setCalculatedInstallments(processed);
			} catch (error) {
				console.error('Error fetching installments:', error);
				setCalculatedInstallments([]);
			} finally {
				setIsLoadingInstallments(false);
			}
		};

		if (price > 0 && !offerPaid) {
			fetchInstallments();
		}
	}, [offerId, offerPaid, offerType, price, recurrencePeriod]);

	useEffect(() => {
		onCalculate?.(calculatedInstallments);
	}, [calculatedInstallments, onCalculate]);

	const getInstallmentOption = (installment: string) => {
		return calculatedInstallments.find((option) => String(option.value) === installment);
	};

	const maxInstallmentOption = useMemo(() => {
		if (!calculatedInstallments.length) {
			return null;
		}
		return calculatedInstallments[calculatedInstallments.length - 1];
	}, [calculatedInstallments]);

	return {
		calculatedInstallments,
		getInstallmentOption,
		maxInstallmentOption,
		isLoadingInstallments
	};
};

