"use client";

import type { Payment, ProductData } from "@/types";
import type { TrackingPixels, KwaiPixel as KwaiItem } from "@/types/pixels";
import { calculatePixelValue } from "@/lib/utils/payment";
import { resolveKwaiConfig } from "@/lib/utils/pixels";

declare global {
	interface Window {
		kwaiq?: {
			instance: (pixelId: string) => {
				track: (event: string, data: unknown, options?: unknown) => void;
			};
		};
	}
}

export default function useKwaiPixels(offer: ProductData | undefined, activeIndex: number = 0) {
	const trackingPixels = (offer?.tracking_pixels as TrackingPixels) || ({} as TrackingPixels);

	const kwaiq = typeof window !== 'undefined' ? (window as any)?.kwaiq : undefined;

	const kwaiItem: KwaiItem | undefined =
		Array.isArray(trackingPixels.kwai_pixels) && trackingPixels.kwai_pixels.length > 0
			? trackingPixels.kwai_pixels[Math.min(activeIndex, trackingPixels.kwai_pixels.length - 1)]
			: undefined;

	const {
		kwaiBoletoPurchaseTrigger,
		kwaiPixPurchaseTrigger,
		kwaiPicpayPurchaseTrigger,
		kwaiNubankPurchaseTrigger,
		kwaiBoletoConversionValue,
		kwaiPixConversionValue,
		kwaiPicpayConversionValue,
		kwaiNubankConversionValue,
	} = resolveKwaiConfig(trackingPixels, kwaiItem);

	const getPaymentPayload = (payment: Payment) => {
		const disabled_orderbumps = offer?.disable_orderbump_pixel_events || false;
		let orders = Array.isArray(payment?.orders) ? payment.orders : [];
		if (disabled_orderbumps) orders = orders.filter((o) => o?.offer_type !== 'orderbump');
		const contentIds = orders.map((o) => o?.offerId).filter(Boolean);
		const n = Number(payment?.amount);
		const value = !disabled_orderbumps
			? n
			: orders.reduce((acc, o) => acc + Number(o?.amount || 0), 0);
		return { contentIds, value };
	};

	const onCheckoutVisit = (): void => {
		if (!kwaiq || !Array.isArray(trackingPixels.kwai_pixels) || !trackingPixels.kwai_pixels.length) return;

		trackingPixels.kwai_pixels.forEach((pixel) => {
			kwaiq.instance(pixel.pixelId).track(
				'initiatedCheckout',
				{
					content_ids: [offer?.id],
					value: Number(offer?.price),
					currency: 'BRL',
				},
			);
		});
	};

	const onTicketGenerated = (payment: Payment): void => {
		const { contentIds, value } = getPaymentPayload(payment);
		if (!kwaiq || !contentIds.length) return;

		if (kwaiBoletoPurchaseTrigger) {
			onPaymentApproved(payment, calculatePixelValue(value, kwaiBoletoConversionValue));
			return;
		}

		(trackingPixels.kwai_pixels || []).forEach((pixel) => {
			kwaiq.instance(pixel.pixelId).track(
				'addPaymentInfo',
				{
					content_ids: contentIds,
					value: Number(value),
					currency: 'BRL',
					event_type: 'boleto_gerado',
				},
				{
					eventID: payment?.id,
				},
			);
		});
	};

	const onPixGenerated = (payment: Payment): void => {
		const { contentIds, value } = getPaymentPayload(payment);
		if (!kwaiq || !contentIds.length) return;

		if (kwaiPixPurchaseTrigger) {
			onPaymentApproved(payment, calculatePixelValue(value, kwaiPixConversionValue));
			return;
		}

		(trackingPixels.kwai_pixels || []).forEach((pixel) => {
			kwaiq.instance(pixel.pixelId).track(
				'addPaymentInfo',
				{
					content_ids: contentIds,
					value: Number(value),
					currency: 'BRL',
					event_type: 'pix_gerado',
				},
				{
					eventID: payment?.id,
				},
			);
		});
	};

	const onPicpayGenerated = (payment: Payment): void => {
		const { contentIds, value } = getPaymentPayload(payment);
		if (!kwaiq || !contentIds.length) return;

		if (kwaiPicpayPurchaseTrigger) {
			onPaymentApproved(payment, calculatePixelValue(value, kwaiPicpayConversionValue));
			return;
		}

		(trackingPixels.kwai_pixels || []).forEach((pixel) => {
			kwaiq.instance(pixel.pixelId).track(
				'addPaymentInfo',
				{
					content_ids: contentIds,
					value: Number(value),
					currency: 'BRL',
					event_type: 'picpay_gerado',
				},
				{
					eventID: payment?.id,
				},
			);
		});
	};

	const onNubankGenerated = (payment: Payment): void => {
		const { contentIds, value } = getPaymentPayload(payment);
		if (!kwaiq || !contentIds.length) return;

		if (kwaiNubankPurchaseTrigger) {
			onPaymentApproved(payment, calculatePixelValue(value, kwaiNubankConversionValue));
			return;
		}

		(trackingPixels.kwai_pixels || []).forEach((pixel) => {
			kwaiq.instance(pixel.pixelId).track(
				'addPaymentInfo',
				{
					content_ids: contentIds,
					value: Number(value),
					currency: 'BRL',
					event_type: 'openfinance_nubank_gerado',
				},
				{
					eventID: payment?.id,
				},
			);
		});
	};

	const onPaymentApproved = (payment: Payment, amount: number): void => {
		const purchaseAlreadyTracked =
			(payment?.paymentMethod === 'boleto' && kwaiBoletoPurchaseTrigger) ||
			((payment?.paymentMethod === 'pix' || payment?.paymentMethod === 'pix_auto') && kwaiPixPurchaseTrigger) ||
			(payment?.paymentMethod === 'picpay' && kwaiPicpayPurchaseTrigger) ||
			(payment?.paymentMethod === 'openfinance_nubank' && kwaiNubankPurchaseTrigger);

		const disabled_orderbumps = offer?.disable_orderbump_pixel_events || false;
		if (purchaseAlreadyTracked && payment?.status === 'paid') return;

		const { contentIds, value } = getPaymentPayload(payment);
		if (!kwaiq || !contentIds.length) return;

		(trackingPixels.kwai_pixels || []).forEach((pixel) => {
			kwaiq.instance(pixel.pixelId).track(
				'purchase',
				{
					content_ids: contentIds,
					value: !disabled_orderbumps ? Number(amount) : Number(value),
					currency: 'BRL',
				},
				{
					eventID: payment?.id,
				},
			);
		});
	};

	return {
		onCheckoutVisit,
		onTicketGenerated,
		onPixGenerated,
		onPicpayGenerated,
		onNubankGenerated,
		onPaymentApproved,
	};
}

