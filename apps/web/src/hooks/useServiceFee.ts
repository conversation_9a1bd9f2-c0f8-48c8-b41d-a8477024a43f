"use client";

import { useCheckout } from "@/contexts";

export const useServiceFee = () => {
	const { offer } = useCheckout();
	const hasGlobalFee = offer?.product?.user?.willBeCharged ?? true;
	
	const getServiceFeeValue = () => {
		const customServiceFee = offer?.serviceFee;

		if (customServiceFee !== undefined && customServiceFee !== null) {
			return customServiceFee;
		}

		const globalServiceFee = 0.99;

		return globalServiceFee;
	};

	const serviceFeeValue = getServiceFeeValue();

	return {
		serviceFeeValue,
		hasServiceFee: hasGlobalFee,
		serviceFeeFormatted: serviceFeeValue.toFixed(2)
	};
};

