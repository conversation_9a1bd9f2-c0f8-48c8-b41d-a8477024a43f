"use client";

import { useCallback } from "react";

import { useIntlContext } from "@/contexts/intl-context";
import { interpolate } from "@/lib/utils/interpolate";

type MessageKey = string;

export function useTranslation() {
	const { messages, locale } = useIntlContext();

	const t = useCallback(
		(key: MessageKey, params?: Record<string, string | number>): string => {
			const value = resolveMessage(messages, key);

			if (typeof value !== "string") {
				console.warn(`Translation not found or invalid for key "${key}"`);
				return key;
			}

			if (!params) {
				return value;
			}

			return interpolate(value, params);
		},
		[messages],
	);

	return { t, locale };
}

function resolveMessage(source: unknown, path: string): unknown {
	const segments = path.split(".");

	return segments.reduce<unknown>((accumulator, segment) => {
		if (typeof accumulator !== "object" || accumulator === null) {
			return undefined;
		}

		return (accumulator as Record<string, unknown>)[segment];
	}, source);
}


