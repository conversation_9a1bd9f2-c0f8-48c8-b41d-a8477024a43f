"use client";

import { useEffect, useState } from "react";
import {
	getInAppBrowser,
	shouldShowOpenInBrowserWarning,
	type InAppBrowserType,
} from "@/lib/utils/browser";

export function useInAppBrowser() {
	const [browser, setBrowser] = useState<InAppBrowserType>(null);
	const [showWarning, setShowWarning] = useState(false);

	useEffect(() => {
		if (typeof window !== "undefined") {
			const detected = getInAppBrowser();
			setBrowser(detected);
			setShowWarning(shouldShowOpenInBrowserWarning());
		}
	}, []);

	return {
		browser,
		isInAppBrowser: browser !== null,
		showWarning,
		dismissWarning: () => setShowWarning(false),
	};
}

