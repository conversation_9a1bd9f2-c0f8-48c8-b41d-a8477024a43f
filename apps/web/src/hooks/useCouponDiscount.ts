"use client";

import { useCheckout } from "@/contexts";

export const useCouponDiscount = () => {
	const { applyCouponValue: shouldApplyCoupon, couponData = { discount: 0, applyOnBumps: false } } =
		useCheckout();

	const applyCoupon = (price: number, bump: boolean = false) => {
		return price - getDiscount(price, bump);
	};

	const getDiscount = (price: number, bump: boolean = false) => {
		if (!shouldApplyCoupon || (bump && !couponData?.applyOnBumps)) {
			return 0;
		}
		return (couponData.discount * price) / 100;
	};

	return {
		applyCoupon,
		getDiscount
	};
};

