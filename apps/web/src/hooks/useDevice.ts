"use client";

import { useEffect, useState } from "react";
import { CheckoutDeviceType } from "@/types/builder";

const useDevice = (): {
	device: CheckoutDeviceType;
} => {
	const getDevice = () => {
		if (typeof window === 'undefined') {
			return CheckoutDeviceType.DESKTOP;
		}
		return window.innerWidth < 768
			? CheckoutDeviceType.MOBILE
			: CheckoutDeviceType.DESKTOP;
	};

	const [device, setDevice] = useState<CheckoutDeviceType>(getDevice());

	useEffect(() => {
		if (typeof window === 'undefined') return;

		const observer = new ResizeObserver(() => {
			setDevice(getDevice());
		});

		observer.observe(document.body);

		return () => observer.disconnect();
	}, []);

	return {
		device,
	};
};

export default useDevice;

