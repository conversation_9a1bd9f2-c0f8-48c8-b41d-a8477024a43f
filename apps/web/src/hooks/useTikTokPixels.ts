"use client";

import TikTokPixel from 'tiktok-pixel';
import type { Payment, ProductData } from "@/types";
import type { TrackingPixels, TiktokPixel as TiktokItem } from "@/types/pixels";
import { calculatePixelValue } from "@/lib/utils/payment";
import { resolveTiktokConfig } from "@/lib/utils/pixels";

export default function useTikTokPixels(offer: ProductData | undefined, activeIndex: number = 0) {
	const trackingPixels = (offer?.tracking_pixels as TrackingPixels) || ({} as TrackingPixels);

	const tiktokItem: TiktokItem | undefined =
		Array.isArray(trackingPixels.tiktok_pixels) && trackingPixels.tiktok_pixels.length > 0
			? trackingPixels.tiktok_pixels[Math.min(activeIndex, trackingPixels.tiktok_pixels.length - 1)]
			: undefined;

	const {
		tiktokBoletoPurchaseTrigger,
		tiktokPixPurchaseTrigger,
		tiktokPicpayPurchaseTrigger,
		tiktokNubankPurchaseTrigger,
		tiktokBoletoConversionValue,
		tiktokPixConversionValue,
		tiktokPicpayConversionValue,
		tiktokNubankConversionValue,
	} = resolveTiktokConfig(trackingPixels, tiktokItem);

	const getPaymentPayload = (payment: Payment) => {
		const disabled_orderbumps = offer?.disable_orderbump_pixel_events || false;
		let orders = Array.isArray(payment?.orders) ? payment.orders : [];
		if (disabled_orderbumps) orders = orders.filter((o) => o?.offer_type !== 'orderbump');
		const contentIds = orders.map((o) => o?.offerId).filter(Boolean);
		const n = Number(payment?.amount);
		const value = !disabled_orderbumps
			? n
			: orders.reduce((acc, o) => acc + Number(o?.amount || 0), 0);
		return { contentIds, value };
	};

	const onCheckoutVisit = () => {
		TikTokPixel.track(
			'InitiateCheckout',
			{
				content_ids: [offer?.id],
				value: Number(offer?.price),
				currency: 'BRL',
			},
		);
	};

	const onTicketGenerated = (payment: Payment): void => {
		const { contentIds, value } = getPaymentPayload(payment);
		if (!contentIds.length) return;

		if (tiktokBoletoPurchaseTrigger) {
			onPaymentApproved(payment, calculatePixelValue(value, tiktokBoletoConversionValue));
			return;
		}

		TikTokPixel.track(
			'boleto_gerado',
			{
				content_ids: contentIds,
				value: Number(value),
				currency: 'BRL',
			},
			{
				eventID: payment?.id,
			} as any,
		);
	};

	const onPixGenerated = (payment: Payment): void => {
		const { contentIds, value } = getPaymentPayload(payment);
		if (!contentIds.length) return;

		if (tiktokPixPurchaseTrigger) {
			onPaymentApproved(payment, calculatePixelValue(value, tiktokPixConversionValue));
			return;
		}

		TikTokPixel.track(
			'pix_gerado',
			{
				content_ids: contentIds,
				value: Number(value),
				currency: 'BRL',
			},
			{
				eventID: payment?.id,
			} as any,
		);
	};

	const onPicpayGenerated = (payment: Payment): void => {
		const { contentIds, value } = getPaymentPayload(payment);
		if (!contentIds.length) return;

		if (tiktokPicpayPurchaseTrigger) {
			onPaymentApproved(payment, calculatePixelValue(value, tiktokPicpayConversionValue));
			return;
		}

		TikTokPixel.track(
			'picpay_gerado',
			{
				content_ids: contentIds,
				value: Number(value),
				currency: 'BRL',
			},
			{
				eventID: payment?.id,
			} as any,
		);
	};

	const onNubankGenerated = (payment: Payment): void => {
		const { contentIds, value } = getPaymentPayload(payment);
		if (!contentIds.length) return;

		if (tiktokNubankPurchaseTrigger) {
			onPaymentApproved(payment, calculatePixelValue(value, tiktokNubankConversionValue));
			return;
		}

		TikTokPixel.track(
			'openfinance_nubank_gerado',
			{
				content_ids: contentIds,
				value: Number(value),
				currency: 'BRL',
			},
			{
				eventID: payment?.id,
			} as any,
		);
	};

	const onPaymentApproved = (payment: Payment, amount: number): void => {
		const purchaseAlreadyTracked =
			(payment?.paymentMethod === 'boleto' && tiktokBoletoPurchaseTrigger) ||
			((payment?.paymentMethod === 'pix' || payment?.paymentMethod === 'pix_auto') && tiktokPixPurchaseTrigger) ||
			(payment?.paymentMethod === 'picpay' && tiktokPicpayPurchaseTrigger) ||
			(payment?.paymentMethod === 'openfinance_nubank' && tiktokNubankPurchaseTrigger);

		const disabled_orderbumps = offer?.disable_orderbump_pixel_events || false;
		if (purchaseAlreadyTracked && payment?.status === 'paid') return;

		const { contentIds, value } = getPaymentPayload(payment);
		if (!contentIds.length) return;

		TikTokPixel.track(
			'Purchase',
			{
				content_ids: contentIds,
				value: !disabled_orderbumps ? Number(amount) : Number(value),
				currency: 'BRL',
			},
			{
				eventID: payment?.id,
			} as any,
		);
	};

	return {
		onCheckoutVisit,
		onTicketGenerated,
		onPixGenerated,
		onPicpayGenerated,
		onNubankGenerated,
		onPaymentApproved,
	};
}

