"use client";

import { useEffect, useState } from "react";
import type { Payment, ProductData } from "@/types";
import type { TrackingPixels, FacebookPixel } from "@/types/pixels";
import { calculatePixelValue } from "@/lib/utils/payment";
import { resolveFacebookConfig } from "@/lib/utils/pixels";

// Wrapper seguro para Facebook Pixel que só executa no cliente
const getFacebookPixel = () => {
	if (typeof window === 'undefined') {
		// Retorna no-op functions no servidor
		return {
			track: () => {},
			trackCustom: () => {},
		};
	}
	
	// Apenas no cliente, importa o Facebook Pixel
	const ReactFacebookPixel = require('@bettercart/react-facebook-pixel');
	return ReactFacebookPixel.default || ReactFacebookPixel;
};

export const useFacebookPixels = (
	offer: ProductData | undefined,
	pixelEventId?: string,
	activeIndex: number = 0,
) => {
	const [isClient, setIsClient] = useState(false);
	
	useEffect(() => {
		setIsClient(true);
	}, []);

	const trackingPixels = (offer?.tracking_pixels as TrackingPixels) || ({} as TrackingPixels);

	const fbItem: FacebookPixel | undefined =
		Array.isArray(trackingPixels.facebook_pixels) && trackingPixels.facebook_pixels.length > 0
			? trackingPixels.facebook_pixels[Math.min(activeIndex, trackingPixels.facebook_pixels.length - 1)]
			: undefined;

	const {
		fbBoletoPurchaseTrigger,
		fbBoletoConversionValue,
		fbPixPurchaseTrigger,
		fbPixConversionValue,
		fbPicpayPurchaseTrigger,
		fbPicpayConversionValue,
		fbNubankPurchaseTrigger,
		fbNubankConversionValue,
	} = resolveFacebookConfig(trackingPixels, fbItem);

	const getPaymentPayload = (payment: Payment) => {
		const disabled_orderbumps = offer?.disable_orderbump_pixel_events || false;
		let orders = Array.isArray(payment?.orders) ? payment.orders : [];
		if (disabled_orderbumps) orders = orders.filter((o) => o?.offer_type != 'orderbump');
		const contentIds = orders.map((o) => o?.offerId).filter(Boolean);
		const n = Number(payment?.amount);
		const value = !disabled_orderbumps
			? n
			: orders.reduce((acc, o) => acc + Number(o?.amount || 0), 0);
		return { contentIds, value };
	};

	const onCheckoutVisit = (offerData: ProductData) => {
		if (!isClient) return;
		const ReactFacebookPixel = getFacebookPixel();
		ReactFacebookPixel.track(
			'InitiateCheckout',
			{
				content_ids: [offerData.id],
				value: Number(offerData.price),
				currency: 'BRL',
			},
			{
				eventID: pixelEventId,
			}
		);
	};

	const onTicketGenerated = (payment: Payment) => {
		if (!isClient) return;
		const { contentIds, value } = getPaymentPayload(payment);
		if (!contentIds.length) return;

		if (fbBoletoPurchaseTrigger) {
			return onPaymentApproved(payment, calculatePixelValue(value, fbBoletoConversionValue));
		}

		const ReactFacebookPixel = getFacebookPixel();
		ReactFacebookPixel.trackCustom(
			'boleto_gerado',
			{
				content_ids: contentIds,
				value: Number(value),
				currency: 'BRL',
			},
			{
				eventID: payment?.id,
			}
		);
	};

	const onPixGenerated = (payment: Payment) => {
		if (!isClient) return;
		const { contentIds, value } = getPaymentPayload(payment);
		if (!contentIds.length) return;

		if (fbPixPurchaseTrigger) {
			return onPaymentApproved(payment, calculatePixelValue(value, fbPixConversionValue));
		}

		const ReactFacebookPixel = getFacebookPixel();
		ReactFacebookPixel.trackCustom(
			'pix_gerado',
			{
				content_ids: contentIds,
				value: Number(value),
				currency: 'BRL',
			},
			{
				eventID: payment?.id,
			}
		);
	};

	const onPicpayGenerated = (payment: Payment) => {
		if (!isClient) return;
		const { contentIds, value } = getPaymentPayload(payment);
		if (!contentIds.length) return;

		if (fbPicpayPurchaseTrigger) {
			return onPaymentApproved(payment, calculatePixelValue(value, fbPicpayConversionValue));
		}

		const ReactFacebookPixel = getFacebookPixel();
		ReactFacebookPixel.trackCustom(
			'picpay_gerado',
			{
				content_ids: contentIds,
				value: Number(value),
				currency: 'BRL',
			},
			{
				eventID: payment?.id,
			}
		);
	};

	const onNubankGenerated = (payment: Payment) => {
		if (!isClient) return;
		const { contentIds, value } = getPaymentPayload(payment);
		if (!contentIds.length) return;

		if (fbNubankPurchaseTrigger) {
			return onPaymentApproved(payment, calculatePixelValue(value, fbNubankConversionValue));
		}

		const ReactFacebookPixel = getFacebookPixel();
		ReactFacebookPixel.trackCustom(
			'openfinance_nubank_gerado',
			{
				content_ids: contentIds,
				value: Number(value),
				currency: 'BRL',
			},
			{
				eventID: payment?.id,
			}
		);
	};

	const onPaymentApproved = (payment: Payment, amount: number) => {
		if (!isClient) return;
		
		const purchaseAlreadyTracked =
			(payment?.paymentMethod === 'boleto' && fbBoletoPurchaseTrigger) ||
			((payment?.paymentMethod === 'pix' || payment?.paymentMethod === 'pix_auto') && fbPixPurchaseTrigger) ||
			(payment?.paymentMethod === 'picpay' && fbPicpayPurchaseTrigger) ||
			(payment?.paymentMethod === 'openfinance_nubank' && fbNubankPurchaseTrigger);

		const disabled_orderbumps = offer?.disable_orderbump_pixel_events || false;
		if (purchaseAlreadyTracked && payment?.status === 'paid') return;

		const { contentIds, value } = getPaymentPayload(payment);
		if (!contentIds.length) return;

		const ReactFacebookPixel = getFacebookPixel();
		ReactFacebookPixel.track(
			'Purchase',
			{
				content_ids: contentIds,
				value: !disabled_orderbumps ? Number(amount) : Number(value),
				currency: 'BRL',
			},
			{
				eventID: payment?.id,
			}
		);

		ReactFacebookPixel.trackCustom(
			payment?.paymentMethod,
			{
				event_id: payment?.id,
				content_ids: contentIds,
				value: !disabled_orderbumps ? Number(amount) : Number(value),
				currency: 'BRL',
			},
			{
				eventID: payment?.id,
			}
		);
	};

	return {
		onCheckoutVisit,
		onTicketGenerated,
		onPixGenerated,
		onPicpayGenerated,
		onNubankGenerated,
		onPaymentApproved,
	};
};

