"use client";

import { useReducer } from "react";
import { v4 } from "uuid";
import type { CheckoutComponentNotificationType } from "@/types/builder";

export type Notification = {
	id: string;
	message: string;
	exibitionTime: number;
	type: CheckoutComponentNotificationType;
};

type ReducerState = {
	notifications: Notification[];
};

enum ActionTypes {
	NOTIFY = "notify",
	DISMISS = "dismiss",
}

type Action<T extends ActionTypes, P> = {
	type: T;
	payload: P;
};

type NotifyAction = Action<
	ActionTypes.NOTIFY,
	{
		message: string;
		type: CheckoutComponentNotificationType;
		exibitionTime: number;
	}
>;

type DismissAction = Action<ActionTypes.DISMISS, { id: string }>;

const reducer = (
	state: ReducerState,
	action: NotifyAction | DismissAction,
) => {
	switch (action.type) {
		case ActionTypes.NOTIFY:
			return {
				...state,
				notifications: [
					...state.notifications,
					{
						id: v4(),
						message: action.payload.message,
						type: action.payload.type,
						exibitionTime: action.payload.exibitionTime,
					},
				],
			};
		case ActionTypes.DISMISS:
			return {
				...state,
				notifications: state.notifications.filter(
					(notification) => notification.id !== action.payload.id,
				),
			};
		default:
			return state;
	}
};

const initialState: ReducerState = {
	notifications: [],
};

const useNotifications = () => {
	const [{ notifications }, dispatch] = useReducer(reducer, initialState);

	const notify = (
		message: string,
		type: CheckoutComponentNotificationType,
		exibitionTime: number,
	) => {
		dispatch({
			type: ActionTypes.NOTIFY,
			payload: { message, type, exibitionTime },
		});
	};

	const dismiss = (id: string) => {
		dispatch({ type: ActionTypes.DISMISS, payload: { id } });
	};

	return { notifications, notify, dismiss };
};

export default useNotifications;

