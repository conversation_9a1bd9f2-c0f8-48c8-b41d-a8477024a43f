"use client";

import { useEffect } from "react";

const usePageLeave = (onLeave: () => void) => {
	useEffect(() => {
		if (typeof window === 'undefined') return;

		const onBeforeUnload = (e: BeforeUnloadEvent) => {
			(e || window.event).returnValue = ""; // Gecko + IE
			onLeave();
			return ""; // Webkit, Safari, Chrome etc.
		};

		window.addEventListener("beforeunload", onBeforeUnload);

		return () => {
			window.removeEventListener("beforeunload", onBeforeUnload);
		};
	}, [onLeave]);
};

export { usePageLeave };

