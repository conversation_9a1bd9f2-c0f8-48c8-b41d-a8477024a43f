"use client";

import { useMemo } from "react";
import useDevice from "./useDevice";
import { useCheckout } from "@/contexts";
import { isNommi } from "@/lib/utils/brand";

export default function useSettings() {
	const { offer, paymentMethod, isFetching } = useCheckout();
	const { device } = useDevice();

	const defaultTheme = {
		backgroundColor: "#f1f1f1",
		backgroundImage: "",
		backgroundSize: "",
		backgroundPosition: "",
		backgroundRepeat: "",

		form: {
			background: {
				color: "#ffffff",
			},
		},
		text: {
			color: {
				primary: "#000000",
				secondary: "#4B5563",
				active: "#0B6856",
			},
		},
		icon: {
			color: "#000000",
		},
		paymentOptions: {
			button: {
				unselected: {
					text: {
						color: "#4B5563",
					},
					background: {
						color: "#ffffff",
					},
					icon: {
						color: "#000000",
					},
				},
				selected: {
					text: {
						color: "#ffffff",
					},
					background: {
						color: isNommi ? "#2886B9" : "#0B6856",
					},
					icon: {
						color: "#ffffff",
					},
				},
			},
		},
		payButton: {
			text: {
				text: isFetching ? "Carregando..." : `Pagar com ${paymentMethod?.name}`,
				color: "#ffffff",
			},
			color: isNommi ? "#2886B9" : "#0B6856",
		},
		box: {
			default: {
				header: {
					background: {
						color: "#D1D1D1",
					},
					text: {
						color: {
							primary: "#000000",
							secondary: "#000000",
						},
					},
				},
				background: {
					color: "#EDEDED",
				},
				text: {
					color: {
						primary: "#000000",
						secondary: "#000000",
					},
				},
			},
			unselected: {
				header: {
					background: {
						color: isNommi ? "#FFD666" : "rgb(11, 104, 86)",
					},
					text: {
						color: {
							primary: isNommi ? "#000" : "#ffffff",
							secondary: isNommi ? "#000" : "#ffffff",
						},
					},
				},
				background: {
					color: "#EDEDED",
				},
				text: {
					color: {
						primary: "#4B5563",
						secondary: "#4B5563",
					},
				},
			},
			selected: {
				header: {
					background: {
						color: isNommi ? "#2886B9" : "#0B6856",
					},
					text: {
						color: {
							primary: "#ffffff",
							secondary: "#ffffff",
						},
					},
				},
				background: {
					color: "#ffffff",
				},
				text: {
					color: {
						primary: "#4B5563",
						secondary: "#4B5563",
					},
				},
			},
		},
		lastDesktopRows: null,
	};

	const settings = useMemo(() => {
		if (!offer?.checkout?.config) {
			return defaultTheme;
		}

		const desktopRows = offer?.checkout?.config?.desktop?.rows || [];
		const lastDesktopRows = desktopRows[desktopRows.length - 1]?.layout;

		const { checkout } = offer;

		const config = checkout.config || {};

		const deviceConfig = config?.[device] || {};
		const settings = deviceConfig?.settings || {};
		const background = settings?.background || {};
		const color = background?.color || "#ffffff";
		const imagePreview = background?.image?.preview || "";
		const cover = background?.cover || "";
		const fixed = background?.fixed || "";
		const repeat = background?.repeat || "";

		return {
			backgroundColor: color,
			backgroundImage: `url("${imagePreview}")`,
			backgroundSize: cover ? "cover" : "",
			backgroundPosition: fixed ? "fixed" : "",
			backgroundRepeat: repeat ? "repeat" : "",
			form: {
				background: {
					color:
						offer?.checkout.config[device].settings?.form?.background?.color ||
						defaultTheme.form.background.color,
				},
			},
			icon: {
				color:
					offer?.checkout.config[device].settings?.icon?.color ||
					defaultTheme.icon.color,
			},
			paymentOptions: {
				button: {
					unselected: {
						text: {
							color:
								offer?.checkout.config[device]?.settings?.paymentOptions?.button
									?.unselected?.text?.color ||
								defaultTheme.paymentOptions.button.unselected.text.color,
						},
						background: {
							color:
								offer?.checkout.config[device]?.settings?.paymentOptions?.button
									?.unselected?.background?.color ||
								defaultTheme.paymentOptions.button.unselected.background.color,
						},
						icon: {
							color:
								offer?.checkout.config[device]?.settings?.paymentOptions?.button
									?.unselected?.icon?.color ||
								defaultTheme.paymentOptions.button.unselected.icon.color,
						},
					},
					selected: {
						text: {
							color:
								offer?.checkout.config[device]?.settings?.paymentOptions?.button
									?.selected?.text?.color ||
								defaultTheme.paymentOptions.button.selected.text.color,
						},
						background: {
							color:
								offer?.checkout.config[device]?.settings?.paymentOptions?.button
									?.selected?.background?.color ||
								defaultTheme.paymentOptions.button.selected.background.color,
						},
						icon: {
							color:
								offer?.checkout.config[device]?.settings?.paymentOptions?.button
									?.selected?.icon?.color ||
								defaultTheme.paymentOptions.button.selected.icon.color,
						},
					},
				},
			},
			payButton: {
				...offer?.checkout.config[device]?.settings?.payButton,
				text: {
					color:
						offer?.checkout.config[device]?.settings?.payButton?.text.color ||
						defaultTheme.payButton.text.color,
					text: `Pagar com ${paymentMethod?.name}`,
				},
			},
			text: {
				color: {
					primary:
						offer?.checkout?.config[device]?.settings?.text?.color?.primary ||
						defaultTheme.text.color.primary,
					secondary:
						offer?.checkout?.config[device]?.settings?.text?.color?.secondary ||
						defaultTheme.text.color.secondary,
					active:
						offer?.checkout?.config[device]?.settings?.text?.color?.active ||
						defaultTheme.text.color.active,
				},
			},
			box: {
				default: {
					header: {
						background: {
							color:
								offer?.checkout?.config[device]?.settings?.box?.default?.header
									?.background?.color ||
								defaultTheme.box.default.header.background.color,
						},
						text: {
							color: {
								primary:
									offer?.checkout?.config[device]?.settings?.box?.unselected
										?.header?.text?.color.primary ||
									defaultTheme.box.unselected.header.text.color.primary,
								secondary:
									offer?.checkout?.config[device]?.settings?.box?.unselected
										?.header?.text?.color.secondary ||
									defaultTheme.box.unselected.header.text.color.secondary,
							},
						},
					},
					background: {
						color:
							offer?.checkout?.config[device]?.settings?.box?.default?.background
								?.color || defaultTheme.box.default.background.color,
					},
					text: {
						color: {
							primary:
								offer?.checkout?.config[device]?.settings?.box?.default?.text
									?.color.primary || defaultTheme.box.default.text.color.primary,
							secondary:
								offer?.checkout?.config[device]?.settings?.box?.default?.text
									?.color.primary || defaultTheme.box.default.text.color.primary,
						},
					},
				},
				unselected: {
					header: {
						background: {
							color:
								offer?.checkout?.config[device]?.settings?.box?.unselected?.header
									?.background?.color ||
								defaultTheme.box.unselected.header.background.color,
						},
						text: {
							color: {
								primary:
									offer?.checkout?.config[device]?.settings?.box?.unselected
										?.header?.text?.color.primary ||
									defaultTheme.box.unselected.header.text.color.primary,
								secondary:
									offer?.checkout?.config[device]?.settings?.box?.unselected
										?.header?.text?.color.secondary ||
									defaultTheme.box.unselected.header.text.color.secondary,
							},
						},
					},
					background: {
						color:
							offer?.checkout?.config[device]?.settings?.box?.unselected?.background
								?.color || defaultTheme.box.unselected.background.color,
					},
					text: {
						color: {
							primary:
								offer?.checkout?.config[device]?.settings?.box?.unselected?.text
									?.color.primary ||
								defaultTheme.box.unselected.text.color.primary,
							secondary:
								offer?.checkout?.config[device]?.settings?.box?.unselected?.text
									?.color.secondary ||
								defaultTheme.box.unselected.text.color.secondary,
						},
					},
				},
				selected: {
					header: {
						background: {
							color:
								offer?.checkout?.config[device]?.settings?.box?.selected?.header
									?.background.color ||
								defaultTheme.box.selected.header.background.color,
						},
						text: {
							color: {
								primary:
									offer?.checkout?.config[device]?.settings?.box?.selected?.header
										?.text?.color.primary ||
									defaultTheme.box.selected.header.text.color.primary,
								secondary:
									offer?.checkout?.config[device]?.settings?.box?.selected?.header
										?.text?.color.secondary ||
									defaultTheme.box.selected.header.text.color.secondary,
							},
						},
					},
					background: {
						color:
							offer?.checkout?.config[device]?.settings?.box?.selected?.background
								?.color || defaultTheme.box.selected.background.color,
					},
					text: {
						color: {
							primary:
								offer?.checkout?.config[device]?.settings?.box?.selected?.text
									?.color.primary || defaultTheme.box.selected.text.color.primary,
							secondary:
								offer?.checkout?.config[device]?.settings?.box?.selected?.text
									?.color.secondary ||
								defaultTheme.box.selected.text.color.secondary,
						},
					},
				},
			},
			lastDesktopRows: JSON.stringify(lastDesktopRows),
		};
	}, [offer, device, paymentMethod]);

	return settings;
}

