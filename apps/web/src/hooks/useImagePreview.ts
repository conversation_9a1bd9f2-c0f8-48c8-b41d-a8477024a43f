"use client";

import type { CheckoutImage } from "@/types/builder";

type useImagePreviewProps = {
  image: CheckoutImage;
};

type useImagePreviewReturn = {
  preview: string;
};

const useImagePreview = ({
  image,
}: useImagePreviewProps): useImagePreviewReturn => {
  if (image instanceof File) {
    return {
      preview: URL.createObjectURL(image),
    };
  }
  return {
    preview: image?.preview,
  };
};

export default useImagePreview;

