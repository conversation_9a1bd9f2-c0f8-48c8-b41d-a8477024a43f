"use client";

import { useState, useCallback, useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import { cepService } from '@/lib/api/cep-service';
import type { AddressData } from '@/lib/api/cep-service';

type FormValues = {
	street?: string;
	neighborhood?: string;
	city?: string;
	state?: string;
	number?: string;
	complement?: string;
	zipCode?: string;
};

export const useCepSearch = () => {
	const { setValue, getValues } = useFormContext();
	const [isLoadingCep, setIsLoadingCep] = useState(false);
	const [hasManualChanges, setHasManualChanges] = useState({
		street: false,
		neighborhood: false,
		city: false,
		state: false,
	});
	const lastSearchedCep = useRef<string>('');

	const searchCep = useCallback(async (cep: string): Promise<AddressData | null> => {
		if (!cepService.isValidCep(cep)) {
			return null;
		}
		const normalizedCep = cep.replace(/\D/g, '');
		if (lastSearchedCep.current === normalizedCep) {
			return null;
		}
		lastSearchedCep.current = normalizedCep;
		setIsLoadingCep(true);
		try {
			const addressData = await cepService.searchCep(cep);
			if (addressData && setValue && getValues) {
				const currentValues = {
					street: getValues()?.street,
					neighborhood: getValues()?.neighborhood,
					city: getValues()?.city,
					state: getValues()?.state,
					number: getValues()?.number,
					complement: getValues()?.complement,
				};

				if (!hasManualChanges.street && !currentValues.street) {
					setValue('street', addressData.street || '', {
						shouldValidate: false,
					});
				}
				if (!hasManualChanges.neighborhood && !currentValues.neighborhood) {
					setValue('neighborhood', addressData.neighborhood || '', {
						shouldValidate: false,
					});
				}
				if (!hasManualChanges.city && !currentValues.city) {
					setValue('city', addressData.city || '', {
						shouldValidate: false,
					});
				}
				if (!hasManualChanges.state && !currentValues.state) {
					setValue('state', addressData.state || '', {
						shouldValidate: false,
					});
				}
				setValue('number', currentValues.number || '', {
					shouldValidate: false,
				});
				setValue('complement', currentValues.complement || '', {
					shouldValidate: false,
				});
			}
			return addressData;
		} catch (error) {
			console.error('Erro ao buscar CEP:', error);
			return null;
		} finally {
			setIsLoadingCep(false);
		}
	}, [setValue, getValues, hasManualChanges.street, hasManualChanges.neighborhood, hasManualChanges.city, hasManualChanges.state]);

	const handleManualChange = useCallback((field: keyof typeof hasManualChanges) => {
		setHasManualChanges((prev) => ({
			...prev,
			[field]: true,
		}));
	}, []);

	const resetManualChanges = useCallback(() => {
		setHasManualChanges({
			street: false,
			neighborhood: false,
			city: false,
			state: false,
		});
	}, []);

	return {
		searchCep,
		isLoadingCep,
		handleManualChange,
		resetManualChanges
	};
};

