"use client";

import { useState, useEffect } from 'react';

type Props = {
	offerId?: string;
	isLoadingInstallments?: boolean;
};

export const useIsFetchingCalculatedInstallments = ({ 
	offerId,
	isLoadingInstallments = false 
}: Props = {}) => {
	const [isFetchingInstallments, setIsFetchingInstallments] = useState(false);

	useEffect(() => {
		setIsFetchingInstallments(isLoadingInstallments);
	}, [isLoadingInstallments]);

	return {
		isFetchingInstallments
	};
};

