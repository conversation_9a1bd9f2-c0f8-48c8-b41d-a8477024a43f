import type { NextConfig } from "next";
import path from "path";

const nextConfig: NextConfig = {
	typedRoutes: true,
	reactCompiler: true,
	experimental: {
		turbopack: {
			// Define a raiz do workspace do monorepo
			root: path.resolve(__dirname, "../.."),
		},
	},
	images: {
		remotePatterns: [
			{
				protocol: "https",
				hostname: "cdn-checkout.cakto.com.br",
				port: "",
				pathname: "/**",
			},
		],
	},
};

export default nextConfig;
