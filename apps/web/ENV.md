# Variáveis de Ambiente

Este documento descreve todas as variáveis de ambiente necessárias para o projeto.

## Padrão Next.js

No Next.js, as variáveis de ambiente seguem um padrão específico:

- **Variáveis sem prefixo**: Apenas no servidor (server-side only)
- **Variáveis com `NEXT_PUBLIC_`**: Expostas ao cliente (client-side accessible)

## Variáveis de Servidor (Server-side only)

Essas variáveis são acessíveis apenas no servidor e não são expostas ao cliente.

### API Configuration

```env
CHECKOUT_API_URL=https://checkout-api.example.com
BASE_CHECKOUT_API_URL=https://checkout-api.example.com
API_BASE_URL=https://api.example.com
```

### GeoIP Resolver

```env
GEOIP_RESOLVER_URL=https://ipapi.co/:ip/country/
GEOIP_RESOLVER_TOKEN=your_token_here
```

## Variáveis <PERSON> (Client-side accessible)

Essas variáveis são expostas ao cliente através do prefixo `NEXT_PUBLIC_`.

### Branding & Images

```env
NEXT_PUBLIC_BRANDING_IMAGES_URL=https://images.example.com
NEXT_PUBLIC_BRANDING=default
NEXT_PUBLIC_CAKTO_URL=https://cakto.example.com
```

### Payment Providers

```env
NEXT_PUBLIC_HOPYPAY_PUBLIC_KEY=your_hopypay_public_key
NEXT_PUBLIC_EBANX_PUBLIC_KEY=your_ebanx_public_key
```

### PostHog Analytics

```env
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key
NEXT_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com
```

### 3DS Configuration

```env
NEXT_PUBLIC_3DS_PROVIDER=cielo
NEXT_PUBLIC_CIELO_3DS_API_VERSION=v2
NEXT_PUBLIC_CIELO_3DS_SCRIPT=https://3ds2.cielo.com.br/v1/3ds2.min.js
NEXT_PUBLIC_PAGARME_3DS_SCRIPT=https://3ds2.pagar.me/v1/3ds2.min.js
```

### Antifraud (Nethone)

```env
NEXT_PUBLIC_NETHONE_SCRIPT_SRC=https://nethone.example.com/script.js
```

### Google Pay

```env
NEXT_PUBLIC_GOOGLEPAY_ENVIRONMENT=TEST
NEXT_PUBLIC_GOOGLEPAY_GATEWAY=your_gateway
NEXT_PUBLIC_GOOGLEPAY_GATEWAY_MERCHANT_ID=your_merchant_id
NEXT_PUBLIC_GOOGLEPAY_MERCHANT_NAME=Your Merchant Name
NEXT_PUBLIC_GOOGLEPAY_MERCHANT_ID=your_merchant_id
```

### Google Maps

```env
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

### Country Detection (GeoIP)

```env
NEXT_PUBLIC_GEOIP_API_URL=https://ipapi.co
```

> **Nota**: O serviço ipapi.co oferece 1000 requisições gratuitas por dia. Para uso em produção, considere criar uma conta ou usar um serviço alternativo como ipinfo.io (50k req/mês grátis).

### PIX Configuration

```env
NEXT_PUBLIC_PIX_CHECK_INTERVAL=5000
NEXT_PUBLIC_PIX_CHECK_DURATION=300000
```

### Mock API (for testing)

```env
NEXT_PUBLIC_MOCK_API_URL=https://mock-api.example.com
```

### Environment

```env
NEXT_PUBLIC_ENVIRONMENT=development
```

## Uso no Código

Para acessar variáveis de ambiente no código, use as funções helper em `src/lib/env.ts`:

```typescript
import { 
  getCheckoutApiUrl, 
  getPostHogKey, 
  isDevelopment,
  getGeoipApiUrl,
  getEbanxPublicKey
} from "@/lib/env";

// Server-side
const apiUrl = getCheckoutApiUrl();

// Client-side
const posthogKey = getPostHogKey();
const isDev = isDevelopment();
const geoipUrl = getGeoipApiUrl();
const ebanxKey = getEbanxPublicKey();
```

## Migração do Vite

Se você está migrando do projeto Vite original:

- `VITE_BASE_API_URL` → `API_BASE_URL` (server-side) ou `NEXT_PUBLIC_*` (se necessário no cliente)
- `VITE_BASE_CHECKOUT_API_URL` → `CHECKOUT_API_URL` (server-side)
- `VITE_*` → `NEXT_PUBLIC_*` (para variáveis que precisam ser acessíveis no cliente)

## Arquivos de Ambiente

- `.env.local`: Variáveis locais (não versionadas)
- `.env.example`: Template com todas as variáveis necessárias
- `.env.production`: Variáveis de produção (se necessário)
- `.env.development`: Variáveis de desenvolvimento (se necessário)

