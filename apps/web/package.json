{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port=3001", "build": "next build", "start": "next start"}, "dependencies": {"@bettercart/react-facebook-pixel": "^2.0.1", "@heroicons/react": "^2.0.18", "@my-better-t-app/api": "workspace:*", "@orpc/client": "catalog:", "@orpc/server": "catalog:", "@orpc/tanstack-query": "^1.10.0", "@radix-ui/react-popover": "^1.1.15", "@tanstack/react-form": "^1.12.3", "@tanstack/react-query": "^5.85.5", "babel-plugin-react-compiler": "^1.0.0", "class-variance-authority": "^0.7.1", "classnames": "^2.3.2", "clientjs": "0.2.1", "clsx": "^2.1.1", "cpf-cnpj-validator": "^1.0.3", "credit-card-type": "^10.0.2", "lucide-react": "^0.546.0", "moment": "^2.29.4", "next": "16.0.0", "next-themes": "^0.4.6", "payment-token-efi": "^3.1.2", "posthog-js": "^1.256.2", "radix-ui": "^1.4.2", "react": "19.2.0", "react-confetti": "^6.4.0", "react-dom": "19.2.0", "react-ga4": "^2.1.0", "react-hook-form": "^7.46.2", "react-player": "^2.13.0", "react-qrcode-logo": "^3.0.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwind-variants": "^0.1.14", "tiktok-pixel": "^2.0.3", "tw-animate-css": "^1.3.4", "uuid": "^9.0.1", "zod": "catalog:"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@tanstack/react-query-devtools": "^5.85.5", "@types/clientjs": "0.2.2", "@types/node": "catalog:", "@types/react": "19.2.2", "@types/react-dom": "19.2.2", "@types/uuid": "^9.0.7", "tailwindcss": "^4.1.10", "typescript": "catalog:"}}