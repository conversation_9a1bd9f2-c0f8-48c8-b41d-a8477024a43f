# Prompt de Finalização - Internacionalização cakto-checkoutv2

## 🎯 OBJETIVO PRINCIPAL

Finalizar a implementação completa de internacionalização (i18n) do sistema de checkout, garantindo que **ZERO textos hardcoded** permaneçam no código. O projeto deve suportar 3 idiomas (Português, Inglês e Espanhol) com 100% de cobertura.

## 📊 STATUS ATUAL: ~95% COMPLETO

### ✅ O QUE JÁ FOI FEITO (Última Sessão)

#### 1. **Componentes Totalmente Internacionalizados (8)**
- ✅ `InstallmentsSelector.tsx` - Seletor de parcelas/assinatura
- ✅ `PixAutoSuccessPayment.tsx` - Tela de sucesso PIX Automático
- ✅ `PicPayPayment.tsx` - Pagamento PicPay
- ✅ `PixAutoPayment.tsx` - Pagamento PIX Automático
- ✅ `payment-methods.tsx` - Métodos de pagamento
- ✅ `checkout-form.tsx` - Formulário principal
- ✅ `PixAutoForm.tsx` - Formulário PIX Auto
- ✅ Múltiplos componentes de UI (correções de tipo)

#### 2. **Traduções Adicionadas (30+ Novas Chaves)**

**Arquivo**: `apps/web/src/lib/i18n/messages/pt.json` (+ en.json + es.json)

```json
{
  "checkout": {
    "loading_checkout": "Carregando checkout...",
    "no_payment_methods": "Nenhum método de pagamento disponível",
    "not_found": "Não encontrado"
  },
  "payment": {
    "subscription": "Assinatura",
    "new_badge": "NOVO",
    "loading_installments": "Carregando...",
    "pix_auto": {
      "info_1": "Com o Pix Automático, as renovações são feitas de forma recorrente...",
      "info_2": "Autorize uma vez o pagamento no app do seu banco..."
    }
  },
  "success": {
    "amount": "Valor",
    "status": "Status",
    "transaction_id_label": "ID da Transação",
    "pix_auto_configured": "PIX Automático configurado com sucesso!",
    "pix_auto_configured_description": "Seu pagamento foi aprovado...",
    "pix_auto_active": "PIX Automático Ativo",
    "pix_auto_active_description": "A partir de agora, suas cobranças...",
    "access_my_product": "ACESSAR MEU PRODUTO",
    "email_confirmation_sent": "Também enviamos uma confirmação...",
    "qrcode_generated": "QRCode gerado com sucesso!",
    "finalize_payment_now": "Agora é só finalizar o pagamento",
    "expires_in_label": "Este código expirará em:",
    "open_picpay_scan": "Abra o PicPay em seu telefone...",
    "qrcode_problem": "Se tiver algum problema com a leitura...",
    "already_paid": "Já fiz o pagamento",
    "pay_now_pix_auto": "Pagar agora com o Pix Automático"
  }
}
```

**IMPORTANTE**: Todas essas chaves foram adicionadas nos 3 idiomas (pt/en/es) com traduções completas.

#### 3. **15+ Erros TypeScript Corrigidos**

Todos relacionados ao `verbatimModuleSyntax` do TypeScript 5.8+:

```typescript
// ❌ ANTES (Errado)
import { SVGProps, ClipboardEvent } from "react";

// ✅ DEPOIS (Correto)
import type { SVGProps, ClipboardEvent } from "react";
```

**Arquivos corrigidos**:
- CheckoutComponentRenderer.tsx
- CheckoutComponentTestimonial.tsx
- address-form.tsx
- locale-selector.tsx
- SecurityIcon.tsx
- InstallmentsSelector.tsx
- ApplePayForm.tsx, GooglePayForm.tsx, CreditCardForm.tsx
- OrderSummary.tsx
- PixAutoSuccessPayment.tsx
- button-form.tsx
- text-field.tsx

#### 4. **Tipos TypeScript Atualizados**

**Arquivo**: `apps/web/src/types/i18n.ts`

```typescript
export type Messages = {
  checkout: {
    loading_checkout: MessageValue;
    no_payment_methods: MessageValue;
    not_found: MessageValue;
    // ... outros
  };
  payment: {
    subscription: MessageValue;
    new_badge: MessageValue;
    loading_installments: MessageValue;
    // ... outros
  };
  success: {
    amount: MessageValue;
    status: MessageValue;
    transaction_id_label: MessageValue;
    pix_auto_configured: MessageValue;
    // ... 10+ novos campos
  };
};
```

## ⏳ O QUE FALTA FAZER (5% Restante)

### 🔴 TAREFA 1: Completar Build TypeScript (ALTA PRIORIDADE)

**Problema**: O build estava falhando no último arquivo com erro de import.

**Último erro encontrado**:
```
./src/components/ui/text-field.tsx:5:17
Type error: 'ClipboardEvent' is a type and must be imported using a type-only import
```

**Já foi corrigido**, mas o build não foi completado até o final.

**Ação necessária**:
```bash
cd /Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2/apps/web
pnpm build
```

**Se encontrar mais erros de tipo**:
- Procure por imports de tipos que não usam `import type`
- Padrão: `import { Tipo } from 'lib'` → `import type { Tipo } from 'lib'`
- Apenas tipos devem usar `import type`
- Valores/funções usam `import` normal

### 🟡 TAREFA 2: Busca por Strings Hardcoded (MÉDIA PRIORIDADE)

Ainda podem existir textos em português/espanhol/inglês hardcoded que não foram detectados.

**Comandos para buscar**:

```bash
cd /Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2/apps/web/src

# Buscar português
grep -r --include="*.tsx" --include="*.ts" \
  -E "(Aguardando|Pagamento|Carregando|Processando|Enviando|Confirmar|Cancelar|Erro|Sucesso|Produto|Compra|Total|Valor|Desconto)" \
  . | grep -v "node_modules" | grep -v ".next" | grep -v "i18n/messages"

# Buscar strings entre aspas que parecem ser mensagens de UI
grep -r --include="*.tsx" --include="*.ts" \
  -P '"[A-ZÁÉÍÓÚÂÊÔÃÕÇ][a-záéíóúâêôãõç\s]{10,}"' \
  . | grep -v "node_modules" | grep -v ".next" | grep -v "i18n/messages" | grep -v "className"

# Buscar arrays com mensagens (como const MESSAGES = [...])
grep -r --include="*.tsx" --include="*.ts" \
  -B 5 "message.*:" \
  . | grep -v "node_modules" | grep -v ".next"
```

**Para cada string encontrada**:

1. **Adicione a chave nas traduções** (pt.json, en.json, es.json)
2. **Adicione o tipo** em `src/types/i18n.ts`
3. **Substitua no componente**:
   ```typescript
   // ❌ Antes
   <span>Carregando dados...</span>
   
   // ✅ Depois
   const { t } = useTranslation();
   <span>{t('common.loading_data')}</span>
   ```

### 🟢 TAREFA 3: Testar os 3 Idiomas (BAIXA PRIORIDADE)

**Pré-requisito**: Build deve estar funcionando (Tarefa 1 completa)

```bash
# Iniciar servidor de desenvolvimento
cd /Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2
pnpm dev:web

# Acessar nas 3 línguas
# http://localhost:3001/pt/7rohg3i  (Português)
# http://localhost:3001/en/7rohg3i  (Inglês)
# http://localhost:3001/es/7rohg3i  (Espanhol)
```

**Verificar**:
- [ ] Todos os textos aparecem no idioma correto
- [ ] Troca de idioma funciona no seletor
- [ ] Nenhum texto em português aparece nas versões EN/ES
- [ ] Formatação de números/moedas está correta
- [ ] Datas estão no formato correto para cada idioma

### 🟢 TAREFA 4 (OPCIONAL): Criar Componentes UX Faltantes

**Estes componentes ainda não existem, mas são mencionados na documentação**:

#### A. Toast Component (`src/components/ui/toast.tsx`)

```typescript
"use client";

import { useTranslation } from "@/hooks/useTranslation";

type ToastType = "success" | "error" | "warning" | "info";

interface ToastProps {
  type: ToastType;
  message: string;
  duration?: number;
}

export function Toast({ type, message, duration = 3000 }: ToastProps) {
  const { t } = useTranslation();
  
  // Implementar lógica do toast
  // Usar t() para textos como "Fechar", "Erro", etc.
}
```

**Traduções necessárias**:
```json
{
  "toast": {
    "close": "Fechar",
    "success": "Sucesso",
    "error": "Erro",
    "warning": "Aviso",
    "info": "Informação"
  }
}
```

#### B. Skeleton Component (`src/components/ui/skeleton.tsx`)

```typescript
"use client";

interface SkeletonProps {
  className?: string;
  count?: number;
}

export function Skeleton({ className, count = 1 }: SkeletonProps) {
  // Componente de loading sem texto
  // Não precisa de i18n
}
```

#### C. ErrorBoundary Component (`src/components/ui/error-boundary.tsx`)

```typescript
"use client";

import { Component, ReactNode } from "react";
import { useTranslation } from "@/hooks/useTranslation";

export class ErrorBoundary extends Component<
  { children: ReactNode },
  { hasError: boolean }
> {
  // Implementar error boundary
  // Usar contexto de tradução para mensagens de erro
}
```

**Traduções necessárias**:
```json
{
  "errors": {
    "boundary": {
      "title": "Algo deu errado",
      "description": "Ocorreu um erro inesperado. Por favor, recarregue a página.",
      "reload": "Recarregar página"
    }
  }
}
```

## 📋 CHECKLIST FINAL DE SUCESSO

Marque cada item quando completar:

### Build & Compilação
- [ ] `pnpm build` executa sem erros TypeScript
- [ ] `pnpm build` executa sem warnings críticos
- [ ] Bundle size está otimizado

### Internacionalização
- [ ] Zero strings hardcoded em português encontradas
- [ ] Zero strings hardcoded em inglês encontradas  
- [ ] Zero strings hardcoded em espanhol encontradas
- [ ] Todos os arquivos .tsx usam `useTranslation()` quando há texto de UI
- [ ] Todos os 3 idiomas (pt/en/es) funcionam corretamente
- [ ] Troca de idioma funciona sem refresh

### Qualidade de Código
- [ ] Sem erros de linting
- [ ] Sem warnings TypeScript
- [ ] Imports organizados (type imports separados)
- [ ] Código segue padrões do projeto

### Testes Manuais
- [ ] Testado checkout em Português
- [ ] Testado checkout em Inglês
- [ ] Testado checkout em Espanhol
- [ ] Testado troca de idioma em tempo real
- [ ] Testado todos os métodos de pagamento em 3 idiomas
- [ ] Testado telas de sucesso/erro em 3 idiomas

## 🛠️ COMANDOS ÚTEIS

```bash
# Diretório base
cd /Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2

# Build completo
cd apps/web && pnpm build

# Desenvolvimento
pnpm dev:web

# Buscar textos hardcoded
cd apps/web/src
grep -r "Aguardando\|Pagamento\|Carregando" --include="*.tsx" .

# Ver erros de tipo
cd apps/web
pnpm tsc --noEmit

# Formatar código
pnpm format

# Limpar build
rm -rf apps/web/.next
```

## 📚 ARQUIVOS IMPORTANTES

### Traduções (JSON)
- `apps/web/src/lib/i18n/messages/pt.json` - Português (Base)
- `apps/web/src/lib/i18n/messages/en.json` - Inglês
- `apps/web/src/lib/i18n/messages/es.json` - Espanhol

### Tipos TypeScript
- `apps/web/src/types/i18n.ts` - Definições de tipos para traduções

### Hook de Tradução
- `apps/web/src/hooks/useTranslation.ts` - Hook principal

### Documentação
- `I18N_IMPLEMENTATION_SUMMARY.md` - Resumo completo do que foi feito
- `STATUS_FASE_7_PROGRESSO.md` - Status da fase 7
- `PROMPT_FASE_7_CONTINUACAO.md` - Contexto da fase 7

## 🎯 PADRÕES ESTABELECIDOS

### 1. Uso do Hook de Tradução

```typescript
"use client";

import { useTranslation } from "@/hooks/useTranslation";

export function MeuComponente() {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('checkout.title')}</h1>
      <p>{t('checkout.subtitle')}</p>
    </div>
  );
}
```

### 2. Estrutura de Chaves de Tradução

```
categoria.subcategoria.chave
```

Exemplos:
- `checkout.loading` - Categoria checkout, chave loading
- `payment.pix.step_1` - Categoria payment, subcategoria pix, chave step_1
- `success.pix_auto_configured` - Categoria success, chave pix_auto_configured

### 3. Imports de Tipos

```typescript
// ✅ CORRETO - Tipos usam import type
import type { Payment, ProductData } from "@/types";
import type { SVGProps } from "react";

// ✅ CORRETO - Valores/funções usam import normal
import { useCheckout } from "@/contexts";
import { formatPrice } from "@/lib/utils/format";

// ❌ ERRADO - Misturar tipos e valores
import { Payment, useCheckout } from "@/types";
```

### 4. SSR Safety

```typescript
// ✅ Sempre verificar window/navigator/document
if (typeof window === "undefined") return null;
window.location.href = url;

// ✅ Usar "use client" em componentes com hooks
"use client";

import { useState } from "react";
```

## 🐛 PROBLEMAS CONHECIDOS & SOLUÇÕES

### Problema 1: Build falha com "is a type and must be imported using a type-only import"

**Solução**:
```typescript
// Trocar
import { TipoQualquer } from "biblioteca";

// Por
import type { TipoQualquer } from "biblioteca";
```

### Problema 2: Texto não aparece traduzido

**Causas possíveis**:
1. Chave não existe no JSON de traduções
2. Tipo não foi adicionado em `i18n.ts`
3. Hook `useTranslation()` não foi importado
4. Componente não tem `"use client"`

**Solução**: Verificar todos os pontos acima

### Problema 3: TypeScript reclama de propriedade inexistente no tipo Messages

**Solução**: Adicionar a propriedade em `src/types/i18n.ts`

## 📞 COMO PEDIR AJUDA

Se ficar preso:

1. **Rode o build completo** e copie o erro exato
2. **Leia este prompt inteiro** - a solução pode estar aqui
3. **Verifique os arquivos de documentação**:
   - `I18N_IMPLEMENTATION_SUMMARY.md`
   - `_docs/README.md`
   - `.cursorrules` (na raiz)

## 🎉 CRITÉRIO DE SUCESSO FINAL

O projeto estará **100% completo** quando:

✅ `pnpm build` executa sem erros  
✅ Zero textos hardcoded encontrados em busca  
✅ Todos os 3 idiomas funcionam perfeitamente  
✅ Troca de idioma funciona sem problemas  
✅ Checklist de testes está completo  

---

**BOA SORTE! 🚀**

Você está a apenas 5% de completar uma internacionalização completa e profissional de um sistema de checkout complexo!

**Tempo estimado para completar**: 1-2 horas

**Prioridade das tarefas**:
1. 🔴 Tarefa 1 (Build) - FAZER PRIMEIRO
2. 🟡 Tarefa 2 (Busca) - FAZER DEPOIS
3. 🟢 Tarefa 3 (Testes) - FAZER POR ÚLTIMO
4. 🟢 Tarefa 4 (UX) - OPCIONAL

---

**Última atualização**: 2025-11-11  
**Sessão anterior completada por**: AI Assistant (Claude)  
**Progresso atual**: ~95%

