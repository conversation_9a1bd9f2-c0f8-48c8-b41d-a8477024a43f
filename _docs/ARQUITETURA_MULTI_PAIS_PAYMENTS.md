# Arquitetura Multi-País e Métodos de Pagamento

**Data:** 2025-11-12  
**Status:** ✅ Infraestrutura existente | 🚧 Componentes faltantes  
**Objetivo:** Documentar arquitetura atual e gaps de implementação

---

## 📊 Status Atual da Implementação

### ✅ O que JÁ EXISTE (Infraestrutura)

1. **CountryProvider** - Sistema completo de detecção e seleção de país
2. **COUNTRIES** - Configuração de 7 países (BR, MX, AR, CL, CO, PE, US)
3. **Types** - Tipos para países, moedas, documentos e métodos
4. **CountrySelector** - Componente para mudar país manualmente
5. **i18n** - Sistema de tradução em 3 idiomas (pt, en, es)
6. **PaymentTabs** - Filtragem de métodos por país (checkout-context linha 389-435)

### 🚧 O que FALTA IMPLEMENTAR

#### México (MX) - Prioridade ALTA
- ❌ **OxxoForm** - Formulário de pagamento OXXO
- ❌ **SpeiForm** - Formulário de transferência SPEI
- ❌ **OxxoPayment** - Tela de voucher gerado
- ❌ **SpeiPayment** - Tela de dados da transferência
- ❌ **OxxoIcon** - Ícone do método
- ❌ **SpeiIcon** - Ícone do método
- ❌ **Traduções es-MX** - Textos específicos do México

#### Argentina (AR) - Prioridade MÉDIA
- ❌ **RapipagoForm**
- ❌ **PagofacilForm**
- ❌ Ícones e telas de waiting

#### Chile (CL) - Prioridade MÉDIA
- ❌ **ServipagForm**
- ❌ **MulticajaForm**
- ❌ Ícones e telas de waiting

#### Colômbia (CO) - Prioridade MÉDIA
- ❌ **PseForm**
- ❌ **EfectyForm**
- ❌ **BalotoForm**
- ❌ Ícones e telas de waiting

#### Peru (PE) - Prioridade BAIXA
- ❌ **PagoefectivoForm**
- ❌ **SafetypayForm**
- ❌ Ícones e telas de waiting

---

## 🏗️ Arquitetura Atual (Análise Detalhada)

### 1. Fluxo de Dados Completo

```
┌────────────────────────────────────────────────────────┐
│ 1. User Access                                         │
│    URL: /es/mcrd948                                    │
└────────────────────────────────────────────────────────┘
                         ↓
┌────────────────────────────────────────────────────────┐
│ 2. Server Component (Page)                             │
│    - Extrai locale da URL                              │
│    - Detecta país (via headers ou default)             │
│    - Busca dados: getCheckoutData(id, affiliate)       │
└────────────────────────────────────────────────────────┘
                         ↓
┌────────────────────────────────────────────────────────┐
│ 3. API Response                                        │
│    {                                                   │
│      product: {                                        │
│        paymentMethods: [                               │
│          {type: "oxxo", name: "OXXO"},                │
│          {type: "spei", name: "SPEI"},                │
│          {type: "credit_card", name: "Tarjeta"}       │
│        ]                                               │
│      }                                                 │
│    }                                                   │
└────────────────────────────────────────────────────────┘
                         ↓
┌────────────────────────────────────────────────────────┐
│ 4. Client Components Tree                              │
│    <CheckoutClient>                                    │
│      <CountryProvider> ← Detecta/armazena país        │
│        <CheckoutProvider> ← Filtra métodos por país   │
│          <CheckoutForm>                                │
│            <CheckoutComponent>                         │
│              <PaymentMethods> ← Renderiza tabs        │
│                <OxxoForm /> ← Formulário específico   │
└────────────────────────────────────────────────────────┘
```

### 2. CheckoutProvider - paymentTabs (Linhas 389-435)

**Como funciona a filtragem atual:**

```typescript
const paymentTabs = useMemo(() => {
  const paymentMethods = offer?.product?.paymentMethods || [];

  // 1. Filtra métodos disponíveis no país selecionado
  const availableInCountry = paymentMethods.filter(({ type }) => 
    country.paymentMethods.includes(type)
  );

  // 2. Para assinaturas, usa métodos do produto
  let allowedPaymentsMethods: PaymentMethod[] =
    offer?.type === "subscription"
      ? availableInCountry?.map(({ type }) => type)
      : availableInCountry?.map(({ type }) => type);

  // 3. Remove pix_auto de produtos únicos
  if (offer?.type === "unique") {
    allowedPaymentsMethods = allowedPaymentsMethods.filter(
      (methodType) => methodType !== "pix_auto",
    );
  }

  // 4. Ordena conforme paymentsOrder
  const paymentsOrder = offer?.product?.paymentsOrder || allowedPaymentsMethods;

  // 5. Mapeia para tabs com ícone e componente
  return availableInCountry
    .filter(({ type }) => 
      allowedPaymentsMethods.includes(type) && type !== "threeDs"
    )
    .sort((a, b) => 
      paymentsOrder.indexOf(a.type) - paymentsOrder.indexOf(b.type)
    )
    .map((item) => ({
      id: item.type,
      label: item.name,
      Icon: mapIcons[item.type] || CreditCardIcon,
      Component: mapForms[item.type] || NotFoundComponent,
    }));
}, [offer, country]);
```

**Diagrama do Fluxo:**

```
API Response Methods     Country Config         Result
─────────────────────────────────────────────────────────
["oxxo", "spei",    ∩   ["oxxo", "spei",   =   ["oxxo", "spei",
 "credit_card"]          "credit_card"]         "credit_card"]
                              ↓
                    Aplica paymentsOrder
                              ↓
                    ["oxxo", "spei", "credit_card"]
                              ↓
                    Mapeia Icon + Component
                              ↓
                    [{
                      id: "oxxo",
                      label: "OXXO",
                      Icon: OxxoIcon,
                      Component: OxxoForm
                    }, ...]
```

---

## 📋 Análise de Gaps por País

### 🇲🇽 México (MX) - **PRIORIDADE 1**

**Configuração:** ✅ Completa em `constants/countries.ts`

```typescript
MX: {
  code: "MX",
  name: "México",
  currency: "MXN",
  paymentMethods: ["credit_card", "spei", "oxxo"],
  documentLabel: "CURP/RFC",
  phoneFormat: "+52 99 9999 9999",
}
```

**Componentes Faltantes:**

| Método | Form | Icon | WaitingPayment | i18n | Prioridade |
|--------|------|------|----------------|------|------------|
| OXXO | ❌ | ❌ | ❌ | ❌ | CRÍTICO |
| SPEI | ❌ | ❌ | ❌ | ❌ | CRÍTICO |
| credit_card | ✅ | ✅ | ✅ | ✅ | ✅ OK |

**Estimativa:** 8-12 horas

---

### 🇦🇷 Argentina (AR) - **PRIORIDADE 2**

**Configuração:** ✅ Completa

**Componentes Faltantes:**

| Método | Form | Icon | WaitingPayment | i18n | Prioridade |
|--------|------|------|----------------|------|------------|
| Rapipago | ❌ | ❌ | ❌ | ❌ | ALTO |
| Pagofacil | ❌ | ❌ | ❌ | ❌ | ALTO |
| credit_card | ✅ | ✅ | ✅ | ✅ | ✅ OK |

**Estimativa:** 6-8 horas

---

### 🇨🇱 Chile (CL) - **PRIORIDADE 3**

**Componentes Faltantes:**

| Método | Form | Icon | WaitingPayment | i18n | Prioridade |
|--------|------|------|----------------|------|------------|
| Servipag | ❌ | ❌ | ❌ | ❌ | MÉDIO |
| Multicaja | ❌ | ❌ | ❌ | ❌ | MÉDIO |

**Estimativa:** 6-8 horas

---

### 🇨🇴 Colômbia (CO) - **PRIORIDADE 3**

**Componentes Faltantes:**

| Método | Form | Icon | WaitingPayment | i18n | Prioridade |
|--------|------|------|----------------|------|------------|
| PSE | ❌ | ❌ | ❌ | ❌ | ALTO |
| Efecty | ❌ | ❌ | ❌ | ❌ | MÉDIO |
| Baloto | ❌ | ❌ | ❌ | ❌ | MÉDIO |

**Estimativa:** 8-10 horas

---

### 🇵🇪 Peru (PE) - **PRIORIDADE 4**

**Componentes Faltantes:**

| Método | Form | Icon | WaitingPayment | i18n | Prioridade |
|--------|------|------|----------------|------|------------|
| PagoEfectivo | ❌ | ❌ | ❌ | ❌ | BAIXO |
| SafetyPay | ❌ | ❌ | ❌ | ❌ | BAIXO |

**Estimativa:** 6-8 horas

---

## 🎯 Plano de Implementação Priorizado

### Sprint 1: México (OXXO + SPEI) - **8-12h**

**Objetivo:** Suportar 100% dos pagamentos do México

1. **Componentes Base (4h)**
   - [ ] Criar `OxxoForm.tsx`
   - [ ] Criar `SpeiForm.tsx`
   - [ ] Criar `OxxoIcon.tsx`
   - [ ] Criar `SpeiIcon.tsx`

2. **Waiting Payments (3h)**
   - [ ] Criar `OxxoPayment.tsx`
   - [ ] Criar `SpeiPayment.tsx`
   - [ ] Atualizar `WaitingPayment.tsx`

3. **Integração (2h)**
   - [ ] Adicionar no `mapIcons`
   - [ ] Adicionar no `mapForms`
   - [ ] Atualizar types

4. **i18n (2h)**
   - [ ] Criar `es-MX.json` (ou expandir `es.json`)
   - [ ] Adicionar todas as chaves de OXXO
   - [ ] Adicionar todas as chaves de SPEI

5. **Testes (1h)**
   - [ ] Testar com país MX selecionado
   - [ ] Testar fluxo OXXO
   - [ ] Testar fluxo SPEI

---

### Sprint 2: Argentina (Rapipago + Pagofacil) - **6-8h**

Similar ao México, adaptando para métodos argentinos

---

### Sprint 3: Colômbia (PSE + Efecty + Baloto) - **8-10h**

PSE é mais complexo (banco selection), precisa de mais tempo

---

### Sprint 4: Chile + Peru - **12-16h**

Países de menor volume, podem ser feitos juntos

---

## 🔍 Análise da API Response Atual

### Estrutura Real da API

```json
{
  "product": {
    "paymentMethods": [
      {"type": "applepay", "name": "Apple Pay"},
      {"type": "boleto", "name": "Boleto"},
      {"type": "credit_card", "name": "Cartão de Crédito"},
      {"type": "googlepay", "name": "Google Pay"},
      {"type": "pix", "name": "PIX"}
    ],
    "paymentsOrder": ["pix", "boleto", "credit_card", "picpay"]
  }
}
```

### O que a API DEVE retornar para MX

```json
{
  "product": {
    "paymentMethods": [
      {"type": "oxxo", "name": "OXXO"},
      {"type": "spei", "name": "SPEI"},
      {"type": "credit_card", "name": "Tarjeta de Crédito"}
    ],
    "paymentsOrder": ["oxxo", "spei", "credit_card"]
  }
}
```

### Mudanças Necessárias no Backend

**Endpoint atual:**
```
GET /api/product/checkout/{offer_id}/
```

**Endpoint desejado:**
```
GET /api/product/checkout/{offer_id}/?country={country_code}
```

**Lógica no backend (FastAPI):**

```python
@router.get("/api/product/checkout/{offer_id}/")
async def get_checkout(
    offer_id: str,
    country: str = Query(default="BR"),  # Novo parâmetro
    checkoutUrl: str = Query(default=None),
    # ... outros params
):
    # Buscar offer
    offer = await get_offer(offer_id)
    
    # Filtrar métodos de pagamento por país
    payment_methods = filter_payment_methods_by_country(
        offer.payment_methods,
        country
    )
    
    # Retornar com métodos filtrados
    return {
        "product": {
            "paymentMethods": payment_methods,
            # ... resto dos dados
        }
    }
```

**Função helper:**

```python
def filter_payment_methods_by_country(
    available_methods: list,
    country_code: str
) -> list:
    """
    Filtra métodos de pagamento baseado no país
    """
    COUNTRY_PAYMENT_METHODS = {
        "BR": ["pix", "pix_auto", "boleto", "credit_card", "picpay", 
               "googlepay", "applepay", "openfinance_nubank"],
        "MX": ["oxxo", "spei", "credit_card", "googlepay", "applepay"],
        "AR": ["rapipago", "pagofacil", "credit_card", "googlepay", "applepay"],
        "CL": ["servipag", "multicaja", "credit_card", "googlepay", "applepay"],
        "CO": ["pse", "efecty", "baloto", "credit_card", "googlepay", "applepay"],
        "PE": ["pagoefectivo", "safetypay", "credit_card", "googlepay", "applepay"],
        "US": ["credit_card", "googlepay", "applepay"],
    }
    
    allowed = COUNTRY_PAYMENT_METHODS.get(country_code, ["credit_card"])
    
    return [
        method for method in available_methods
        if method["type"] in allowed
    ]
```

---

## 🔄 Fluxo de Orquestração Completo

### Cenário: Usuário do México acessa checkout

```
1. User Access
   URL: https://pay.cakto.com.br/es/mcrd948
   
2. Next.js Page (Server Side)
   - Extrai locale: "es"
   - Detecta país via IP ou locale → "MX"
   - NÃO passa país para API ainda (API decide sozinha)
   
3. API Call
   GET /api/product/checkout/mcrd948/
   
4. API Response
   {
     "product": {
       "paymentMethods": [
         {"type": "applepay", "name": "Apple Pay"},
         {"type": "boleto", "name": "Boleto"},  ← Não deveria vir
         {"type": "credit_card", "name": "Cartão de Crédito"},
         {"type": "pix", "name": "PIX"}  ← Não deveria vir
       ]
     }
   }
   
5. CountryProvider (Client Side)
   - Detecta país: "MX" (via /api/geoip ou localStorage)
   - Carrega config: COUNTRIES.MX
   - paymentMethods: ["credit_card", "spei", "oxxo"]
   
6. CheckoutProvider - paymentTabs (FILTRO DUPLO)
   const availableInCountry = paymentMethods.filter(({ type }) => 
     country.paymentMethods.includes(type)  ← FILTRO DO FRONTEND
   );
   
   Resultado:
   API tem: ["applepay", "boleto", "credit_card", "pix"]
   Country permite: ["credit_card", "spei", "oxxo"]
   Intersecção: ["credit_card"]  ← Só sobra 1 método!
   
7. PaymentMethods Renderiza
   Só mostra: [Tarjeta de Crédito]
   OXXO e SPEI não aparecem! ❌
```

### ⚠️ PROBLEMA IDENTIFICADO

**A API não está retornando os métodos corretos para o México!**

A API retorna métodos do Brasil (pix, boleto) mesmo quando o usuário é do México.

---

## 🔧 Solução: 2 Abordagens Possíveis

### Opção A: Backend detecta país (RECOMENDADO)

**Vantagens:**
- Dados corretos desde o início
- Menos lógica no frontend
- Melhor SEO e performance

**Implementação:**

1. **Backend detecta país via headers:**
   ```python
   country = request.headers.get("CF-IPCountry") or "BR"
   ```

2. **Backend filtra métodos:**
   ```python
   payment_methods = filter_by_country(offer.methods, country)
   ```

3. **Frontend só consome:**
   ```typescript
   // Não precisa filtrar, já vem filtrado
   const tabs = paymentMethods.map(...)
   ```

### Opção B: Frontend passa país no request

**Vantagens:**
- Controle explícito
- Permite override manual

**Implementação:**

1. **Frontend detecta país:**
   ```typescript
   const country = await detectUserCountry();
   ```

2. **Frontend passa na URL:**
   ```typescript
   const data = await getCheckoutData(id, affiliate, country);
   // GET /api/checkout/{id}/?country=MX
   ```

3. **Backend usa o parâmetro:**
   ```python
   country = request.query_params.get("country", "BR")
   ```

---

## ✅ Solução Recomendada: HÍBRIDA

**Combinar ambas abordagens para máxima flexibilidade:**

### 1. Backend tenta detectar automaticamente

```python
@router.get("/api/product/checkout/{offer_id}/")
async def get_checkout(
    request: Request,
    offer_id: str,
    country: str = Query(default=None),  # Opcional
):
    # 1. Usar parâmetro se fornecido (prioridade)
    if country:
        detected_country = country
    # 2. Tentar headers
    elif cf_country := request.headers.get("CF-IPCountry"):
        detected_country = cf_country
    # 3. Fallback
    else:
        detected_country = "BR"
    
    # Filtrar métodos
    payment_methods = filter_payment_methods_by_country(
        offer.payment_methods,
        detected_country
    )
    
    return {
        "detectedCountry": detected_country,  # Retornar país detectado
        "product": {
            "paymentMethods": payment_methods,
            # ...
        }
    }
```

### 2. Frontend usa país detectado pelo backend

```typescript
// apps/web/src/app/[locale]/[id]/page.tsx
export default async function CheckoutPage({ params }) {
  const { locale, id } = await params;
  
  // NÃO passa país - deixa backend detectar
  const checkoutData = await getCheckoutData(id, affiliate);
  
  // Backend retorna país detectado
  const detectedCountry = checkoutData.detectedCountry;
  
  return (
    <CheckoutClient 
      initialData={checkoutData}
      checkoutId={id}
      initialCountry={detectedCountry}  // Passa para cliente
    />
  );
}
```

### 3. CountryProvider usa país inicial

```typescript
// apps/web/src/contexts/country-context.tsx
export function CountryProvider({ 
  children, 
  initialCountry  // Novo prop
}: { 
  children: ReactNode;
  initialCountry?: CountryCode;
}) {
  const [userCountryData, setUserCountryData] = useState<UserCountryData>(() => {
    // 1. Usar initialCountry do servidor (SSR)
    if (initialCountry && COUNTRIES[initialCountry]) {
      return {
        countryCode: initialCountry,
        isManualSelection: false,
      };
    }
    
    // 2. Tentar localStorage
    // 3. Fallback
    // ...
  });
}
```

---

## 📦 Estrutura de Arquivos Proposta

```
apps/web/src/
├── components/
│   ├── icons/
│   │   └── payment/
│   │       ├── OxxoIcon.tsx         ← CRIAR
│   │       ├── SpeiIcon.tsx         ← CRIAR
│   │       ├── RapipagoIcon.tsx     ← CRIAR
│   │       ├── PagofacilIcon.tsx    ← CRIAR
│   │       ├── PseIcon.tsx          ← CRIAR
│   │       └── index.ts             ← ATUALIZAR
│   │
│   └── payments/
│       ├── forms/
│       │   ├── OxxoForm.tsx         ← CRIAR
│       │   ├── SpeiForm.tsx         ← CRIAR
│       │   ├── RapipagoForm.tsx     ← CRIAR
│       │   └── ...
│       │
│       ├── waiting/
│       │   ├── OxxoPayment.tsx      ← CRIAR
│       │   ├── SpeiPayment.tsx      ← CRIAR
│       │   ├── RapipagoPayment.tsx  ← CRIAR
│       │   └── ...
│       │
│       └── WaitingPayment.tsx       ← ATUALIZAR
│
├── lib/
│   ├── i18n/
│   │   └── messages/
│   │       ├── es-MX.json           ← CRIAR (ou expandir es.json)
│   │       ├── es-AR.json           ← CRIAR
│   │       └── ...
│   │
│   └── api/
│       └── checkout.ts              ← ATUALIZAR (passar país)
│
└── types/
    ├── index.ts                     ← ATUALIZAR
    └── payment.ts                   ← CRIAR (separar tipos)
```

---

## 🧪 Estratégia de Testes

### 1. Testes Unitários
```typescript
describe("PaymentTabs Filtering", () => {
  it("shows only MX methods when country is MX", () => {
    const methods = filterPaymentMethods(
      apiMethods,
      COUNTRIES.MX
    );
    
    expect(methods).toContain("oxxo");
    expect(methods).toContain("spei");
    expect(methods).not.toContain("pix");
  });
});
```

### 2. Testes de Integração
```bash
# Simular usuário do México
curl "http://localhost:3001/api/product/checkout/mcrd948/?country=MX"

# Verificar resposta
{
  "detectedCountry": "MX",
  "product": {
    "paymentMethods": [
      {"type": "oxxo", "name": "OXXO"},
      {"type": "spei", "name": "SPEI"}
    ]
  }
}
```

### 3. Testes E2E
```typescript
// Cypress
describe("Mexico Checkout", () => {
  it("shows OXXO and SPEI for MX users", () => {
    cy.visit("/es/mcrd948");
    cy.contains("OXXO").should("be.visible");
    cy.contains("SPEI").should("be.visible");
    cy.contains("PIX").should("not.exist");
  });
});
```

---

## 📝 Próximos Passos Imediatos

### 1. Validar com Backend
- [ ] Confirmar se API consegue detectar país via headers
- [ ] Confirmar se API pode filtrar métodos por país
- [ ] Confirmar estrutura de response para OXXO/SPEI

### 2. Decidir Abordagem
- [ ] Escolher: Backend detecta OU Frontend passa
- [ ] Documentar decisão
- [ ] Atualizar contratos de API

### 3. Criar Componentes do México
- [ ] Seguir documento `PROMPT_MEXICO_PAYMENT_METHODS.md`
- [ ] Começar por OXXO (mais comum)
- [ ] Depois SPEI
- [ ] Testar com mock data primeiro

---

## 🎯 Métricas de Sucesso

- ✅ Usuário do México vê apenas métodos do México
- ✅ Usuário do Brasil vê apenas métodos do Brasil
- ✅ Mudança manual de país atualiza métodos imediatamente
- ✅ Traduções corretas por país
- ✅ Moedas formatadas corretamente (R$ vs $)
- ✅ Documentos corretos (CPF vs RFC)
- ✅ Máscaras de telefone corretas por país

---

**Conclusão:** A infraestrutura está 80% pronta. Falta apenas criar os componentes específicos de cada método de pagamento e garantir que a API retorna os métodos corretos por país.

