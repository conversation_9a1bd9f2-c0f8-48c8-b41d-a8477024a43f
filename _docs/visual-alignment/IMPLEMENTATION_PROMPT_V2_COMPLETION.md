# Prompt de Implementação: Completar Checkout V2 com Base na V1

**Baseado em:** `V1_VS_V2_GAP_ANALYSIS.md`  
**Objetivo:** Adicionar todos os elementos faltantes da V1 na V2  
**Prioridade:** Elementos críticos primeiro, depois importantes, depois opcionais

---

## 🎯 Contexto

Estamos migrando o checkout da Cakto da v1 (React + Vite) para v2 (Next.js + Tailwind 4.x). A v2 já possui o sistema de cores da marca implementado, mas faltam diversos componentes e elementos presentes na v1 que são essenciais para:

1. **Conversão:** Order bumps aumentam ticket médio
2. **Confiança:** Informações de segurança e vendedor
3. **UX:** Ícones, tooltips e informações contextuais
4. **Transparência:** Informações completas do produto

**Sistema de Cores:** ✅ Já implementado (use classes como `bg-brand-primary`, `text-brand-primary`, etc.)

---

## 📦 Fase 1: Product Info Card (CRÍTICO)

### Objetivo
Adicionar card com informações do produto no topo do checkout, exibindo logo, nome, preços e opções de pagamento.

### Localização
**Arquivo:** `/apps/web/src/components/checkout/product-info-card.tsx` (CRIAR NOVO)

### Design Specs

```
┌─────────────────────────────────────────────────┐
│ [Logo]  Nome do Produto                         │
│         De R$ 35,90                             │
│         7 X de R$ 5,07                          │
│         ou R$ 29,90 à vista                     │
└─────────────────────────────────────────────────┘
```

### Implementação

```typescript
"use client";

import Image from "next/image";
import { formatPrice } from "@/lib/utils/format";
import { useCheckout } from "@/contexts";

export function ProductInfoCard() {
  const { offer } = useCheckout();
  
  if (!offer) return null;

  const product = offer.product;
  const hasInstallments = offer.installments && offer.installments.length > 0;
  const bestInstallment = hasInstallments 
    ? offer.installments[offer.installments.length - 1] 
    : null;

  return (
    <div className="flex items-start gap-4 p-4 bg-white rounded-lg border border-gray-200 mb-6">
      {/* Logo do Produto */}
      {product.image && (
        <div className="flex-shrink-0">
          <Image
            src={product.image}
            alt={product.name}
            width={64}
            height={64}
            className="rounded-full object-cover"
          />
        </div>
      )}

      {/* Informações do Produto */}
      <div className="flex-1">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {product.name}
        </h3>

        <div className="space-y-1">
          {/* Preço Original (riscado) */}
          {offer.originalPrice && offer.originalPrice > offer.price && (
            <p className="text-sm text-gray-500 line-through">
              De {formatPrice(offer.originalPrice)}
            </p>
          )}

          {/* Preço Parcelado */}
          {bestInstallment && (
            <p className="text-base font-semibold text-gray-900">
              {bestInstallment.quantity}x de {formatPrice(bestInstallment.value)}
            </p>
          )}

          {/* Preço à Vista */}
          <p className="text-sm text-gray-700">
            ou {formatPrice(offer.price)} à vista
          </p>
        </div>
      </div>
    </div>
  );
}
```

### Integração

**Arquivo:** `/apps/web/src/app/[locale]/[id]/page.tsx`

```typescript
import { ProductInfoCard } from "@/components/checkout/product-info-card";

// Adicionar antes da seção de "Informações de Contato"
<ProductInfoCard />
```

### Validação

- [ ] Logo do produto é exibido (se disponível)
- [ ] Nome do produto é exibido
- [ ] Preço original riscado (quando houver desconto)
- [ ] Preço parcelado (quando disponível)
- [ ] Preço à vista
- [ ] Responsive em mobile

---

## 📦 Fase 2: Order Bumps Section (CRÍTICO)

### Objetivo
Adicionar seção de ofertas limitadas (order bumps) para aumentar ticket médio através de upsells.

### Verificação Prévia
**ANTES DE IMPLEMENTAR:** Verificar se a API retorna order bumps em `offer.orderBumps` ou similar.

### Localização
**Arquivos:**
- `/apps/web/src/components/checkout/order-bumps.tsx` (CRIAR NOVO)
- `/apps/web/src/components/checkout/order-bump-item.tsx` (CRIAR NOVO)

### Design Specs

```
┌─────────────────────────────────────────────────┐
│ Ofertas limitadas                               │
│                                                 │
│ ┌───────────────────────────────────────────┐   │
│ │ ☐ [Imagem] Multiplique suas vendas        │   │
│ │            Descrição curta                │   │
│ │            R$ 10,00                       │   │
│ └───────────────────────────────────────────┘   │
│                                                 │
│ ┌───────────────────────────────────────────┐   │
│ │ ☐ [Imagem] Apostila +100 receitas         │   │
│ │            caseiras                       │   │
│ │            R$ 7,50                        │   │
│ └───────────────────────────────────────────┘   │
└─────────────────────────────────────────────────┘
```

### Implementação - OrderBumpItem

```typescript
"use client";

import { useState } from "react";
import Image from "next/image";
import Checkbox from "@/components/ui/checkbox-form";
import { formatPrice } from "@/lib/utils/format";

interface OrderBumpItemProps {
  id: string;
  name: string;
  description?: string;
  price: number;
  image?: string;
  selected: boolean;
  onToggle: (id: string, selected: boolean) => void;
}

export function OrderBumpItem({
  id,
  name,
  description,
  price,
  image,
  selected,
  onToggle,
}: OrderBumpItemProps) {
  return (
    <div
      className={`
        relative p-4 rounded-lg border-2 transition-all cursor-pointer
        ${
          selected
            ? "border-brand-primary bg-brand-primary-lighter"
            : "border-gray-200 bg-white hover:border-brand-primary hover:border-opacity-40"
        }
      `}
      onClick={() => onToggle(id, !selected)}
    >
      <div className="flex items-start gap-3">
        {/* Checkbox */}
        <div className="flex-shrink-0 pt-1">
          <Checkbox
            name={`order-bump-${id}`}
            checked={selected}
            onChange={() => onToggle(id, !selected)}
            className="cursor-pointer"
          />
        </div>

        {/* Imagem (se disponível) */}
        {image && (
          <div className="flex-shrink-0">
            <Image
              src={image}
              alt={name}
              width={64}
              height={64}
              className="rounded object-cover"
            />
          </div>
        )}

        {/* Conteúdo */}
        <div className="flex-1 min-w-0">
          <h4 className="text-base font-semibold text-gray-900 mb-1">
            {name}
          </h4>
          {description && (
            <p className="text-sm text-gray-600 mb-2 line-clamp-2">
              {description}
            </p>
          )}
          <p className="text-lg font-bold text-brand-primary">
            + {formatPrice(price)}
          </p>
        </div>
      </div>
    </div>
  );
}
```

### Implementação - OrderBumps Container

```typescript
"use client";

import { useState, useEffect } from "react";
import { OrderBumpItem } from "./order-bump-item";
import { useCheckout } from "@/contexts";

interface OrderBump {
  id: string;
  name: string;
  description?: string;
  price: number;
  image?: string;
}

export function OrderBumps() {
  const { offer, updateOrderBumps } = useCheckout();
  const [selectedBumps, setSelectedBumps] = useState<Set<string>>(new Set());

  // NOTA: Verificar estrutura real da API
  const orderBumps: OrderBump[] = offer?.orderBumps || [];

  if (!orderBumps || orderBumps.length === 0) {
    return null;
  }

  const handleToggle = (id: string, selected: boolean) => {
    setSelectedBumps((prev) => {
      const next = new Set(prev);
      if (selected) {
        next.add(id);
      } else {
        next.delete(id);
      }
      return next;
    });
  };

  // Atualizar contexto quando seleção mudar
  useEffect(() => {
    const selected = orderBumps.filter((bump) =>
      selectedBumps.has(bump.id)
    );
    updateOrderBumps?.(selected);
  }, [selectedBumps, orderBumps, updateOrderBumps]);

  return (
    <section className="mb-6">
      <h2 className="text-xl font-bold text-gray-900 mb-4">
        Ofertas limitadas
      </h2>

      <div className="space-y-3">
        {orderBumps.map((bump) => (
          <OrderBumpItem
            key={bump.id}
            {...bump}
            selected={selectedBumps.has(bump.id)}
            onToggle={handleToggle}
          />
        ))}
      </div>
    </section>
  );
}
```

### Atualizar Context

**Arquivo:** `/apps/web/src/contexts/checkout-context.tsx`

Adicionar ao contexto:

```typescript
interface CheckoutContextData {
  // ... existente
  selectedOrderBumps: OrderBump[];
  updateOrderBumps: (bumps: OrderBump[]) => void;
}

// No provider:
const [selectedOrderBumps, setSelectedOrderBumps] = useState<OrderBump[]>([]);

const updateOrderBumps = useCallback((bumps: OrderBump[]) => {
  setSelectedOrderBumps(bumps);
}, []);
```

### Atualizar Order Summary

**Arquivo:** Componente de resumo do pedido

Adicionar order bumps selecionados ao cálculo do total:

```typescript
const orderBumpsTotal = selectedOrderBumps.reduce(
  (sum, bump) => sum + bump.price,
  0
);

const subtotal = offerPrice + orderBumpsTotal;
const total = subtotal + serviceFee;
```

### Integração

**Arquivo:** `/apps/web/src/app/[locale]/[id]/page.tsx`

```typescript
import { OrderBumps } from "@/components/checkout/order-bumps";

// Adicionar entre seção de pagamento e resumo do pedido
<OrderBumps />
```

### Validação

- [ ] Order bumps são listados corretamente
- [ ] Checkbox funciona
- [ ] Seleção atualiza o total
- [ ] Visual muda quando selecionado
- [ ] Imagens são exibidas (quando disponível)
- [ ] Responsive em mobile
- [ ] Dados são enviados no submit

---

## 📦 Fase 3: Enhanced Order Summary (IMPORTANTE)

### Objetivo
Melhorar o resumo do pedido para incluir order bumps, subtotal e opções de parcelamento.

### Localização
**Arquivo:** `/apps/web/src/components/checkout/order-summary.tsx` (atualizar existente)

### Design Specs

```
┌─────────────────────────────────────────────────┐
│ Resumo do pedido                                │
│                                                 │
│ [Logo] Curso de confeitaria profissional       │
│        R$ 29,90                                 │
│                                                 │
│ [Logo] Multiplique suas vendas                 │
│        R$ 10,00                                 │
│                                                 │
│ ────────────────────────────────────────────    │
│ Subtotal                             R$ 39,90  │
│ Taxa de serviço                      R$ 0,99   │
│ ────────────────────────────────────────────    │
│ Total                                           │
│ 7x de R$ 5,84 ou R$ 40,89 à vista              │
└─────────────────────────────────────────────────┘
```

### Implementação

```typescript
"use client";

import Image from "next/image";
import { formatPrice } from "@/lib/utils/format";
import { useCheckout } from "@/contexts";
import { useMemo } from "react";

export function OrderSummary() {
  const { offer, selectedOrderBumps, paymentMethod } = useCheckout();

  if (!offer) return null;

  const calculations = useMemo(() => {
    const offerPrice = offer.price;
    const bumpsTotal = selectedOrderBumps.reduce(
      (sum, bump) => sum + bump.price,
      0
    );
    const subtotal = offerPrice + bumpsTotal;
    const serviceFee = offer.serviceFee || 0;
    const total = subtotal + serviceFee;

    // Calcular parcelamento
    const installments = offer.installments?.length
      ? offer.installments[offer.installments.length - 1]
      : null;

    const installmentValue = installments
      ? (total / installments.quantity)
      : null;

    return {
      offerPrice,
      bumpsTotal,
      subtotal,
      serviceFee,
      total,
      installments: installments?.quantity,
      installmentValue,
    };
  }, [offer, selectedOrderBumps]);

  const showInstallments = paymentMethod === "credit_card";

  return (
    <section className="bg-white p-5 rounded-lg border border-gray-200">
      <h2 className="text-xl font-bold text-gray-900 mb-4">
        Resumo do pedido
      </h2>

      <div className="space-y-4">
        {/* Produto Principal */}
        <div className="flex items-start gap-3">
          {offer.product.image && (
            <Image
              src={offer.product.image}
              alt={offer.product.name}
              width={48}
              height={48}
              className="rounded object-cover flex-shrink-0"
            />
          )}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 line-clamp-2">
              {offer.product.name}
            </p>
            <p className="text-sm text-gray-600 mt-1">
              {formatPrice(calculations.offerPrice)}
            </p>
          </div>
        </div>

        {/* Order Bumps Selecionados */}
        {selectedOrderBumps.map((bump) => (
          <div key={bump.id} className="flex items-start gap-3">
            {bump.image && (
              <Image
                src={bump.image}
                alt={bump.name}
                width={48}
                height={48}
                className="rounded object-cover flex-shrink-0"
              />
            )}
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 line-clamp-2">
                {bump.name}
              </p>
              <p className="text-sm text-gray-600 mt-1">
                {formatPrice(bump.price)}
              </p>
            </div>
          </div>
        ))}

        {/* Divider */}
        <div className="border-t border-gray-200 my-4" />

        {/* Subtotal (se houver order bumps) */}
        {selectedOrderBumps.length > 0 && (
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Subtotal</span>
            <span className="text-sm font-medium text-gray-900">
              {formatPrice(calculations.subtotal)}
            </span>
          </div>
        )}

        {/* Taxa de Serviço */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Taxa de serviço</span>
          <span className="text-sm font-medium text-gray-900">
            {formatPrice(calculations.serviceFee)}
          </span>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-200 my-4" />

        {/* Total */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-lg font-bold text-gray-900">Total</span>
          </div>

          {showInstallments && calculations.installments ? (
            <div className="space-y-1">
              <p className="text-base font-semibold text-gray-900">
                {calculations.installments}x de{" "}
                {formatPrice(calculations.installmentValue!)}
              </p>
              <p className="text-sm text-gray-600">
                ou {formatPrice(calculations.total)} à vista
              </p>
            </div>
          ) : (
            <p className="text-xl font-bold text-gray-900">
              {formatPrice(calculations.total)}
            </p>
          )}
        </div>
      </div>
    </section>
  );
}
```

### Validação

- [ ] Produto principal é exibido
- [ ] Order bumps selecionados aparecem na lista
- [ ] Subtotal é calculado corretamente
- [ ] Taxa de serviço é adicionada
- [ ] Total final está correto
- [ ] Parcelamento exibido quando cartão selecionado
- [ ] Imagens são exibidas

---

## 📦 Fase 4: Form Field Icons (IMPORTANTE)

### Objetivo
Adicionar ícones visuais nos campos de formulário para melhorar UX e guidance visual.

### Localização
**Arquivo:** `/apps/web/src/components/ui/text-field.tsx` (atualizar existente)

### Implementação

```typescript
import { UserIcon, EnvelopeIcon, DocumentTextIcon, PhoneIcon } from "@heroicons/react/24/outline";

// Atualizar interface Props
interface Props {
  // ... existente
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
}

// No componente
const TextField = ({ icon, iconPosition = "left", ...props }: Props) => {
  return (
    <div className="relative">
      {icon && iconPosition === "left" && (
        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
          {icon}
        </div>
      )}

      <input
        className={`
          ${icon && iconPosition === "left" ? "pl-10" : ""}
          ${icon && iconPosition === "right" ? "pr-10" : ""}
          /* ... resto das classes */
        `}
        {...props}
      />

      {icon && iconPosition === "right" && (
        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">
          {icon}
        </div>
      )}
    </div>
  );
};
```

### Integração nos Formulários

**Arquivo:** Componente de formulário de checkout

```typescript
import { UserIcon, EnvelopeIcon, DocumentTextIcon, PhoneIcon } from "@heroicons/react/24/outline";

<TextField
  name="name"
  label="Nome completo"
  icon={<UserIcon className="w-5 h-5" />}
/>

<TextField
  name="email"
  label="E-mail"
  icon={<EnvelopeIcon className="w-5 h-5" />}
/>

<TextField
  name="document"
  label="CPF/CNPJ"
  icon={<DocumentTextIcon className="w-5 h-5" />}
/>

<TextField
  name="phone"
  label="Celular"
  icon={<PhoneIcon className="w-5 h-5" />}
/>
```

### Validação

- [ ] Ícones aparecem nos campos
- [ ] Posicionamento está correto
- [ ] Não interfere no placeholder ou texto
- [ ] Acessibilidade mantida
- [ ] Responsive funciona

---

## 📦 Fase 5: Data Privacy Tooltip (IMPORTANTE)

### Objetivo
Adicionar tooltip explicativo "Porque pedimos esse dado?" para aumentar transparência e confiança.

### Localização
**Arquivo:** `/apps/web/src/components/ui/data-privacy-tooltip.tsx` (CRIAR NOVO)

### Implementação

```typescript
"use client";

import { useState } from "react";
import { QuestionMarkCircleIcon } from "@heroicons/react/24/outline";
import * as Popover from "@radix-ui/react-popover";

interface DataPrivacyTooltipProps {
  field: "phone" | "document" | "email";
}

const explanations = {
  phone: {
    title: "Por que pedimos seu telefone?",
    content:
      "Seu telefone é usado para enviar notificações importantes sobre seu pedido, como confirmação de pagamento e acesso ao produto. Nunca compartilharemos seus dados com terceiros.",
  },
  document: {
    title: "Por que pedimos seu CPF/CNPJ?",
    content:
      "O CPF/CNPJ é necessário para emissão da nota fiscal e para garantir a segurança da sua compra. Seus dados são criptografados e protegidos.",
  },
  email: {
    title: "Por que pedimos seu e-mail?",
    content:
      "Seu e-mail será usado para enviar o acesso ao produto adquirido e informações importantes sobre seu pedido.",
  },
};

export function DataPrivacyTooltip({ field }: DataPrivacyTooltipProps) {
  const [open, setOpen] = useState(false);
  const explanation = explanations[field];

  return (
    <Popover.Root open={open} onOpenChange={setOpen}>
      <Popover.Trigger asChild>
        <button
          type="button"
          className="inline-flex items-center gap-1 text-sm text-brand-primary hover:text-brand-primary-hover transition-colors"
        >
          <QuestionMarkCircleIcon className="w-4 h-4" />
          <span>Porque pedimos esse dado?</span>
        </button>
      </Popover.Trigger>

      <Popover.Portal>
        <Popover.Content
          className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 w-80 z-50"
          sideOffset={5}
        >
          <h4 className="font-semibold text-gray-900 mb-2">
            {explanation.title}
          </h4>
          <p className="text-sm text-gray-600">{explanation.content}</p>

          <Popover.Arrow className="fill-white" />
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
}
```

### Instalação de Dependência

```bash
pnpm add @radix-ui/react-popover
```

### Integração

**Arquivo:** Formulário de checkout

```typescript
import { DataPrivacyTooltip } from "@/components/ui/data-privacy-tooltip";

// Abaixo do campo de telefone
<TextField
  name="phone"
  label="Celular"
  icon={<PhoneIcon className="w-5 h-5" />}
/>
<DataPrivacyTooltip field="phone" />
```

### Validação

- [ ] Tooltip abre ao clicar
- [ ] Conteúdo explicativo é exibido
- [ ] Fecha ao clicar fora
- [ ] Acessível via teclado
- [ ] Responsive funciona

---

## 📦 Fase 6: Section Icons (BAIXA PRIORIDADE)

### Objetivo
Adicionar ícones nos títulos das seções para consistência visual.

### Implementação Rápida

**Arquivo:** Seção "Seus dados"

```typescript
import { UserCircleIcon } from "@heroicons/react/24/outline";

<div className="flex items-center gap-2 mb-4">
  <UserCircleIcon className="w-6 h-6 text-gray-700" />
  <h2 className="text-xl font-bold text-gray-900">Seus dados</h2>
</div>
```

**Arquivo:** Seção "Forma de Pagamento"

```typescript
import { CreditCardIcon } from "@heroicons/react/24/outline";

<div className="flex items-center gap-2 mb-4">
  <CreditCardIcon className="w-6 h-6 text-gray-700" />
  <h2 className="text-xl font-bold text-gray-900">Forma de Pagamento</h2>
</div>
```

### Validação

- [ ] Ícones aparecem ao lado dos títulos
- [ ] Tamanho e cor consistentes
- [ ] Alinhamento correto

---

## 📦 Fase 7: Security & Branding Footer (IMPORTANTE)

### Objetivo
Adicionar footer completo com logo Cakto, informações do vendedor, badges de segurança e links legais.

### Localização
**Arquivo:** `/apps/web/src/components/checkout/security-footer.tsx` (CRIAR NOVO)

### Design Specs

```
┌─────────────────────────────────────────────────┐
│ [Cakto Logo]                                    │
│ Cakto está processando este pagamento para o   │
│ vendedor Maria isabely                          │
│                                                 │
│ [✓] Compra 100% segura                          │
│                                                 │
│ Este site é protegido pelo reCAPTCHA e pela    │
│ • Política de Privacidade                       │
│ • Termos de Serviço                            │
│ • Parcelamento com acréscimo de juros          │
└─────────────────────────────────────────────────┘
```

### Implementação

```typescript
"use client";

import Image from "next/image";
import { ShieldCheckIcon } from "@heroicons/react/24/solid";
import { useCheckout } from "@/contexts";

export function SecurityFooter() {
  const { offer } = useCheckout();

  // NOTA: Verificar se API retorna informações do vendedor
  const sellerName = offer?.seller?.name || "o vendedor";

  return (
    <footer className="mt-8 space-y-6">
      {/* Logo e Seller Info */}
      <div className="text-center space-y-3">
        {/* Logo Cakto */}
        <div className="flex justify-center mb-2">
          <Image
            src="/assets/cakto-logo.svg"
            alt="Cakto"
            width={100}
            height={32}
            className="h-8 w-auto"
          />
        </div>

        {/* Seller Info */}
        <p className="text-sm text-gray-600">
          Cakto está processando este pagamento para{" "}
          <span className="font-medium text-gray-900">{sellerName}</span>
        </p>
      </div>

      {/* Security Badge */}
      <div className="flex items-center justify-center gap-2 py-3 px-4 bg-green-50 rounded-lg border border-green-200">
        <ShieldCheckIcon className="w-5 h-5 text-green-600" />
        <span className="text-sm font-medium text-green-700">
          Compra 100% segura
        </span>
      </div>

      {/* Legal Links */}
      <div className="text-center space-y-2">
        <p className="text-xs text-gray-600">
          Este site é protegido pelo reCAPTCHA e pela{" "}
          <a
            href="https://policies.google.com/privacy"
            target="_blank"
            rel="noopener noreferrer"
            className="text-brand-primary hover:text-brand-primary-hover underline"
          >
            Política de Privacidade
          </a>{" "}
          e{" "}
          <a
            href="https://policies.google.com/terms"
            target="_blank"
            rel="noopener noreferrer"
            className="text-brand-primary hover:text-brand-primary-hover underline"
          >
            Termos de Serviço
          </a>{" "}
          do Google.
        </p>

        <div className="flex flex-col items-center gap-1 text-xs text-gray-500">
          <a
            href="/privacy-policy"
            className="hover:text-brand-primary transition-colors"
          >
            Política de Privacidade
          </a>
          <a
            href="/terms-of-service"
            className="hover:text-brand-primary transition-colors"
          >
            Termos de Serviço
          </a>
          <p className="text-gray-400">
            Parcelamento com acréscimo de juros
          </p>
        </div>
      </div>
    </footer>
  );
}
```

### Integração

**Arquivo:** `/apps/web/src/app/[locale]/[id]/page.tsx`

```typescript
import { SecurityFooter } from "@/components/checkout/security-footer";

// Adicionar após o botão de pagamento
<SecurityFooter />
```

### Validação

- [ ] Logo Cakto é exibido
- [ ] Nome do vendedor é exibido
- [ ] Badge "Compra 100% segura" aparece
- [ ] Links funcionam
- [ ] Responsive em mobile

---

## 📦 Fase 8: Sidebar "Compra Segura" (OPCIONAL)

### Decisão de Design

**Opção A: Não implementar sidebar**
- Manter layout de card único
- Mais moderno e mobile-first
- Elementos já estão integrados

**Opção B: Implementar sidebar responsiva**
- Desktop: 2 colunas
- Mobile: Card único colapsado
- Mais fiel à V1

### Se optar por implementar

**Arquivo:** `/apps/web/src/components/checkout/secure-purchase-sidebar.tsx`

```typescript
"use client";

import Image from "next/image";
import { formatPrice } from "@/lib/utils/format";
import { useCheckout } from "@/contexts";
import { ShieldCheckIcon } from "@heroicons/react/24/solid";

export function SecurePurchaseSidebar() {
  const { offer } = useCheckout();

  if (!offer) return null;

  const product = offer.product;
  const bestInstallment = offer.installments?.[offer.installments.length - 1];

  return (
    <aside className="bg-white rounded-lg shadow-lg overflow-hidden sticky top-4">
      {/* Header */}
      <div className="bg-brand-primary px-6 py-4">
        <h2 className="text-lg font-bold text-white">Compra segura</h2>
      </div>

      {/* Content */}
      <div className="p-6 space-y-6">
        {/* Product Info */}
        <div className="space-y-3">
          {product.image && (
            <Image
              src={product.image}
              alt={product.name}
              width={80}
              height={80}
              className="rounded-full mx-auto"
            />
          )}
          <h3 className="text-base font-semibold text-gray-900 text-center">
            {product.name}
          </h3>
        </div>

        {/* Pricing */}
        <div className="text-center space-y-1">
          {bestInstallment && (
            <p className="text-lg font-bold text-gray-900">
              Em até {bestInstallment.quantity}x de{" "}
              {formatPrice(bestInstallment.value)}
            </p>
          )}
          <p className="text-sm text-gray-600">
            {formatPrice(offer.price)} à vista
          </p>
        </div>

        {/* Help Links */}
        <div className="space-y-2 text-center text-sm">
          <a
            href="/help"
            className="block text-brand-primary hover:text-brand-primary-hover"
          >
            Precisa de ajuda?
          </a>
          <a
            href="/seller-contact"
            className="block text-brand-primary hover:text-brand-primary-hover"
          >
            Veja o contato do vendedor
          </a>
        </div>

        {/* Security Info */}
        <div className="space-y-3 pt-4 border-t border-gray-200">
          <Image
            src="/assets/cakto-logo.svg"
            alt="Cakto"
            width={80}
            height={24}
            className="mx-auto"
          />
          <p className="text-xs text-gray-600 text-center">
            Cakto está processando este pagamento para{" "}
            {offer.seller?.name || "o vendedor"}
          </p>
          <div className="flex items-center justify-center gap-2 py-2 px-3 bg-green-50 rounded-lg">
            <ShieldCheckIcon className="w-4 h-4 text-green-600" />
            <span className="text-xs font-medium text-green-700">
              Compra 100% segura
            </span>
          </div>
        </div>
      </div>
    </aside>
  );
}
```

### Layout com Sidebar

**Arquivo:** `/apps/web/src/app/[locale]/[id]/page.tsx`

```typescript
<div className="container mx-auto px-4 py-8">
  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
    {/* Main Content */}
    <div className="lg:col-span-2">
      {/* Formulário de checkout */}
    </div>

    {/* Sidebar - Hidden on mobile */}
    <div className="hidden lg:block">
      <SecurePurchaseSidebar />
    </div>
  </div>
</div>
```

### Validação

- [ ] Sidebar aparece apenas em desktop
- [ ] Sticky funciona (fica fixo ao scroll)
- [ ] Todas as informações são exibidas
- [ ] Links funcionam
- [ ] Mobile esconde sidebar

---

## 🧪 Checklist Final de Validação

### Funcionalidades Críticas

- [ ] **ProductInfoCard**
  - [ ] Exibe logo, nome e preços
  - [ ] Dados vêm do contexto
  - [ ] Responsive

- [ ] **Order Bumps**
  - [ ] Lista produtos complementares
  - [ ] Checkboxes funcionam
  - [ ] Atualiza total dinamicamente
  - [ ] Envia dados no submit
  - [ ] Aparece no resumo do pedido

- [ ] **Enhanced Order Summary**
  - [ ] Lista produto principal
  - [ ] Lista order bumps selecionados
  - [ ] Calcula subtotal
  - [ ] Adiciona taxa de serviço
  - [ ] Mostra total correto
  - [ ] Exibe parcelamento (quando cartão)

- [ ] **Security Footer**
  - [ ] Logo Cakto
  - [ ] Nome do vendedor
  - [ ] Badge de segurança
  - [ ] Links legais

### Funcionalidades Importantes

- [ ] **Form Field Icons**
  - [ ] Ícones nos 4 campos
  - [ ] Posicionamento correto
  - [ ] Não interfere no input

- [ ] **Data Privacy Tooltip**
  - [ ] Abre e fecha corretamente
  - [ ] Conteúdo explicativo
  - [ ] Acessível

- [ ] **Section Icons**
  - [ ] Ícone em "Seus dados"
  - [ ] Ícone em "Forma de Pagamento"

### Testes de Integração

- [ ] Order bumps atualizam o total
- [ ] Dados são enviados na submissão
- [ ] Navegação entre métodos de pagamento funciona
- [ ] Validação de formulário funciona
- [ ] Erros são exibidos corretamente

### Testes Responsivos

- [ ] Mobile (< 640px)
- [ ] Tablet (640px - 1024px)
- [ ] Desktop (> 1024px)
- [ ] Landscape mobile

### Testes de Performance

- [ ] Imagens otimizadas (Next/Image)
- [ ] Sem re-renders desnecessários
- [ ] Lazy loading quando apropriado

---

## 📚 Recursos e Referências

### Bibliotecas Necessárias

```json
{
  "@heroicons/react": "^2.0.0",
  "@radix-ui/react-popover": "^1.0.0",
  "tailwind-variants": "^0.1.0"
}
```

### Comandos de Instalação

```bash
# Se faltar alguma dependência
pnpm add @heroicons/react
pnpm add @radix-ui/react-popover
```

### Arquivos de Referência V1

- `/cakto-checkout/src/pages/Checkout.tsx`
- `/cakto-checkout/src/components/OrderBump.tsx`
- `/cakto-checkout/src/components/OrderSummary.tsx`

### Sistema de Cores (Já Implementado)

Use as classes customizadas:
- `bg-brand-primary`
- `text-brand-primary`
- `border-brand-primary`
- `bg-brand-primary-light`
- `bg-brand-primary-lighter`
- `hover:bg-brand-primary-hover`

---

## 🚀 Ordem de Implementação Recomendada

1. **Dia 1:** ProductInfoCard + Security Footer (2-3h)
2. **Dia 2:** Order Bumps (full) (4-6h)
3. **Dia 3:** Enhanced Order Summary (2-3h)
4. **Dia 4:** Form Icons + Data Privacy Tooltip (2-3h)
5. **Dia 5:** Polish, testes e ajustes finais (2-4h)

**Total estimado:** 12-19 horas

---

## ⚠️ Pontos de Atenção

### Antes de Começar

1. ✅ **Verificar estrutura da API**
   - Confirmar se `offer.orderBumps` existe
   - Confirmar se `offer.seller` existe
   - Verificar estrutura de `offer.installments`

2. ✅ **Verificar assets**
   - Logo Cakto (`/assets/cakto-logo.svg`)
   - Logos de produtos
   - Placeholder images

3. ✅ **Verificar context**
   - `useCheckout()` tem todos os dados?
   - Precisa adicionar `selectedOrderBumps`?
   - Precisa adicionar `updateOrderBumps()`?

### Durante Implementação

- Sempre use o sistema de cores da marca (classes customizadas)
- Mantenha consistência visual com componentes existentes
- Teste responsividade em todos os breakpoints
- Valide acessibilidade (navegação por teclado, screen readers)
- Teste com dados reais da API

### Após Implementação

- Revisar código com linter
- Testar fluxo completo de checkout
- Validar com stakeholders
- Documentar mudanças

---

**Documento criado por:** AI Assistant  
**Data:** 11/11/2025  
**Versão:** 1.0  
**Status:** Pronto para implementação

