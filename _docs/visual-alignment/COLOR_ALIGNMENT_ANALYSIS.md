# Análise de Alinhamento de Cores - Cakto Checkout v2

**Data:** 11 de novembro de 2025  
**Contexto:** Alinhamento das cores da v2 com o padrão visual da Cakto (v1)

## Problema Identificado

A v2 do checkout está usando cores genéricas do Tailwind CSS em vez das cores oficiais da marca Cakto, resultando em uma experiência visual inconsistente.

## Cores Oficiais da Marca Cakto

### Cor Primária Cakto
```css
/* Verde Cakto - Usado em botões, tabs ativas, destaques */
#0F7864

/* Hover do Verde Cakto */
#0b6856
```

### Cor Primária Nommi (Alternativa)
```css
/* Azul Nommi - Usado quando BRANDING=nommi */
#2886B9

/* Hover do Azul Nommi */
#1f6e94
```

### Sombra do Botão
```css
/* Box shadow para hover em botões Cakto */
box-shadow: 0px 8px 16px 0px rgba(15, 120, 101, 0.24);
```

---

## Comparação: v1 vs v2

### 1. ❌ Tabs de Métodos de Pagamento

#### v1 (Correto)
```typescript
// PaymentMethods.tsx - linha 37
variants: {
  status: {
    active: 'bg-[#0F7864]',    // ✅ Verde Cakto
    inactive: 'text-gray-700'
  }
}
```

#### v2 (Incorreto)
```typescript
// payment-methods.tsx - linha 82
className={`... ${
  tabIndex === index
    ? "bg-green-600 text-white border-green-600 shadow-lg"  // ❌ Verde genérico (#16a34a)
    : "bg-white text-gray-700 border-gray-300 hover:border-green-400"
}`}
```

**Impacto:** Tab ativa está com verde errado, não combina com a identidade visual da Cakto.

---

### 2. ⚠️ Card do Formulário de Cartão de Crédito

#### v1 (Correto)
```typescript
// CreditCardForm.tsx - linha 69
<Card color="secondary" outline shadow={false} className="w-full">
```

Onde `color="secondary"` é mapeado para:
```css
bg-indigo-50 bg-opacity-50  /* Fundo levemente colorido */
```

#### v2 (Diferente)
```typescript
// CreditCardForm.tsx - linha 82
<Card outline shadow={false} className="w-full" backgroundColor="transparent">
```

**Impacto:** Card sem fundo colorido, visual mais "plano" e menos destacado.

---

### 3. ❌ Componente Button

#### v1 (Correto)
```typescript
// Button.tsx - linha 54
const bgColor = backgroundColor || (isNommi ? '#2886B9' : '#0F7864');
const hoverColor = isNommi ? '#1f6e94' : '#0b6856';
```

#### v2 (Parcialmente Correto)
```typescript
// button-form.tsx - linha 60
const bgColor = backgroundColor || style?.backgroundColor || (isNommi ? "#2886B9" : "#0F7864");
const hoverColor = isNommi ? "#1f6e94" : "#0b6856";
```

**Status:** ✅ Button está correto! Usa as cores Cakto apropriadas.

---

### 4. ❌ Outros Componentes com Verde Genérico

Componentes encontrados na v2 usando `green-*` do Tailwind:

1. **PixAutoPayment.tsx** (linha 131)
   ```typescript
   className="... border-green-700 ... hover:bg-green-50/60"
   ```

2. **PixPayment.tsx** (linhas 131, 165)
   ```typescript
   className="... border-green-700 ..."
   className="... bg-green-50 text-green-700 ..."
   ```

3. **BoletoPayment.tsx** (linha 92)
   ```typescript
   className="... border-green-700 ..."
   ```

4. **CouponFormNew.tsx** (linha 70)
   ```typescript
   <div className="... bg-green-50 ...">
   ```

**Impacto:** Tonalidade de verde inconsistente com a marca.

---

## Configuração do Tailwind

### v1 - Tailwind 3.x com arquivo de configuração
```javascript
// tailwind.config.js
theme: {
  extend: {
    boxShadow: {
      'button-hover': '0px 8px 16px 0px rgba(15, 120, 101, 0.24)',
    }
  }
}
```

### v2 - Tailwind 4.x com CSS-in-JS
```css
/* index.css - linha 1-2 */
@import "tailwindcss";
@theme {
  /* Configuração via CSS */
}
```

**Diferença:** v2 usa Tailwind 4.x que não tem arquivo `.config` separado.

---

## Tabela de Mapeamento de Cores

| Elemento | v1 (Correto) | v2 (Atual) | Status | Precisa Mudar? |
|----------|--------------|------------|--------|----------------|
| Tab ativa | `#0F7864` | `green-600` (#16a34a) | ❌ | ✅ Sim |
| Tab hover | `#0b6856` | `green-400` border | ❌ | ✅ Sim |
| Button primário | `#0F7864` | `#0F7864` | ✅ | ❌ Não |
| Button hover | `#0b6856` | `#0b6856` | ✅ | ❌ Não |
| Card form | `indigo-50` opacity | `transparent` | ⚠️ | ✅ Sim |
| Pix border | `#0F7864` | `green-700` | ❌ | ✅ Sim |
| Pix background | - | `green-50` | ❌ | ✅ Sim |
| Coupon success | - | `green-50` | ❌ | ✅ Sim |
| Boleto border | `#0F7864` | `green-700` | ❌ | ✅ Sim |

---

## Plano de Ação

### Fase 1: Criar Tokens de Cor no Tailwind (ESSENCIAL)

**Objetivo:** Definir as cores da Cakto como tokens reutilizáveis.

**Arquivo:** `/apps/web/src/index.css`

```css
@theme {
  /* ... configuração existente ... */
  
  /* Cores da marca Cakto */
  --color-cakto-primary: #0F7864;
  --color-cakto-primary-hover: #0b6856;
  --color-cakto-primary-light: #f0fdf9;
  --color-cakto-secondary: #eef2ff; /* indigo-50 */
  
  /* Cores da marca Nommi */
  --color-nommi-primary: #2886B9;
  --color-nommi-primary-hover: #1f6e94;
  
  /* Sombras */
  --shadow-button-hover: 0px 8px 16px 0px rgba(15, 120, 101, 0.24);
}
```

Uso no Tailwind:
```typescript
className="bg-cakto-primary hover:bg-cakto-primary-hover"
```

---

### Fase 2: Atualizar PaymentMethods.tsx (CRÍTICO)

**Arquivo:** `/apps/web/src/components/checkout/payment-methods.tsx`

**Mudança:**
```typescript
// ANTES (linha 82)
className={`... ${
  tabIndex === index
    ? "bg-green-600 text-white border-green-600 shadow-lg"
    : "bg-white text-gray-700 border-gray-300 hover:border-green-400"
}`}

// DEPOIS
className={`... ${
  tabIndex === index
    ? "bg-[#0F7864] text-white border-[#0F7864] shadow-lg"
    : "bg-white text-gray-700 border-gray-300 hover:border-[#0F7864] hover:border-opacity-60"
}`}
```

**Ou com tokens (melhor):**
```typescript
className={`... ${
  tabIndex === index
    ? "bg-cakto-primary text-white border-cakto-primary shadow-lg"
    : "bg-white text-gray-700 border-gray-300 hover:border-cakto-primary hover:border-opacity-60"
}`}
```

---

### Fase 3: Atualizar CreditCardForm (Card)

**Arquivo:** `/apps/web/src/components/payments/CreditCardForm.tsx`

**Mudança (linha 82):**
```typescript
// ANTES
<Card outline shadow={false} className="w-full" backgroundColor="transparent">

// DEPOIS
<Card color="secondary" outline shadow={false} className="w-full">
```

**Garantir que Card.tsx suporte `color="secondary"`:**
```typescript
// /apps/web/src/components/ui/card-form.tsx
variants: {
  color: {
    primary: "bg-white",
    secondary: "bg-indigo-50 bg-opacity-50",  // ✅ Adicionar
  }
}
```

---

### Fase 4: Atualizar Componentes de Pagamento

#### 4.1. PixPayment.tsx

**Mudanças:**
```typescript
// Linha 131 - Border do botão
className="... border-[#0F7864] ... hover:bg-[#f0fdf9]"

// Linha 165 - Badge de número
className="... bg-[#f0fdf9] text-[#0F7864] ..."
```

#### 4.2. PixAutoPayment.tsx

**Mudanças (linha 131):**
```typescript
className="... border-[#0F7864] ... hover:bg-[#f0fdf9]"
```

#### 4.3. BoletoPayment.tsx

**Mudanças (linha 92):**
```typescript
className="... border-[#0F7864] ... hover:bg-[#f0fdf9]"
```

#### 4.4. CouponFormNew.tsx

**Mudanças (linha 70):**
```typescript
<div className="... bg-[#f0fdf9] ...">
```

---

### Fase 5: Adicionar Configuração de Sombra

**Arquivo:** `/apps/web/src/index.css`

```css
@theme {
  /* ... outras configurações ... */
  
  --shadow-button-hover: 0px 8px 16px 0px rgba(15, 120, 101, 0.24);
}

/* Classe utilitária para sombra de botão */
.shadow-button-hover {
  box-shadow: var(--shadow-button-hover);
}
```

**Usar em Button.tsx e outros:**
```typescript
className="... hover:shadow-button-hover"
```

---

## Checklist de Implementação

### Essenciais (Bloqueadores)
- [ ] Criar tokens de cor no `index.css`
- [ ] Atualizar `payment-methods.tsx` (tabs)
- [ ] Atualizar `CreditCardForm.tsx` (card background)
- [ ] Adicionar sombra `button-hover` no tema

### Importantes (Alta Prioridade)
- [ ] Atualizar `PixPayment.tsx` (borders e backgrounds)
- [ ] Atualizar `PixAutoPayment.tsx` (borders e backgrounds)
- [ ] Atualizar `BoletoPayment.tsx` (borders e backgrounds)
- [ ] Atualizar `CouponFormNew.tsx` (background)

### Melhorias (Média Prioridade)
- [ ] Revisar todos os componentes com `green-*`
- [ ] Criar documentação de uso das cores
- [ ] Adicionar variantes dark mode (futuro)

### Testes
- [ ] Testar tabs de pagamento (visual)
- [ ] Testar hover states
- [ ] Testar formulário de cartão
- [ ] Testar componentes Pix/Boleto
- [ ] Comparar lado a lado com v1

---

## Impacto Visual Esperado

### Antes (v2 atual)
- ❌ Tabs com verde claro genérico (#16a34a)
- ❌ Card de cartão sem fundo colorido
- ❌ Inconsistência nas tonalidades de verde
- ⚠️ Visual menos "Cakto"

### Depois (v2 alinhado)
- ✅ Tabs com verde Cakto oficial (#0F7864)
- ✅ Card de cartão com fundo indigo-50
- ✅ Cores consistentes em toda aplicação
- ✅ Visual idêntico à identidade da marca

---

## Referências de Cores

### Paleta Cakto Completa

```css
/* Primárias */
--cakto-green: #0F7864;
--cakto-green-hover: #0b6856;
--cakto-green-light: #f0fdf9; /* Para backgrounds sutis */

/* Secundárias */
--cakto-indigo-light: #eef2ff; /* indigo-50 */

/* Cinzas (para textos e borders) */
--gray-300: #d1d5db;
--gray-700: #374151;
--gray-500: #6b7280;

/* Utilitárias */
--border-default: #919EAB33; /* usado em inputs */
```

### Conversão Tailwind → Hex

| Classe Tailwind | Valor Hex | Uso Atual v2 | Deve Ser |
|-----------------|-----------|--------------|----------|
| `green-50` | `#f0fdf4` | Backgrounds sutis | `#f0fdf9` |
| `green-400` | `#4ade80` | Border hover | `#0F7864` |
| `green-600` | `#16a34a` | Tab ativa | `#0F7864` |
| `green-700` | `#15803d` | Borders | `#0F7864` |
| `indigo-50` | `#eef2ff` | Card secondary | ✅ Correto |

---

## Próximos Passos

1. **Imediato:**
   - Implementar tokens de cor no Tailwind 4
   - Corrigir tabs de métodos de pagamento
   - Corrigir card de formulário de cartão

2. **Curto Prazo:**
   - Atualizar todos os componentes de pagamento
   - Testar em diferentes resoluções
   - Documentar uso correto das cores

3. **Médio Prazo:**
   - Criar componente de amostra de cores
   - Adicionar testes visuais automatizados
   - Preparar tema dark mode (futuro)

---

## Notas Técnicas

### Tailwind 4.x Differences

A v2 usa Tailwind 4.x que tem algumas diferenças importantes:

1. **Sem arquivo de configuração separado** - tudo é feito via `@theme` no CSS
2. **Cores customizadas via CSS variables** - mais flexível
3. **Performance melhorada** - CSS-in-JS mais rápido

### Branding Dinâmico

Ambas versões suportam múltiplas marcas (Cakto/Nommi):

```typescript
const bgColor = isNommi ? '#2886B9' : '#0F7864';
```

Garantir que todas as mudanças respeitem essa lógica.

---

**Documento criado em:** 11 de novembro de 2025  
**Status:** 📋 Análise Completa - Aguardando Implementação  
**Prioridade:** 🔴 Alta - Impacto visual significativo

