# Prompt: Implementação do Sistema de Cores Cakto no Checkout v2

## Contexto

Estamos migrando o checkout da Cakto da v1 (React + Vite + Tailwind 3.x) para v2 (Next.js + Tailwind 4.x). A v2 atualmente usa cores genéricas do Tailwind (`green-600`, `green-700`, etc.) em vez das cores oficiais da marca Cakto. Precisamos implementar um sistema de cores robusto, escalável e bem organizado que:

1. **Use as cores oficiais da marca Cakto** (verde #0F7864)
2. **<PERSON>ga as melhores práticas do Tailwind 4.x** (CSS-first configuration)
3. **Seja escalável** para futuras adições (dark mode, novos temas)
4. **Seja type-safe** quando possível
5. **Suporte múltiplas marcas** (Cakto e Nommi)

---

## Cores Oficiais da Marca

### <PERSON><PERSON><PERSON> (Marc<PERSON> Principal)

```css
/* Primárias */
Verde Principal: #0F7864
Verde Hover: #0b6856
<PERSON>laro (backgrounds): #f0fdf9
Verde Muito Claro: #ecfdf5

/* Secundárias */
Indigo Claro (cards): #eef2ff
Indigo Mais Claro: #e0e7ff

/* Sombras */
Button Hover: 0px 8px 16px 0px rgba(15, 120, 101, 0.24)
Card Shadow: 0px 1px 3px 0px rgba(15, 120, 101, 0.1)
```

### Nommi (Marca Alternativa)

```css
/* Primárias */
Azul Principal: #2886B9
Azul Hover: #1f6e94
Azul Claro: #f0f9ff
```

---

## Objetivo da Tarefa

Criar um sistema de design tokens no Tailwind 4.x que:

1. **Defina todas as cores da marca como variáveis CSS**
2. **Crie classes utilitárias reutilizáveis**
3. **Implemente um sistema de branding dinâmico**
4. **Atualize todos os componentes para usar as novas classes**
5. **Mantenha compatibilidade com dark mode (preparação futura)**

---

## Estrutura de Implementação

### Fase 1: Configurar Design Tokens no Tailwind 4.x

**Arquivo:** `/apps/web/src/index.css`

#### 1.1. Adicionar Variáveis CSS de Cores

Adicione no bloco `@theme` existente:

```css
@theme {
  /* ==================== EXISTING CONFIG ==================== */
  --font-sans: "Inter", "Geist", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  /* ==================== CAKTO BRAND COLORS ==================== */
  
  /* Primary Colors - Cakto Green */
  --color-cakto-primary: #0F7864;
  --color-cakto-primary-hover: #0b6856;
  --color-cakto-primary-light: #f0fdf9;
  --color-cakto-primary-lighter: #ecfdf5;
  
  /* Primary Colors - Nommi Blue */
  --color-nommi-primary: #2886B9;
  --color-nommi-primary-hover: #1f6e94;
  --color-nommi-primary-light: #f0f9ff;
  
  /* Secondary Colors */
  --color-secondary: #eef2ff;
  --color-secondary-dark: #e0e7ff;
  
  /* Border Colors */
  --color-border-light: #919EAB33;
  --color-border-default: #d1d5db;
  
  /* ==================== SHADOWS ==================== */
  
  /* Button Shadows */
  --shadow-button-hover: 0px 8px 16px 0px rgba(15, 120, 101, 0.24);
  --shadow-button-nommi-hover: 0px 8px 16px 0px rgba(40, 134, 185, 0.24);
  
  /* Card Shadows */
  --shadow-card-subtle: 0px 1px 3px 0px rgba(15, 120, 101, 0.1);
  
  /* ==================== RADIUS ==================== */
  --radius: 0.625rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}
```

#### 1.2. Criar Classes Utilitárias Customizadas

Adicione após o bloco `@theme`:

```css
/* ==================== BRAND UTILITIES ==================== */

/* Background Colors */
.bg-brand-primary {
  background-color: var(--color-cakto-primary);
}

.bg-brand-primary-hover {
  background-color: var(--color-cakto-primary-hover);
}

.bg-brand-primary-light {
  background-color: var(--color-cakto-primary-light);
}

.bg-brand-primary-lighter {
  background-color: var(--color-cakto-primary-lighter);
}

/* Text Colors */
.text-brand-primary {
  color: var(--color-cakto-primary);
}

.text-brand-primary-hover {
  color: var(--color-cakto-primary-hover);
}

/* Border Colors */
.border-brand-primary {
  border-color: var(--color-cakto-primary);
}

.border-brand-primary-hover {
  border-color: var(--color-cakto-primary-hover);
}

/* Hover States */
.hover\:bg-brand-primary-hover:hover {
  background-color: var(--color-cakto-primary-hover);
}

.hover\:border-brand-primary:hover {
  border-color: var(--color-cakto-primary);
}

.hover\:text-brand-primary:hover {
  color: var(--color-cakto-primary);
}

/* Focus States */
.focus\:ring-brand-primary:focus {
  --tw-ring-color: var(--color-cakto-primary);
}

/* Shadow Utilities */
.shadow-button-hover {
  box-shadow: var(--shadow-button-hover);
}

.shadow-card-subtle {
  box-shadow: var(--shadow-card-subtle);
}

/* ==================== NOMMI BRAND OVERRIDES (when needed) ==================== */

[data-branding="nommi"] .bg-brand-primary {
  background-color: var(--color-nommi-primary);
}

[data-branding="nommi"] .bg-brand-primary-hover {
  background-color: var(--color-nommi-primary-hover);
}

[data-branding="nommi"] .text-brand-primary {
  color: var(--color-nommi-primary);
}

[data-branding="nommi"] .border-brand-primary {
  border-color: var(--color-nommi-primary);
}

[data-branding="nommi"] .shadow-button-hover {
  box-shadow: var(--shadow-button-nommi-hover);
}
```

---

### Fase 2: Criar Utilitário de Branding Dinâmico

**Arquivo:** `/apps/web/src/lib/utils/colors.ts` (CRIAR NOVO)

```typescript
import { BRANDING } from "./brand";

/**
 * Retorna as cores da marca atual (Cakto ou Nommi)
 */
export const getBrandColors = () => {
  if (BRANDING === "nommi") {
    return {
      primary: "#2886B9",
      primaryHover: "#1f6e94",
      primaryLight: "#f0f9ff",
      shadow: "0px 8px 16px 0px rgba(40, 134, 185, 0.24)",
    };
  }

  // Default: Cakto
  return {
    primary: "#0F7864",
    primaryHover: "#0b6856",
    primaryLight: "#f0fdf9",
    primaryLighter: "#ecfdf5",
    shadow: "0px 8px 16px 0px rgba(15, 120, 101, 0.24)",
  };
};

/**
 * Classe CSS para cor primária da marca
 */
export const brandPrimaryClass = () => {
  return BRANDING === "nommi" 
    ? "bg-[#2886B9] hover:bg-[#1f6e94]" 
    : "bg-brand-primary hover:bg-brand-primary-hover";
};

/**
 * Retorna style object para cores da marca
 */
export const getBrandStyles = () => {
  const colors = getBrandColors();
  return {
    backgroundColor: colors.primary,
    "--hover-bg": colors.primaryHover,
  } as React.CSSProperties;
};
```

---

### Fase 3: Atualizar Componentes

#### 3.1. PaymentMethods.tsx (CRÍTICO)

**Arquivo:** `/apps/web/src/components/checkout/payment-methods.tsx`

**ANTES (linhas 74-95):**
```typescript
<div className="flex gap-3 overflow-x-auto pb-2 px-1">
  {tabs.map(({ Icon, label, id }, index) => (
    <button
      key={id}
      type="button"
      onClick={() => handleTabChange(index)}
      className={`flex flex-col items-center justify-center gap-2 p-4 rounded-lg border-2 transition-all flex-1 min-w-[110px] max-w-[140px] ${
        tabIndex === index
          ? "bg-green-600 text-white border-green-600 shadow-lg"
          : "bg-white text-gray-700 border-gray-300 hover:border-green-400 hover:shadow-md"
      }`}
    >
```

**DEPOIS:**
```typescript
<div className="flex gap-3 overflow-x-auto pb-2 px-1">
  {tabs.map(({ Icon, label, id }, index) => (
    <button
      key={id}
      type="button"
      onClick={() => handleTabChange(index)}
      className={`flex flex-col items-center justify-center gap-2 p-4 rounded-lg border-2 transition-all flex-1 min-w-[110px] max-w-[140px] ${
        tabIndex === index
          ? "bg-brand-primary text-white border-brand-primary shadow-lg"
          : "bg-white text-gray-700 border-gray-300 hover:border-brand-primary hover:border-opacity-60 hover:shadow-md"
      }`}
    >
```

**OU usando utility function:**
```typescript
import { getBrandColors } from "@/lib/utils/colors";

// No componente
const brandColors = getBrandColors();

// No JSX
<button
  style={{
    backgroundColor: tabIndex === index ? brandColors.primary : "white",
    borderColor: tabIndex === index ? brandColors.primary : "#d1d5db",
  }}
  className={`... ${
    tabIndex === index
      ? "text-white shadow-lg"
      : "text-gray-700 hover:shadow-md"
  }`}
>
```

#### 3.2. CreditCardForm.tsx

**Arquivo:** `/apps/web/src/components/payments/CreditCardForm.tsx`

**ANTES (linha ~82):**
```typescript
<Card outline shadow={false} className="w-full" backgroundColor="transparent">
```

**DEPOIS:**
```typescript
<Card color="secondary" outline shadow={false} className="w-full">
```

**E garantir que card-form.tsx suporte:**
```typescript
// /apps/web/src/components/ui/card-form.tsx
variants: {
  color: {
    primary: "bg-white",
    secondary: "bg-[#eef2ff] bg-opacity-50",
  },
}
```

#### 3.3. PixPayment.tsx

**Arquivo:** `/apps/web/src/components/payments/PixPayment.tsx`

**ANTES (linha ~131):**
```typescript
className="w-full py-3 px-4 border border-green-700 bg-white hover:bg-green-50/60"
```

**DEPOIS:**
```typescript
className="w-full py-3 px-4 border border-brand-primary bg-white hover:bg-brand-primary-light"
```

**ANTES (linha ~165):**
```typescript
<span className="... bg-green-50 text-green-700 ...">
```

**DEPOIS:**
```typescript
<span className="... bg-brand-primary-lighter text-brand-primary ...">
```

#### 3.4. PixAutoPayment.tsx

**Arquivo:** `/apps/web/src/components/payments/PixAutoPayment.tsx`

**ANTES (linha ~131):**
```typescript
className="... border-green-700 ... hover:bg-green-50/60"
```

**DEPOIS:**
```typescript
className="... border-brand-primary ... hover:bg-brand-primary-light"
```

#### 3.5. BoletoPayment.tsx

**Arquivo:** `/apps/web/src/components/payments/BoletoPayment.tsx`

**ANTES (linha ~92):**
```typescript
className="... border-green-700 ... hover:bg-green-50/60"
```

**DEPOIS:**
```typescript
className="... border-brand-primary ... hover:bg-brand-primary-light"
```

#### 3.6. CouponFormNew.tsx

**Arquivo:** `/apps/web/src/components/payments/CouponFormNew.tsx`

**ANTES (linha ~70):**
```typescript
<div className="... bg-green-50 ...">
```

**DEPOIS:**
```typescript
<div className="... bg-brand-primary-light ...">
```

#### 3.7. Button.tsx

**Arquivo:** `/apps/web/src/components/ui/button-form.tsx`

Adicionar classe de sombra no hover:

**ANTES:**
```typescript
className={`${button({ size: finalSize })} ${className ?? ""}`}
```

**DEPOIS:**
```typescript
className={`${button({ size: finalSize })} hover:shadow-button-hover ${className ?? ""}`}
```

---

### Fase 4: Atualizar Card Component

**Arquivo:** `/apps/web/src/components/ui/card-form.tsx`

Garantir que o componente suporte `color="secondary"`:

```typescript
const card = tv({
  base: `
  block
  p-5
  xs:p-3
  rounded-md
  shadow-lg
  w-full
  `,
  variants: {
    color: {
      primary: "bg-white",
      secondary: "bg-[#eef2ff] bg-opacity-50",  // ← ADICIONAR/VERIFICAR
    },
    outline: {
      true: "border border-slate-300",
    },
    shadow: {
      false: "shadow-none",
    },
  },
});
```

---

### Fase 5: Adicionar Data Attribute para Branding

**Arquivo:** `/apps/web/src/app/layout.tsx`

Adicionar data attribute para permitir overrides de Nommi:

```typescript
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const branding = getBranding(); // Importar de @/lib/env

  return (
    <html lang="pt-BR" data-branding={branding}>
      <body>{children}</body>
    </html>
  );
}
```

Isso permite que as classes `[data-branding="nommi"]` funcionem automaticamente.

---

## Checklist de Implementação

### Essenciais (Fazer Primeiro)

- [ ] **Fase 1.1:** Adicionar variáveis CSS no `@theme` do `index.css`
- [ ] **Fase 1.2:** Criar classes utilitárias de marca no `index.css`
- [ ] **Fase 2:** Criar arquivo `lib/utils/colors.ts` com helpers
- [ ] **Fase 3.1:** Atualizar `payment-methods.tsx` (tabs)
- [ ] **Fase 3.2:** Atualizar `CreditCardForm.tsx` (card)
- [ ] **Fase 4:** Garantir que `card-form.tsx` suporte `color="secondary"`

### Importantes (Fazer em Seguida)

- [ ] **Fase 3.3:** Atualizar `PixPayment.tsx`
- [ ] **Fase 3.4:** Atualizar `PixAutoPayment.tsx`
- [ ] **Fase 3.5:** Atualizar `BoletoPayment.tsx`
- [ ] **Fase 3.6:** Atualizar `CouponFormNew.tsx`
- [ ] **Fase 3.7:** Adicionar sombra hover no `button-form.tsx`

### Opcional (Nice to Have)

- [ ] **Fase 5:** Adicionar `data-branding` no layout
- [ ] Criar Storybook com exemplos de cores
- [ ] Adicionar testes visuais
- [ ] Documentar sistema de cores no README

---

## Testes de Validação

Após implementação, validar:

### Visual
1. ✅ Tabs de pagamento usam verde Cakto (#0F7864)
2. ✅ Hover das tabs muda para verde mais escuro (#0b6856)
3. ✅ Card de formulário tem fundo indigo claro
4. ✅ Botões têm sombra no hover
5. ✅ Componentes Pix/Boleto usam cores corretas

### Funcional
1. ✅ Cores mudam corretamente para Nommi quando `BRANDING=nommi`
2. ✅ Todas as classes customizadas funcionam
3. ✅ Não há regressões visuais em outros componentes

### Compatibilidade
1. ✅ Funciona em Chrome, Firefox, Safari
2. ✅ Funciona em mobile e desktop
3. ✅ Build de produção funciona corretamente

---

## Exemplo de Uso das Classes

### Botão Primário
```typescript
<button className="bg-brand-primary hover:bg-brand-primary-hover text-white">
  Finalizar Compra
</button>
```

### Card Secundário
```typescript
<Card color="secondary" outline>
  Conteúdo do card
</Card>
```

### Border com Cor da Marca
```typescript
<div className="border-2 border-brand-primary hover:border-brand-primary-hover">
  Container
</div>
```

### Usando Helpers TypeScript
```typescript
import { getBrandColors, brandPrimaryClass } from "@/lib/utils/colors";

// Pegar cores programaticamente
const colors = getBrandColors();
style={{ backgroundColor: colors.primary }}

// Ou usar classe dinâmica
className={brandPrimaryClass()}
```

---

## Boas Práticas

### ✅ FAZER

1. **Usar classes de marca** em vez de valores hardcoded
   ```typescript
   // ✅ BOM
   className="bg-brand-primary"
   
   // ❌ EVITAR
   className="bg-[#0F7864]"
   ```

2. **Usar helpers TypeScript** quando precisar de lógica condicional
   ```typescript
   // ✅ BOM
   const colors = getBrandColors();
   style={{ color: colors.primary }}
   ```

3. **Agrupar variantes relacionadas**
   ```css
   /* ✅ BOM - agrupado por contexto */
   --color-cakto-primary: #0F7864;
   --color-cakto-primary-hover: #0b6856;
   --color-cakto-primary-light: #f0fdf9;
   ```

### ❌ NÃO FAZER

1. **Não usar cores Tailwind genéricas** para elementos de marca
   ```typescript
   // ❌ EVITAR
   className="bg-green-600"  // Verde genérico
   
   // ✅ USAR
   className="bg-brand-primary"  // Verde Cakto
   ```

2. **Não hardcodar valores hex** diretamente nos componentes
   ```typescript
   // ❌ EVITAR
   style={{ backgroundColor: "#0F7864" }}
   
   // ✅ USAR
   className="bg-brand-primary"
   // ou
   style={{ backgroundColor: getBrandColors().primary }}
   ```

3. **Não misturar abordagens**
   ```typescript
   // ❌ EVITAR - inconsistente
   className="bg-brand-primary border-green-600"
   
   // ✅ USAR - consistente
   className="bg-brand-primary border-brand-primary"
   ```

---

## Estrutura Final de Arquivos

```
apps/web/src/
├── index.css                          # ← Tokens e classes customizadas
├── lib/
│   └── utils/
│       ├── colors.ts                  # ← Helper functions (CRIAR NOVO)
│       └── brand.ts                   # ← Já existe
├── components/
│   ├── checkout/
│   │   └── payment-methods.tsx        # ← Atualizar
│   ├── payments/
│   │   ├── CreditCardForm.tsx         # ← Atualizar
│   │   ├── PixPayment.tsx             # ← Atualizar
│   │   ├── PixAutoPayment.tsx         # ← Atualizar
│   │   ├── BoletoPayment.tsx          # ← Atualizar
│   │   └── CouponFormNew.tsx          # ← Atualizar
│   └── ui/
│       ├── button-form.tsx            # ← Atualizar (adicionar sombra)
│       └── card-form.tsx              # ← Verificar color="secondary"
└── app/
    └── layout.tsx                     # ← Adicionar data-branding (opcional)
```

---

## Referências

- [Tailwind 4.x CSS-first Config](https://tailwindcss.com/docs/v4-beta)
- [Análise Completa de Cores](./_docs/visual-alignment/COLOR_ALIGNMENT_ANALYSIS.md)
- [Brand Utils v1](/cakto-checkout/src/utils/brand/index.ts)

---

## Notas Finais

- **Priorize as fases 1-4** - elas têm o maior impacto visual
- **Teste cada mudança** antes de prosseguir para a próxima
- **Use inspeção visual** lado a lado com a v1 para garantir fidelidade
- **Documente** qualquer desvio ou decisão de design

---

**Prompt criado em:** 11 de novembro de 2025  
**Versão:** 1.0  
**Status:** ✅ Pronto para Implementação

