# Análise de Gap: Checkout V1 vs V2

**Data:** 11/11/2025  
**Objetivo:** Mapear todos os elementos presentes na V1 (Vite) que estão ausentes ou incompletos na V2 (Next.js)

---

## 📊 Visão Geral

### Layout Structure

| Elemento | V1 (Vite) | V2 (Next.js) | Status |
|----------|-----------|--------------|--------|
| Layout de 2 colunas | ✅ Sim | ❌ Não | ⚠️ Missing |
| Card centralizado único | ❌ Não | ✅ Sim | ✅ Implementado |
| Sidebar "Compra Segura" | ✅ Sim | ❌ Não | ⚠️ Missing |
| Country Selector | ❌ Não | ✅ Sim | ✅ Implementado |

---

## 🎯 Elementos Faltantes (Missing Components)

### 1. **Product Information Card** (CRÍTICO)

**V1 - Presente:**
```
┌─────────────────────────────────────┐
│ [Logo <PERSON>] Curso de confeitaria   │
│ De R$ 35,90                         │
│ 7 X de R$ 5,07                      │
│ ou R$ 29,90 à vista                 │
└─────────────────────────────────────┘
```

**V2 - Ausente:**
- Não exibe informações do produto no topo do formulário
- Usuário não vê o que está comprando enquanto preenche dados

**Impacto:** ⚠️ ALTO - Usuário perde contexto do que está comprando

**Localização esperada:** Acima da seção "Informações de Contato" ou dentro dela

---

### 2. **Ícones nos Campos de Formulário** (IMPORTANTE)

**V1 - Presente:**
```
┌─────────────────────────────────────┐
│ Seus dados                          │
│ [👤] Nome completo                  │
│ [📧] Email                          │
│ [📄] CPF/CNPJ                       │
│ [📱] Celular                        │
└─────────────────────────────────────┘
```

**V2 - Ausente:**
- Campos não possuem ícones visuais
- Menos guidance visual para o usuário

**Impacto:** ⚠️ MÉDIO - UX menos intuitiva

**Ícones necessários:**
- Nome: User icon
- Email: Email/Envelope icon
- CPF/CNPJ: Document/ID icon
- Celular: Phone icon

---

### 3. **Link "Porque pedimos esse dado?"** (IMPORTANTE)

**V1 - Presente:**
```
Celular [📱] __________________
[?] Porque pedimos esse dado?
```

**V2 - Ausente:**

**Impacto:** ⚠️ MÉDIO - Transparência e confiança reduzidas

**Funcionalidade esperada:**
- Link ou tooltip explicativo
- Modal ou accordion com justificativa
- Aumenta confiança do usuário

---

### 4. **Ícone na Seção "Pagamento"** (BAIXO)

**V1 - Presente:**
```
┌─────────────────────────────────────┐
│ [💳] Pagamento                      │
│ [Apple Pay] [Google Pay] [PIX]...  │
└─────────────────────────────────────┘
```

**V2 - Presente (sem ícone):**
```
┌─────────────────────────────────────┐
│ Forma de Pagamento                  │
│ [Apple Pay] [Google Pay] [PIX]...  │
└─────────────────────────────────────┘
```

**Impacto:** ⚠️ BAIXO - Estético

**Ícone sugerido:** Cartão de crédito ou ícone genérico de pagamento

---

### 5. **Order Bumps / Ofertas Limitadas** (CRÍTICO)

**V1 - Presente:**
```
┌─────────────────────────────────────┐
│ Ofertas limitadas                   │
│                                     │
│ ☐ Multiplique suas vendas          │
│    R$ 10,00                         │
│                                     │
│ ☐ Apostila +100 receitas caseiras  │
│    R$ 7,50                          │
│                                     │
│ ☐ Aprenda a persuadir seu cliente  │
│    R$ 6,50                          │
│                                     │
│ ☐ Acesso vitalicio                 │
│    R$ 10,00                         │
└─────────────────────────────────────┘
```

**V2 - Ausente completamente**

**Impacto:** ⚠️ CRÍTICO - Perda de oportunidade de upsell/cross-sell

**Funcionalidades necessárias:**
- Listagem de produtos complementares
- Checkboxes para adicionar ao pedido
- Atualização automática do total
- Imagens dos produtos (quando disponível)
- Descrição curta de cada oferta

**Localização esperada:** Entre seção de pagamento e resumo do pedido

---

### 6. **Resumo do Pedido Detalhado** (IMPORTANTE)

**V1 - Presente (versão completa):**
```
┌─────────────────────────────────────┐
│ Resumo do pedido                    │
│                                     │
│ Curso de confeitaria profissional  │
│ R$ 29,90                            │
│                                     │
│ Taxa de serviço                     │
│ R$ 0,99                             │
│                                     │
│ Total                               │
│ 7x de R$ 5,21                       │
└─────────────────────────────────────┘
```

**V2 - Presente (versão simplificada):**
```
┌─────────────────────────────────────┐
│ Resumo do pedido                    │
│                                     │
│ [Logo] Curso de confeitaria...      │
│ R$ 29,90                            │
│                                     │
│ Taxa de serviço    R$ 0,99         │
│ Total              R$ 30,89         │
└─────────────────────────────────────┘
```

**Diferenças:**
- ✅ V2 tem logo do produto
- ❌ V2 não mostra opções de parcelamento no total
- ❌ V2 não lista order bumps adicionados

**Impacto:** ⚠️ MÉDIO - Informação incompleta

**Melhorias necessárias:**
- Adicionar opção de exibir "7x de R$ X,XX" quando cartão selecionado
- Listar order bumps adicionados dinamicamente
- Subtotal antes de taxa de serviço

---

### 7. **Sidebar "Compra Segura"** (IMPORTANTE)

**V1 - Presente:**
```
┌─────────────────────────┐
│ Compra segura          │
├─────────────────────────┤
│ [Logo] Curso de...     │
│                        │
│ Em até 7 X de R$ 5,07  │
│ R$ 29,90 à vista       │
│                        │
│ Precisa de ajuda?      │
│ Veja o contato do      │
│ vendedor               │
│                        │
│ [Cakto logo]           │
│ Cakto está processando │
│ este pagamento para... │
│                        │
│ Compra 100% segura     │
└─────────────────────────┘
```

**V2 - Ausente completamente**

**Impacto:** ⚠️ ALTO - Layout menos robusto, menos informações de confiança

**Funcionalidades na sidebar:**
1. Header "Compra segura" (verde escuro)
2. Resumo do produto com logo
3. Preços (parcelado e à vista)
4. Link "Precisa de ajuda?"
5. Link "Veja o contato do vendedor"
6. Informações de segurança e branding
7. Logo Cakto
8. Nome do vendedor
9. Badges de segurança

**Decisão de design:** Avaliar se sidebar é necessária ou se elementos podem ser integrados no card único

---

### 8. **Informações de Segurança e Branding** (IMPORTANTE)

**V1 - Completo:**
```
┌─────────────────────────────────────┐
│ [Cakto logo]                        │
│ Cakto está processando este         │
│ pagamento para o vendedor           │
│ Maria isabely                       │
│                                     │
│ [Badge] Compra 100% segura          │
│                                     │
│ Este site é protegido pelo          │
│ reCAPTCHA...                        │
│ • Política de Privacidade           │
│ • Termos de Serviço                 │
│ • Parcelamento com juros            │
└─────────────────────────────────────┘
```

**V2 - Simplificado:**
```
┌─────────────────────────────────────┐
│ Compra 100% segura                  │
│                                     │
│ Este site é protegido pelo          │
│ reCAPTCHA...                        │
└─────────────────────────────────────┘
```

**Faltando na V2:**
- ❌ Logo Cakto
- ❌ Nome do vendedor
- ❌ Texto "Cakto está processando este pagamento para..."
- ❌ Badge visual "Compra 100% segura"
- ❌ Links para Política de Privacidade
- ❌ Links para Termos de Serviço
- ❌ Informação sobre parcelamento com juros

**Impacto:** ⚠️ ALTO - Credibilidade e transparência reduzidas

---

### 9. **Country Selector** (NOVO NA V2)

**V1 - Ausente**

**V2 - Presente:**
```
┌─────────────────────────────────────┐
│                    [🇧🇷 Brasil ▾]  │
└─────────────────────────────────────┘
```

**Status:** ✅ Implementado na V2 (melhoria)

**Nota:** Verificar se está funcionalmente completo e com todos os países necessários

---

### 10. **Informações Contextuais de Pagamento** (NOVO NA V2)

**V1 - Ausente**

**V2 - Presente:**
```
┌─────────────────────────────────────┐
│ [✓] Liberação imediata              │
│ [✓] É simples, só usar o aplicativo │
│     de seu banco para pagar Pix     │
└─────────────────────────────────────┘
```

**Status:** ✅ Implementado na V2 (melhoria)

**Nota:** Verificar se muda dinamicamente baseado no método de pagamento selecionado

---

## 📁 Mapeamento de Componentes

### Componentes Existentes na V2

| Componente | Arquivo | Status |
|------------|---------|--------|
| PaymentMethods | `payment-methods.tsx` | ✅ Completo |
| CreditCardForm | `CreditCardForm.tsx` | ✅ Completo |
| PixPayment | `PixPayment.tsx` | ✅ Completo |
| PixAutoPayment | `PixAutoPayment.tsx` | ✅ Completo |
| BoletoPayment | `BoletoPayment.tsx` | ✅ Completo |
| CouponFormNew | `CouponFormNew.tsx` | ✅ Completo |
| TextField | `text-field.tsx` | ✅ Completo |
| Card | `card-form.tsx` | ✅ Completo |
| Button | `button-form.tsx` | ✅ Completo |

### Componentes Faltantes na V2

| Componente | Prioridade | Descrição |
|------------|------------|-----------|
| **ProductInfoCard** | 🔴 ALTA | Card com info do produto no topo |
| **OrderBumps** | 🔴 ALTA | Seção de ofertas limitadas |
| **OrderBumpItem** | 🔴 ALTA | Item individual de order bump |
| **SecurePurchaseSidebar** | 🟡 MÉDIA | Sidebar lateral (ou integrar) |
| **SellerInfo** | 🟡 MÉDIA | Informações do vendedor |
| **SecurityBadges** | 🟡 MÉDIA | Badges de segurança |
| **DataPrivacyTooltip** | 🟡 MÉDIA | Tooltip "Porque pedimos esse dado?" |
| **FormFieldIcon** | 🟢 BAIXA | Ícones nos campos de formulário |
| **SectionIcon** | 🟢 BAIXA | Ícones nos títulos de seção |

---

## 🗂️ Estrutura de Dados Necessária

### 1. Product Information

```typescript
interface ProductInfo {
  id: string;
  name: string;
  logo?: string;
  originalPrice?: number;
  cashPrice: number;
  installments?: {
    quantity: number;
    value: number;
  };
}
```

**Fonte de dados:** Já disponível via `useCheckout().offer`

**Status:** ✅ Dados disponíveis, componente faltante

---

### 2. Order Bumps

```typescript
interface OrderBump {
  id: string;
  name: string;
  description?: string;
  price: number;
  image?: string;
  selected: boolean;
}

interface OrderBumpsData {
  title: string; // "Ofertas limitadas"
  items: OrderBump[];
}
```

**Fonte de dados:** ❓ Verificar se API retorna order bumps

**Status:** ⚠️ Verificar disponibilidade na API

---

### 3. Seller Information

```typescript
interface SellerInfo {
  name: string;
  contactUrl?: string;
  supportUrl?: string;
}
```

**Fonte de dados:** ❓ Verificar se API retorna informações do vendedor

**Status:** ⚠️ Verificar disponibilidade na API

---

### 4. Security & Legal Links

```typescript
interface SecurityInfo {
  privacyPolicyUrl: string;
  termsOfServiceUrl: string;
  hasInstallmentFees: boolean;
  recaptchaEnabled: boolean;
}
```

**Fonte de dados:** Configuração estática ou API

**Status:** ⚠️ Definir fonte de dados

---

## 🎨 Componentes de UI Necessários

### 1. Icons Package

**Atualmente usando:** `@heroicons/react`

**Ícones necessários adicionais:**
- User icon (para Nome)
- Envelope icon (para Email)
- Document icon (para CPF/CNPJ)
- Phone icon (para Celular)
- Payment icon (para seção Pagamento)
- Shield/Lock icon (para badges de segurança)

**Status:** ✅ Já disponíveis no Heroicons

---

### 2. Tooltip/Popover Component

**Necessário para:** "Porque pedimos esse dado?"

**Opções:**
1. Radix UI Tooltip
2. Headless UI Popover
3. Custom modal simples

**Status:** ⚠️ Não implementado

---

### 3. Checkbox Component (para Order Bumps)

**Arquivo:** Verificar se existe `checkbox-form.tsx`

**Status:** ✅ Existe `Checkbox` component

---

## 📊 Checklist de Implementação

### 🔴 Prioridade ALTA (Impacto Crítico)

- [ ] **ProductInfoCard Component**
  - [ ] Criar componente `/components/checkout/product-info-card.tsx`
  - [ ] Exibir logo do produto
  - [ ] Exibir nome do produto
  - [ ] Exibir preço original (riscado)
  - [ ] Exibir preço parcelado
  - [ ] Exibir preço à vista
  - [ ] Integrar com `useCheckout().offer`
  - [ ] Adicionar ao layout principal

- [ ] **Order Bumps Section**
  - [ ] Verificar API para dados de order bumps
  - [ ] Criar componente `/components/checkout/order-bumps.tsx`
  - [ ] Criar componente `/components/checkout/order-bump-item.tsx`
  - [ ] Implementar checkbox de seleção
  - [ ] Atualizar total dinamicamente
  - [ ] Adicionar ao layout entre Pagamento e Resumo

- [ ] **Security & Branding Footer**
  - [ ] Criar componente `/components/checkout/security-footer.tsx`
  - [ ] Adicionar logo Cakto
  - [ ] Adicionar nome do vendedor
  - [ ] Adicionar badges de segurança
  - [ ] Adicionar links legais
  - [ ] Adicionar informação de parcelamento

### 🟡 Prioridade MÉDIA (Impacto Importante)

- [ ] **Form Field Icons**
  - [ ] Atualizar `text-field.tsx` para suportar ícone à esquerda
  - [ ] Adicionar ícones nos campos de formulário
  - [ ] Garantir acessibilidade

- [ ] **Data Privacy Tooltip**
  - [ ] Criar componente de tooltip/popover
  - [ ] Adicionar link "Porque pedimos esse dado?"
  - [ ] Criar conteúdo explicativo
  - [ ] Posicionar abaixo do campo Celular

- [ ] **Enhanced Order Summary**
  - [ ] Adicionar exibição de order bumps no resumo
  - [ ] Adicionar subtotal
  - [ ] Adicionar opção de mostrar parcelamento
  - [ ] Melhorar layout visual

- [ ] **Sidebar "Compra Segura"** (Opcional)
  - [ ] Avaliar necessidade de sidebar
  - [ ] Se sim: criar layout de 2 colunas
  - [ ] Se não: integrar elementos no card único
  - [ ] Criar componente sidebar se necessário

### 🟢 Prioridade BAIXA (Nice to Have)

- [ ] **Section Icons**
  - [ ] Adicionar ícone na seção "Seus dados"
  - [ ] Adicionar ícone na seção "Forma de Pagamento"

- [ ] **Animation & Transitions**
  - [ ] Animar adição de order bumps
  - [ ] Animar mudanças no resumo do pedido
  - [ ] Transições suaves entre métodos de pagamento

---

## 🔍 Análise de Layout

### Layout V1 (2 colunas)

```
┌───────────────────────┬─────────────┐
│                       │             │
│   Main Content        │  Sidebar    │
│   - Product Info      │  - Product  │
│   - Seus dados        │  - Price    │
│   - Pagamento         │  - Help     │
│   - Order Bumps       │  - Seller   │
│   - Resumo            │  - Security │
│   - Button            │             │
│   - Security Footer   │             │
│                       │             │
└───────────────────────┴─────────────┘
```

**Vantagens:**
- Mais espaço para conteúdo
- Sidebar sempre visível
- Informações duplicadas (reforço)

**Desvantagens:**
- Menos mobile-friendly
- Informações duplicadas (redundância)
- Mais complexo de implementar

---

### Layout V2 (Card único)

```
┌─────────────────────────┐
│                         │
│   Single Card           │
│   - Country Selector    │
│   - Informações Contato │
│   - Forma Pagamento     │
│   - Resumo Pedido       │
│   - Button              │
│   - Security Info       │
│                         │
└─────────────────────────┘
```

**Vantagens:**
- Mobile-first
- Mais limpo e moderno
- Menos redundância

**Desvantagens:**
- Menos espaço para elementos
- Informações de segurança menos proeminentes
- Difícil encaixar order bumps

---

### 📋 Recomendação de Layout

**Opção 1: Manter Card Único + Expandir**
- Adicionar ProductInfoCard no topo do card
- Adicionar Order Bumps entre Pagamento e Resumo
- Expandir Security Footer no final
- Manter mobile-first

**Opção 2: Híbrido (Responsivo)**
- Desktop: 2 colunas (Main + Sidebar)
- Mobile/Tablet: Card único colapsado
- Melhor dos dois mundos

**Opção 3: Manter V2 Simples**
- Apenas adicionar elementos críticos
- Order Bumps e ProductInfo
- Manter layout clean
- Priorizar conversão sobre informação

---

## 🎯 Conclusão

### Elementos Críticos Faltantes (Must Have)

1. ✅ **ProductInfoCard** - Usuário precisa ver o que está comprando
2. ✅ **Order Bumps** - Oportunidade de aumentar ticket médio
3. ✅ **Security Footer Completo** - Aumentar confiança e credibilidade

### Elementos Importantes (Should Have)

4. ✅ **Form Field Icons** - Melhorar UX e visual
5. ✅ **Data Privacy Tooltip** - Aumentar transparência
6. ✅ **Enhanced Order Summary** - Informação completa

### Elementos Opcionais (Nice to Have)

7. ⚠️ **Sidebar** - Avaliar necessidade vs complexidade
8. ✅ **Section Icons** - Polimento visual

---

## 📝 Próximos Passos

1. **Verificar Disponibilidade de Dados**
   - [ ] Confirmar se API retorna order bumps
   - [ ] Confirmar se API retorna informações do vendedor
   - [ ] Verificar estrutura de dados de `offer`

2. **Priorizar Implementação**
   - Começar por elementos críticos
   - Validar com stakeholders
   - Implementar iterativamente

3. **Criar Prompt de Implementação**
   - Documento separado com instruções detalhadas
   - Ordem de implementação
   - Exemplos de código
   - Testes de validação

---

**Documento criado por:** AI Assistant  
**Revisão necessária:** Product Owner, Tech Lead  
**Próxima ação:** Criar prompt de implementação detalhado

