# Guia de Uso dos Contextos Migrados

## 🎯 Visão Geral

Este guia explica como usar os contextos migrados na Fase 3 no projeto Next.js 16.

## 📦 Contextos Disponíveis

### 1. CheckoutContext
Gerencia todo o estado e lógica de pagamento do checkout.

### 2. CheckoutModeContext
Gerencia o modo de visualização (preview vs produção).

### 3. NotificationContext
Gerencia notificações e mensagens para o usuário.

## 🚀 Setup Básico

### Estrutura de Providers

Os providers devem ser configurados em uma estrutura hierárquica. Exemplo em um Server Component:

```typescript
// app/[locale]/[id]/page.tsx
import { getCheckoutData } from "@/lib/api/checkout";
import { 
  CheckoutProvider, 
  CheckoutModeProvider,
  NotificationProvider 
} from "@/contexts";
import { CheckoutPage } from "./checkout-page";

export default async function Page({ 
  params 
}: { 
  params: { locale: string; id: string } 
}) {
  // Fetch data no servidor
  const checkoutData = await getCheckoutData(params.id);
  
  // Verificar se é preview mode
  const isPreview = params.id.includes('preview');
  
  return (
    <CheckoutModeProvider preview={isPreview}>
      <NotificationProvider>
        <CheckoutProvider 
          initialData={checkoutData} 
          checkoutId={params.id}
        >
          <CheckoutPage />
        </CheckoutProvider>
      </NotificationProvider>
    </CheckoutModeProvider>
  );
}
```

### Client Component que Usa os Contextos

```typescript
// app/[locale]/[id]/checkout-page.tsx
"use client";

import { 
  useCheckout, 
  useCheckoutMode, 
  useNotificationContext 
} from "@/contexts";

export function CheckoutPage() {
  const { 
    offer, 
    pay, 
    paying,
    paymentTabs,
    paymentMethod,
    setPaymentMethod 
  } = useCheckout();
  
  const { preview } = useCheckoutMode();
  const { notify } = useNotificationContext();
  
  const handlePayment = async () => {
    try {
      await pay({
        paymentMethod: paymentMethod?.type || 'credit_card',
        items: [{ id: offer?.id, quantity: 1 }],
        // ... outros campos
      });
      
      notify('Pagamento realizado!', 'success', 5000);
    } catch (error) {
      notify('Erro no pagamento', 'error', 5000);
    }
  };
  
  if (preview) {
    return <div>MODO PREVIEW</div>;
  }
  
  return (
    <div>
      <h1>{offer?.product?.name}</h1>
      <button onClick={handlePayment} disabled={paying}>
        {paying ? 'Processando...' : 'Pagar'}
      </button>
    </div>
  );
}
```

## 📚 API dos Contextos

### CheckoutContext

#### Props do Provider
```typescript
interface CheckoutProviderProps {
  children: ReactNode;
  initialData: ProductData;  // Dados do checkout vindos do SSR
  checkoutId: string;         // ID do checkout (ex: "abc123_xyz")
}
```

#### Hook: useCheckout()
```typescript
const {
  // Estado
  isFetching: boolean;
  offer?: ProductData;
  payments: Payment[];
  firstPayment: Payment | null;
  
  // Métodos de pagamento
  pay: (payload: PaymentPayload) => Promise<unknown>;
  paying: boolean;
  checkPayment: () => Promise<unknown>;
  checkingPayment: boolean;
  
  // Configuração
  paymentMethod: PaymentMethodOption | null;
  setPaymentMethod: (payment: PaymentMethodOption) => void;
  paymentTabs: PaymentTab[];
  
  // Parcelamento
  calcInstallments: unknown;
  setCalcInstallments: (installments: unknown) => void;
  canBePaidInInstallments: boolean;
  
  // Cupom
  couponData?: CouponData;
  validateCoupon: (coupon: string) => Promise<unknown>;
  isLoadingValidateCoupon: boolean;
  applyCouponValue: number;
  setApplyCouponValue: (number: number) => void;
  
  // Erros
  error: unknown;
  creditCardError: string;
  setCreditCardError: (error: string) => void;
  status: MutationStatus;
  
  // Outros
  setFirstPayment: (payload: Partial<Payment>) => void;
  setPayments: React.Dispatch<React.SetStateAction<Payment[]>>;
  setCountdownHeight: (height: number) => void;
  countdownHeight: number;
  method: PaymentMethod | null;
  fingerprint?: string;
  hasAnySubscription: boolean;
} = useCheckout();
```

#### Exemplo de Uso Completo

```typescript
"use client";

import { useCheckout } from "@/contexts";
import { useState } from "react";

export function PaymentForm() {
  const {
    offer,
    pay,
    paying,
    paymentMethod,
    setPaymentMethod,
    paymentTabs,
    validateCoupon,
    isLoadingValidateCoupon,
    creditCardError,
    canBePaidInInstallments,
  } = useCheckout();
  
  const [couponCode, setCouponCode] = useState("");
  
  const handleCouponValidation = async () => {
    try {
      await validateCoupon(couponCode);
      alert("Cupom válido!");
    } catch {
      alert("Cupom inválido");
    }
  };
  
  const handlePayment = async (formData: any) => {
    try {
      await pay({
        paymentMethod: paymentMethod?.type || 'credit_card',
        items: [
          {
            id: offer?.id || '',
            quantity: 1,
          }
        ],
        ...formData,
      });
    } catch (error) {
      console.error("Erro no pagamento:", error);
    }
  };
  
  return (
    <div>
      {/* Tabs de métodos de pagamento */}
      <div>
        {paymentTabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setPaymentMethod({ type: tab.id, name: tab.label })}
          >
            <tab.Icon className="w-6 h-6" />
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* Campo de cupom */}
      <div>
        <input
          value={couponCode}
          onChange={(e) => setCouponCode(e.target.value)}
          placeholder="Código do cupom"
        />
        <button 
          onClick={handleCouponValidation}
          disabled={isLoadingValidateCoupon}
        >
          Validar
        </button>
      </div>
      
      {/* Formulário de pagamento */}
      {paymentMethod && (
        <div>
          {/* Renderizar formulário baseado no método selecionado */}
          {canBePaidInInstallments && (
            <p>Parcele em até 12x</p>
          )}
        </div>
      )}
      
      {/* Mensagem de erro */}
      {creditCardError && (
        <div className="text-red-500">{creditCardError}</div>
      )}
      
      {/* Botão de pagamento */}
      <button onClick={() => handlePayment({})} disabled={paying}>
        {paying ? "Processando..." : "Finalizar Pagamento"}
      </button>
    </div>
  );
}
```

### CheckoutModeContext

#### Props do Provider
```typescript
interface CheckoutModeProviderProps {
  children: ReactNode;
  preview: boolean;  // true = modo preview, false = modo produção
}
```

#### Hook: useCheckoutMode()
```typescript
const {
  preview: boolean;  // Indica se está em modo preview
} = useCheckoutMode();
```

#### Exemplo de Uso

```typescript
"use client";

import { useCheckoutMode } from "@/contexts";

export function PreviewBanner() {
  const { preview } = useCheckoutMode();
  
  if (!preview) return null;
  
  return (
    <div className="bg-yellow-500 text-black p-4">
      ⚠️ MODO PREVIEW - Este checkout não processará pagamentos reais
    </div>
  );
}
```

### NotificationContext

#### Props do Provider
```typescript
interface NotificationProviderProps {
  children: ReactNode;
}
```

#### Hook: useNotificationContext()
```typescript
const {
  notifications: Notification[];
  notify: (message: string, type: CheckoutComponentNotificationType, exibitionTime: number) => void;
  dismiss: (id: string) => void;
} = useNotificationContext();
```

#### Tipos de Notificação
```typescript
type CheckoutComponentNotificationType = 
  | "success"   // Verde, sucesso
  | "error"     // Vermelho, erro
  | "warning"   // Amarelo, aviso
  | "info";     // Azul, informação
```

#### Exemplo de Uso

```typescript
"use client";

import { useNotificationContext } from "@/contexts";

export function NotificationDemo() {
  const { notifications, notify, dismiss } = useNotificationContext();
  
  const showSuccess = () => {
    notify("Operação realizada com sucesso!", "success", 5000);
  };
  
  const showError = () => {
    notify("Ocorreu um erro!", "error", 5000);
  };
  
  return (
    <div>
      <button onClick={showSuccess}>Mostrar Sucesso</button>
      <button onClick={showError}>Mostrar Erro</button>
      
      {/* Lista de notificações */}
      <div className="fixed top-4 right-4 space-y-2">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={`p-4 rounded ${
              notification.type === "success" ? "bg-green-500" :
              notification.type === "error" ? "bg-red-500" :
              notification.type === "warning" ? "bg-yellow-500" :
              "bg-blue-500"
            }`}
          >
            <p>{notification.message}</p>
            <button onClick={() => dismiss(notification.id)}>
              Fechar
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
```

## 🔒 Boas Práticas

### 1. Sempre Use os Hooks Customizados

❌ **Errado:**
```typescript
import { useContext } from "react";
import { CheckoutContext } from "@/contexts/checkout-context";

const context = useContext(CheckoutContext);
```

✅ **Correto:**
```typescript
import { useCheckout } from "@/contexts";

const context = useCheckout();
```

### 2. Configure Providers no Nível Adequado

- `CheckoutModeProvider`: No nível da página ou layout
- `NotificationProvider`: No nível da página ou layout
- `CheckoutProvider`: No nível da página específica do checkout

### 3. Tratamento de Erros

Sempre trate erros ao usar funções assíncronas:

```typescript
try {
  await pay(payload);
  notify("Pagamento realizado!", "success", 5000);
} catch (error) {
  // O erro já é tratado internamente, mas você pode adicionar lógica extra
  notify("Erro ao processar pagamento", "error", 5000);
}
```

### 4. Loading States

Use os estados de loading fornecidos pelos contextos:

```typescript
const { paying, checkingPayment, isLoadingValidateCoupon } = useCheckout();

<button disabled={paying}>
  {paying ? "Processando..." : "Pagar"}
</button>
```

## 🐛 Troubleshooting

### Erro: "useCheckout must be used within CheckoutProvider"

**Causa**: Tentando usar o hook fora do provider.

**Solução**: Certifique-se de que o componente está dentro do `<CheckoutProvider>`:

```typescript
// page.tsx
<CheckoutProvider initialData={data} checkoutId={id}>
  <YourComponent /> {/* ✅ Pode usar useCheckout aqui */}
</CheckoutProvider>
```

### Erro: "initialData is undefined"

**Causa**: Dados não foram carregados no servidor antes de passar para o provider.

**Solução**: Sempre aguarde o fetch no Server Component:

```typescript
export default async function Page({ params }: Props) {
  const data = await getCheckoutData(params.id); // ✅ Await aqui
  
  return (
    <CheckoutProvider initialData={data} checkoutId={params.id}>
      {children}
    </CheckoutProvider>
  );
}
```

### Erro: "Cannot read property 'type' of null"

**Causa**: Tentando acessar `paymentMethod.type` quando `paymentMethod` é null.

**Solução**: Sempre verifique null antes de acessar:

```typescript
const { paymentMethod } = useCheckout();

// ✅ Correto
if (paymentMethod?.type === 'credit_card') {
  // ...
}

// ❌ Errado
if (paymentMethod.type === 'credit_card') {
  // ...
}
```

## 📝 Notas Importantes

### Placeholders Temporários

Alguns recursos ainda usam placeholders:

1. **Ícones de Pagamento**: Usando `CreditCardIcon` do Heroicons temporariamente
2. **Formulários de Pagamento**: Componentes simplificados temporários
3. **Pixel Hooks**: Apenas `console.log` por enquanto

Estes serão implementados completamente nas próximas fases.

### SSR e Client Components

- Os **providers** são Client Components (têm `"use client"`)
- Os **dados iniciais** vêm do Server Component
- A **lógica de negócio** roda no cliente após hidratação

### Performance

Os contextos usam `useMemo` e `useCallback` para otimizar re-renders. Evite criar objetos novos desnecessariamente nas props.

## 🎓 Exemplos Completos

Veja mais exemplos na pasta `_docs/examples/` (a ser criada na próxima fase).

## 📞 Suporte

Para dúvidas sobre os contextos migrados, consulte:
- Este guia
- `MIGRACAO_FASE_3_CONCLUIDA.md` (detalhes técnicos)
- Código original em `_docs/cakto-checkout/src/contexts/`

