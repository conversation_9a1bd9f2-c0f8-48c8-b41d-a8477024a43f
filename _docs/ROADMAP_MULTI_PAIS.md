# Roadmap: Sistema Multi-<PERSON><PERSON>

**Data:** 2025-11-12  
**Versão:** 1.0  
**Status:** 📋 Planejamento

---

## 🎯 Visão Geral

Transformar o checkout Cakto em uma plataforma verdadeiramente multi-país, suportando 7 países da América Latina + Estados Unidos.

---

## 📊 Status por País

| País | Código | Moeda | Config | Components | i18n | API | Status |
|------|--------|-------|--------|------------|------|-----|--------|
| 🇧🇷 Brasil | BR | BRL | ✅ | ✅ | ✅ | ✅ | **100%** |
| 🇲🇽 México | MX | MXN | ✅ | ❌ | ❌ | ❌ | **30%** |
| 🇦🇷 Argentina | AR | ARS | ✅ | ❌ | ❌ | ❌ | **15%** |
| 🇨🇱 Chile | CL | CLP | ✅ | ❌ | ❌ | ❌ | **15%** |
| 🇨🇴 Colômbia | CO | COP | ✅ | ❌ | ❌ | ❌ | **15%** |
| 🇵🇪 Peru | PE | PEN | ✅ | ❌ | ❌ | ❌ | **15%** |
| 🇺🇸 EUA | US | USD | ✅ | ✅ | ✅ | ✅ | **100%** |

---

## 🗺️ Roadmap de Implementação

### ✅ Fase 0: Infraestrutura (CONCLUÍDA)

**Tempo:** 0h (já existe)

- ✅ CountryProvider com detecção de país
- ✅ CountrySelector para mudança manual
- ✅ COUNTRIES config com 7 países
- ✅ Types para países e documentos
- ✅ Sistema de i18n com 3 idiomas
- ✅ Máscaras por país (telefone, documento, CEP)
- ✅ Filtro de métodos de pagamento por país

---

### 🔴 Fase 1: México - OXXO + SPEI (CRÍTICO)

**Prioridade:** 🔴 MÁXIMA  
**Tempo:** 11-12h  
**Impacto:** Mercado de 130M habitantes

#### Backend (3h)
- [ ] Endpoint filtra métodos por país
- [ ] Integração com EBANX/Stripe para OXXO
- [ ] Integração com EBANX/Stripe para SPEI
- [ ] Webhook para confirmação OXXO
- [ ] Webhook para confirmação SPEI

#### Frontend (8h)
- [ ] OxxoIcon (30min)
- [ ] SpeiIcon (30min)
- [ ] OxxoForm (1h)
- [ ] SpeiForm (1h)
- [ ] OxxoPayment (2h)
- [ ] SpeiPayment (2h)
- [ ] Traduções es-MX (1h)
- [ ] Testes (1h)

#### Arquivos
```
CRIAR:
- components/icons/payment/OxxoIcon.tsx
- components/icons/payment/SpeiIcon.tsx
- components/payments/OxxoForm.tsx
- components/payments/SpeiForm.tsx
- components/payments/waiting/OxxoPayment.tsx
- components/payments/waiting/SpeiPayment.tsx

ATUALIZAR:
- contexts/checkout-context.tsx (mapIcons, mapForms)
- components/payments/WaitingPayment.tsx (casos MX)
- types/index.ts (PaymentMethod + Payment)
- lib/i18n/messages/*.json (traduções)
```

---

### 🟠 Fase 2: Argentina - Rapipago + Pagofacil (ALTO)

**Prioridade:** 🟠 ALTA  
**Tempo:** 8-10h  
**Impacto:** Mercado de 45M habitantes

#### Métodos
- [ ] Rapipago (similar ao OXXO)
- [ ] Pagofacil (similar ao OXXO)

#### Componentes
- [ ] RapipagoIcon + RapipagoForm + RapipagoPayment
- [ ] PagofacilIcon + PagofacilForm + PagofacilPayment

**Referência:** [EBANX Argentina](https://www.ebanx.com/en/latin-america/argentina/)

---

### 🟡 Fase 3: Colômbia - PSE + Efecty + Baloto (MÉDIO)

**Prioridade:** 🟡 MÉDIA  
**Tempo:** 10-12h  
**Impacto:** Mercado de 51M habitantes

#### Métodos
- [ ] PSE (transferência bancária - COMPLEXO)
- [ ] Efecty (cash)
- [ ] Baloto (cash)

**Nota:** PSE requer seleção de banco, é mais complexo

---

### 🟢 Fase 4: Chile - Servipag + Multicaja (BAIXO)

**Prioridade:** 🟢 BAIXA  
**Tempo:** 8-10h  
**Impacto:** Mercado de 19M habitantes

---

### 🟢 Fase 5: Peru - PagoEfectivo + SafetyPay (BAIXO)

**Prioridade:** 🟢 BAIXA  
**Tempo:** 8-10h  
**Impacto:** Mercado de 33M habitantes

---

## 📅 Timeline Proposta

### Mês 1: México (Foco Total)
- **Semana 1-2:** Backend + Infraestrutura API
- **Semana 3:** Frontend Components
- **Semana 4:** Testes + Deploy

### Mês 2: Argentina + Colômbia
- **Semana 1-2:** Argentina
- **Semana 3-4:** Colômbia (PSE é complexo)

### Mês 3: Chile + Peru
- **Semana 1-2:** Chile
- **Semana 3-4:** Peru + Polish geral

---

## 🔍 Análise de Impacto

### Potencial de Mercado

| País | População | PIB/capita | Prioridade | ROI Estimado |
|------|-----------|------------|------------|--------------|
| 🇧🇷 Brasil | 215M | $9,6k | ✅ Feito | - |
| 🇲🇽 México | 130M | $11,5k | 🔴 Alta | ⭐⭐⭐⭐⭐ |
| 🇨🇴 Colômbia | 51M | $6,4k | 🟡 Média | ⭐⭐⭐ |
| 🇦🇷 Argentina | 45M | $10,6k | 🟠 Alta | ⭐⭐⭐⭐ |
| 🇵🇪 Peru | 33M | $7,1k | 🟢 Baixa | ⭐⭐ |
| 🇨🇱 Chile | 19M | $15,4k | 🟢 Baixa | ⭐⭐⭐ |
| 🇺🇸 EUA | 330M | $70k | ✅ Feito | - |

**Conclusão:** México deve ser PRIORIDADE 1 (maior mercado + alto PIB)

---

## 🏗️ Arquitetura Técnica Recomendada

### Detecção de País (Hybrid Approach)

```
1. Server-Side (Next.js)
   └─> Detecta país via headers (CF-IPCountry)
   └─> Passa para API como query param
   └─> Retorna ao cliente via initialData

2. Client-Side (React)
   └─> CountryProvider recebe initialCountry
   └─> Permite mudança manual via CountrySelector
   └─> Persiste seleção no localStorage
   └─> Revalida com API quando muda
```

### Fluxo de Dados

```
┌─────────────────────────────────────────────┐
│ User Access                                 │
│ /es/mcrd948                                 │
└─────────────────────────────────────────────┘
              ↓
┌─────────────────────────────────────────────┐
│ Next.js Server (page.tsx)                   │
│ - Headers: CF-IPCountry = "MX"             │
│ - Locale: "es"                             │
│ - Country: "MX" (header ou locale default) │
└─────────────────────────────────────────────┘
              ↓
┌─────────────────────────────────────────────┐
│ API Request                                 │
│ GET /checkout/mcrd948/?country=MX          │
└─────────────────────────────────────────────┘
              ↓
┌─────────────────────────────────────────────┐
│ API Response                                │
│ {                                           │
│   detectedCountry: "MX",                   │
│   product: {                               │
│     paymentMethods: [                      │
│       {type: "oxxo", name: "OXXO"},       │
│       {type: "spei", name: "SPEI"},       │
│       {type: "credit_card", name: "..."}  │
│     ]                                      │
│   }                                        │
│ }                                          │
└─────────────────────────────────────────────┘
              ↓
┌─────────────────────────────────────────────┐
│ CheckoutClient                              │
│ <CountryProvider initialCountry="MX">      │
│   <CheckoutProvider>                        │
│     paymentTabs = [                        │
│       {id: "oxxo", Icon: OxxoIcon, ...},  │
│       {id: "spei", Icon: SpeiIcon, ...},  │
│     ]                                      │
└─────────────────────────────────────────────┘
              ↓
┌─────────────────────────────────────────────┐
│ PaymentMethods Renders                      │
│ [OXXO] [SPEI] [Tarjeta]                   │
└─────────────────────────────────────────────┘
```

---

## 🔧 Mudanças Necessárias

### 1. API (Backend)

**Arquivo:** `backend/api/checkout.py` (ou similar)

```python
PAYMENT_METHODS_BY_COUNTRY = {
    "BR": ["pix", "pix_auto", "boleto", "credit_card", "picpay", 
           "googlepay", "applepay", "openfinance_nubank"],
    "MX": ["oxxo", "spei", "credit_card", "googlepay", "applepay"],
    "AR": ["rapipago", "pagofacil", "credit_card"],
    "CL": ["servipag", "multicaja", "credit_card"],
    "CO": ["pse", "efecty", "baloto", "credit_card"],
    "PE": ["pagoefectivo", "safetypay", "credit_card"],
    "US": ["credit_card", "googlepay", "applepay"],
}

@router.get("/api/product/checkout/{offer_id}/")
async def get_checkout(
    request: Request,
    offer_id: str,
    country: Optional[str] = None,
):
    # Detectar país
    detected_country = (
        country or 
        request.headers.get("CF-IPCountry") or 
        request.headers.get("X-Vercel-IP-Country") or 
        "BR"
    )
    
    # Buscar offer
    offer = await get_offer(offer_id)
    
    # Filtrar métodos permitidos no país
    allowed_methods = PAYMENT_METHODS_BY_COUNTRY.get(detected_country, ["credit_card"])
    
    # Filtrar métodos do produto que são permitidos no país
    product_methods = offer.product.payment_methods
    filtered_methods = [
        method for method in product_methods
        if method["type"] in allowed_methods
    ]
    
    # Se nenhum método compatível, usar credit_card default
    if not filtered_methods:
        filtered_methods = [{"type": "credit_card", "name": "Credit Card"}]
    
    return {
        "detectedCountry": detected_country,
        "product": {
            **offer.product,
            "paymentMethods": filtered_methods,
        },
        # ... resto da response
    }
```

### 2. Frontend - Page (Server Component)

**Arquivo:** `/apps/web/src/app/[locale]/[id]/page.tsx`

```typescript
import { headers } from "next/headers";

async function detectCountryFromHeaders(): Promise<string> {
  const headersList = await headers();
  
  return (
    headersList.get("cf-ipcountry") ||
    headersList.get("x-vercel-ip-country") ||
    "BR"
  );
}

export default async function CheckoutPage({ params, searchParams }) {
  const { locale, id } = await params;
  const { affiliate } = await searchParams;
  
  // Detectar país
  const country = await detectCountryFromHeaders();
  
  // Buscar dados COM país
  const checkoutData = await getCheckoutData(id, affiliate, country);
  
  return (
    <CheckoutClient 
      initialData={checkoutData}
      checkoutId={id}
      initialCountry={checkoutData.detectedCountry}
    />
  );
}
```

### 3. Frontend - getCheckoutData

**Arquivo:** `/apps/web/src/lib/api/checkout.ts`

```typescript
export async function getCheckoutData(
  checkoutId: string,
  affiliate?: string,
  country?: string
): Promise<CheckoutData> {
  const params = new URLSearchParams({
    checkoutUrl: `${window?.location?.origin || ""}/${checkoutId}`,
  });

  if (affiliate) {
    params.append("affiliate", affiliate);
  }

  if (country) {
    params.append("country", country);
  }

  const url = `${API_BASE_URL}/api/product/checkout/${checkoutId}/?${params}`;

  const response = await fetch(url, {
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch checkout: ${response.statusText}`);
  }

  return response.json();
}
```

### 4. Frontend - CountryProvider

**Arquivo:** `/apps/web/src/contexts/country-context.tsx`

```typescript
export function CountryProvider({ 
  children,
  initialCountry, // Novo prop do servidor
}: { 
  children: ReactNode;
  initialCountry?: CountryCode;
}) {
  const [userCountryData, setUserCountryData] = useState<UserCountryData>(() => {
    // 1. Priorizar initialCountry do servidor (SSR)
    if (initialCountry && COUNTRIES[initialCountry]) {
      return {
        countryCode: initialCountry,
        isManualSelection: false,
        source: "server",
      };
    }
    
    // 2. Tentar localStorage (seleção anterior)
    if (typeof window !== "undefined") {
      const stored = localStorage.getItem("user_country");
      if (stored) {
        try {
          const parsed = JSON.parse(stored);
          if (COUNTRIES[parsed.countryCode]) {
            return { ...parsed, source: "localStorage" };
          }
        } catch {}
      }
    }
    
    // 3. Fallback para idioma
    const defaultCountry = LANGUAGE_TO_DEFAULT_COUNTRY[locale] || "BR";
    return {
      countryCode: defaultCountry,
      isManualSelection: false,
      source: "fallback",
    };
  });
  
  // ... resto do provider
}
```

---

## 📋 Sprints Detalhados

### Sprint 1: México OXXO (Semana 1)

**Objetivo:** Suportar pagamento em dinheiro via OXXO

**Tasks:**
1. [ ] Backend: Integrar com EBANX OXXO API
2. [ ] Backend: Endpoint POST /payment/oxxo
3. [ ] Backend: Webhook confirmação OXXO
4. [ ] Frontend: OxxoIcon componente
5. [ ] Frontend: OxxoForm componente
6. [ ] Frontend: OxxoPayment componente
7. [ ] Frontend: Traduções OXXO (es/pt/en)
8. [ ] Frontend: Adicionar no mapIcons/mapForms
9. [ ] Frontend: Atualizar WaitingPayment
10. [ ] Frontend: Atualizar types
11. [ ] Testes: Fluxo completo OXXO
12. [ ] Deploy: Staging → Production

**Entregável:** Usuários do México podem pagar com OXXO

---

### Sprint 2: México SPEI (Semana 2)

**Objetivo:** Suportar transferência bancária via SPEI

**Tasks:**
1. [ ] Backend: Integrar com EBANX SPEI API
2. [ ] Backend: Endpoint POST /payment/spei
3. [ ] Backend: Webhook confirmação SPEI
4. [ ] Frontend: SpeiIcon componente
5. [ ] Frontend: SpeiForm componente
6. [ ] Frontend: SpeiPayment componente
7. [ ] Frontend: Traduções SPEI (es/pt/en)
8. [ ] Frontend: Adicionar no mapIcons/mapForms
9. [ ] Frontend: Atualizar WaitingPayment
10. [ ] Frontend: Atualizar types
11. [ ] Testes: Fluxo completo SPEI
12. [ ] Deploy: Staging → Production

**Entregável:** México 100% funcional (OXXO + SPEI + Card)

---

### Sprint 3: Filtro de Métodos por País (Semana 3)

**Objetivo:** API retorna métodos corretos automaticamente

**Tasks:**
1. [ ] Backend: Função filter_by_country()
2. [ ] Backend: Detectar país via headers
3. [ ] Backend: Testes unitários de filtro
4. [ ] Frontend: Passar país na API call
5. [ ] Frontend: Validar resposta filtrada
6. [ ] E2E: Testar com VPN de cada país
7. [ ] Documentar fluxo completo

**Entregável:** Sistema multi-país orquestrado

---

### Sprint 4-6: Outros Países

Repetir processo para AR, CO, CL, PE seguindo o template do México

---

## 🧪 Estratégia de Testes

### 1. Testes Unitários

```typescript
describe("Mexico Payment Methods", () => {
  it("renders OXXO form correctly", () => {
    render(<OxxoForm />);
    expect(screen.getByText(/OXXO/i)).toBeInTheDocument();
  });

  it("copies OXXO code to clipboard", async () => {
    render(<OxxoPayment voucher={mockVoucher} />);
    const copyBtn = screen.getByRole("button", { name: /copiar/i });
    await userEvent.click(copyBtn);
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(mockVoucher.code);
  });
});
```

### 2. Testes de Integração

```bash
# Simular request do México
curl -H "CF-IPCountry: MX" \
  "https://api.cakto.com.br/api/product/checkout/mcrd948/"

# Esperar response com métodos filtrados
{
  "detectedCountry": "MX",
  "product": {
    "paymentMethods": [
      {"type": "oxxo", "name": "OXXO"},
      {"type": "spei", "name": "SPEI"}
    ]
  }
}
```

### 3. Testes E2E (Playwright/Cypress)

```typescript
test("Mexico user sees OXXO payment option", async ({ page }) => {
  // Simular usuário do México
  await page.setExtraHTTPHeaders({
    "CF-IPCountry": "MX",
  });
  
  await page.goto("/es/mcrd948");
  
  // Verificar métodos visíveis
  await expect(page.getByText("OXXO")).toBeVisible();
  await expect(page.getByText("SPEI")).toBeVisible();
  
  // Verificar métodos do Brasil NÃO visíveis
  await expect(page.getByText("PIX")).not.toBeVisible();
  await expect(page.getByText("Boleto")).not.toBeVisible();
});
```

---

## 📊 Métricas de Sucesso

### KPIs Técnicos
- ✅ 0 erros de detecção de país
- ✅ 100% dos métodos corretos por país
- ✅ < 100ms overhead de filtro
- ✅ 0 erros de type safety

### KPIs de Negócio
- 📈 Conversão no México >= Conversão no Brasil
- 📈 Ticket médio MX >= Ticket médio BR
- 📈 Taxa de abandono MX < 30%
- 📈 Tempo de checkout MX < 2min

---

## 🚨 Riscos e Mitigações

### Risco 1: API não filtra corretamente
**Mitigação:** Frontend faz filtro duplo (já implementado)

### Risco 2: Webhooks não chegam
**Mitigação:** Polling manual + retry logic

### Risco 3: Tradução incorreta
**Mitigação:** Revisão com nativo + testes A/B

### Risco 4: Método não disponível
**Mitigação:** Fallback para credit_card sempre

---

## 📚 Referências Úteis

### EBANX Documentation
- [Mexico Payment Methods](https://www.ebanx.com/en/latin-america/mexico/)
- [OXXO Integration](https://developers.ebanx.com/guides/oxxo)
- [SPEI Integration](https://developers.ebanx.com/guides/spei)

### Next.js
- [Internationalization](https://nextjs.org/docs/app/building-your-application/routing/internationalization)
- [Headers](https://nextjs.org/docs/app/api-reference/functions/headers)
- [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components)

### React
- [Rules of Hooks](https://react.dev/reference/rules/rules-of-hooks)
- [useContext](https://react.dev/reference/react/useContext)

---

## ✅ Definition of Done

Um país é considerado "completo" quando:

- ✅ Todos os métodos de pagamento estão implementados
- ✅ Formulários específicos criados
- ✅ Telas de waiting payment criadas
- ✅ Ícones de cada método criados
- ✅ Traduções em 3 idiomas (pt, en, es)
- ✅ Backend processa pagamentos
- ✅ Webhooks configurados
- ✅ Testes E2E passando
- ✅ Zero erros no console
- ✅ Deploy em produção
- ✅ Monitoramento ativo

---

## 🎯 Próximo Passo Imediato

**AÇÃO NECESSÁRIA:**

1. **Validar com Backend:**
   - [ ] API consegue detectar país via headers?
   - [ ] API consegue filtrar métodos por país?
   - [ ] Qual gateway usar? (EBANX/Stripe/Próprio)

2. **Decidir Priorização:**
   - [ ] Começar com México (OXXO)?
   - [ ] Ou todos os países ao mesmo tempo?

3. **Alinhar com Time:**
   - [ ] Backend tem capacidade para implementar?
   - [ ] Prazo desejado?
   - [ ] Orçamento para gateways?

**Depois de validar, seguir:** `IMPLEMENTACAO_MEXICO_COMPLETA.md`

---

**Conclusão:** A infraestrutura está pronta. Falta apenas criar os componentes específicos de cada método de pagamento e garantir que a API filtra corretamente por país. Começar pelo México (maior mercado) é a recomendação estratégica.

