# Implementar Métodos de Pagamento do México

**Objetivo:** Adicionar suporte completo para OXXO e SPEI (métodos de pagamento do México)  
**Tempo estimado:** 8-10 horas  
**Prioridade:** CRÍTICA

---

## 🎯 Contexto Rápido

O sistema já tem:
- ✅ CountryProvider com México configurado
- ✅ Types definidos para OXXO e SPEI
- ✅ Sistema de i18n (pt, en, es)
- ✅ Filtro de métodos por país

Falta apenas:
- ❌ Componentes de formulário (OxxoForm, SpeiForm)
- ❌ Componentes de waiting payment (OxxoPayment, SpeiPayment)
- ❌ Ícones (OxxoIcon, SpeiIcon)
- ❌ Traduções específicas do México

---

## 📦 PASSO 1: <PERSON><PERSON><PERSON> Ícon<PERSON>

### 1.1 OxxoIcon

**Arquivo:** `/apps/web/src/components/icons/payment/OxxoIcon.tsx`

```typescript
export const OxxoIcon = ({ className }: { className?: string }) => (
  <svg 
    className={className} 
    viewBox="0 0 24 24" 
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="24" height="24" rx="4" fill="#FFDD00"/>
    <text 
      x="12" 
      y="15" 
      textAnchor="middle" 
      fontWeight="bold" 
      fontSize="8" 
      fill="#EC1C24"
    >
      OXXO
    </text>
  </svg>
);
```

### 1.2 SpeiIcon

**Arquivo:** `/apps/web/src/components/icons/payment/SpeiIcon.tsx`

```typescript
export const SpeiIcon = ({ className }: { className?: string }) => (
  <svg 
    className={className} 
    viewBox="0 0 24 24" 
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="24" height="24" rx="4" fill="#0052CC"/>
    <path
      d="M8 8h8M8 12h8M8 16h8"
      stroke="white"
      strokeWidth="2"
      strokeLinecap="round"
    />
    <circle cx="5" cy="8" r="1" fill="white"/>
    <circle cx="5" cy="12" r="1" fill="white"/>
    <circle cx="5" cy="16" r="1" fill="white"/>
  </svg>
);
```

### 1.3 Exportar

**Arquivo:** `/apps/web/src/components/icons/index.ts` (adicionar no final)

```typescript
export { OxxoIcon } from "./payment/OxxoIcon";
export { SpeiIcon } from "./payment/SpeiIcon";
```

---

## 📦 PASSO 2: Criar Formulários

### 2.1 OxxoForm

**Arquivo:** `/apps/web/src/components/payments/OxxoForm.tsx`

```typescript
"use client";

import { useTranslation } from "@/hooks/useTranslation";
import useSettings from "@/hooks/useSettings";
import { InformationCircleIcon } from "@heroicons/react/24/outline";

export function OxxoForm() {
  const { t } = useTranslation();
  const settings = useSettings();

  return (
    <div className="space-y-4">
      {/* Info Box */}
      <div 
        className="p-4 rounded-lg flex items-start gap-3"
        style={{ backgroundColor: settings.box.default.background.color }}
      >
        <InformationCircleIcon 
          className="w-6 h-6 flex-shrink-0"
          style={{ color: settings.text.color.active }}
        />
        <div>
          <h3 
            className="font-semibold mb-1 text-base"
            style={{ color: settings.text.color.primary }}
          >
            {t("payment.oxxo.title")}
          </h3>
          <p 
            className="text-sm"
            style={{ color: settings.text.color.secondary }}
          >
            {t("payment.oxxo.description")}
          </p>
        </div>
      </div>

      {/* Como funciona */}
      <div className="space-y-3">
        <p 
          className="text-sm font-semibold"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.oxxo.how_it_works")}
        </p>
        <ol className="space-y-2">
          {[1, 2, 3, 4].map((step) => (
            <li key={step} className="flex items-start gap-2">
              <span 
                className="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold"
                style={{ 
                  backgroundColor: settings.box.selected.header.background.color,
                  color: settings.box.selected.header.text.color.primary
                }}
              >
                {step}
              </span>
              <span 
                className="text-sm pt-0.5"
                style={{ color: settings.text.color.secondary }}
              >
                {t(`payment.oxxo.step_${step}`)}
              </span>
            </li>
          ))}
        </ol>
      </div>

      {/* Aviso */}
      <div 
        className="p-3 rounded-lg border"
        style={{ 
          backgroundColor: settings.box.unselected.background.color,
          borderColor: settings.box.unselected.header.background.color 
        }}
      >
        <p 
          className="text-xs flex items-start gap-2"
          style={{ color: settings.text.color.secondary }}
        >
          <span className="text-base">⏱️</span>
          <span>{t("payment.oxxo.processing_time")}</span>
        </p>
      </div>
    </div>
  );
}
```

### 2.2 SpeiForm

**Arquivo:** `/apps/web/src/components/payments/SpeiForm.tsx`

```typescript
"use client";

import { useTranslation } from "@/hooks/useTranslation";
import useSettings from "@/hooks/useSettings";
import { CheckCircleIcon, BanknotesIcon } from "@heroicons/react/24/outline";

export function SpeiForm() {
  const { t } = useTranslation();
  const settings = useSettings();

  return (
    <div className="space-y-4">
      {/* Info Box */}
      <div 
        className="p-4 rounded-lg flex items-start gap-3"
        style={{ backgroundColor: settings.box.default.background.color }}
      >
        <BanknotesIcon 
          className="w-6 h-6 flex-shrink-0"
          style={{ color: settings.text.color.active }}
        />
        <div>
          <h3 
            className="font-semibold mb-1 text-base"
            style={{ color: settings.text.color.primary }}
          >
            {t("payment.spei.title")}
          </h3>
          <p 
            className="text-sm"
            style={{ color: settings.text.color.secondary }}
          >
            {t("payment.spei.description")}
          </p>
        </div>
      </div>

      {/* Vantagens */}
      <div className="space-y-3">
        <p 
          className="text-sm font-semibold"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.spei.advantages")}
        </p>
        <ul className="space-y-2">
          {[1, 2, 3].map((num) => (
            <li key={num} className="flex items-start gap-2">
              <CheckCircleIcon 
                className="w-5 h-5 flex-shrink-0 mt-0.5"
                style={{ color: settings.text.color.active }}
              />
              <span 
                className="text-sm"
                style={{ color: settings.text.color.secondary }}
              >
                {t(`payment.spei.advantage_${num}`)}
              </span>
            </li>
          ))}
        </ul>
      </div>

      {/* Confirmação instantânea */}
      <div 
        className="p-4 rounded-lg flex items-center gap-3"
        style={{ 
          backgroundColor: settings.box.selected.background.color,
        }}
      >
        <span className="text-2xl">⚡</span>
        <p 
          className="text-sm font-medium"
          style={{ color: settings.text.color.active }}
        >
          {t("payment.spei.instant_confirmation")}
        </p>
      </div>
    </div>
  );
}
```

---

## 📦 PASSO 3: Atualizar Types

**Arquivo:** `/apps/web/src/types/index.ts`

### 3.1 Adicionar no PaymentMethod type

Procurar por:
```typescript
export type PaymentMethod =
```

Adicionar `"oxxo"` e `"spei"`:
```typescript
export type PaymentMethod =
  | "pix"
  | "pix_auto"
  | "credit_card"
  | "boleto"
  | "picpay"
  | "applepay"
  | "googlepay"
  | "openfinance_nubank"
  | "oxxo"        // ← ADICIONAR
  | "spei"        // ← ADICIONAR
  | "threeDs";
```

### 3.2 Adicionar no Payment type

Procurar por:
```typescript
export type Payment = {
```

Adicionar campos oxxo e spei:
```typescript
export type Payment = {
  id: string;
  status: PaymentStatus;
  paymentMethod: PaymentMethod;
  // ... campos existentes ...
  
  oxxo?: {           // ← ADICIONAR
    code: string;
    barcode: string;
    expirationDate: string;
  };
  spei?: {           // ← ADICIONAR
    clabe: string;
    bank: string;
    reference: string;
    beneficiary: string;
  };
};
```

---

## 📦 PASSO 4: Atualizar CheckoutProvider

**Arquivo:** `/apps/web/src/contexts/checkout-context.tsx`

### 4.1 Importar componentes (linha ~3)

```typescript
import { OxxoForm } from "@/components/payments/OxxoForm";
import { SpeiForm } from "@/components/payments/SpeiForm";
import { OxxoIcon, SpeiIcon } from "@/components/icons";
```

### 4.2 Atualizar mapIcons (linha ~365)

Adicionar:
```typescript
const mapIcons: Record<string, React.ComponentType<{ className?: string }>> = {
  credit_card: CreditCardIcon,
  boleto: BarcodeIcon,
  pix: PixIcon,
  pix_auto: PixAutoIcon,
  picpay: PicPayIcon,
  applepay: ApplePayIcon,
  googlepay: GooglePayIcon,
  openfinance_nubank: NubankIcon,
  oxxo: OxxoIcon,        // ← ADICIONAR
  spei: SpeiIcon,        // ← ADICIONAR
};
```

### 4.3 Atualizar mapForms (linha ~378)

Adicionar:
```typescript
const mapForms: Record<string, React.ComponentType<unknown>> = {
  credit_card: CreditCardForm,
  boleto: BoletoForm,
  pix: PixForm,
  pix_auto: PixAutoForm,
  picpay: PicPayForm,
  applepay: ApplePayForm,
  googlepay: GooglePayForm,
  openfinance_nubank: NubankPayForm,
  oxxo: OxxoForm,        // ← ADICIONAR
  spei: SpeiForm,        // ← ADICIONAR
};
```

---

## 📦 PASSO 5: Adicionar Traduções

### 5.1 Espanhol (es.json)

**Arquivo:** `/apps/web/src/lib/i18n/messages/es.json`

Adicionar dentro de `"payment": { ... }`:

```json
{
  "payment": {
    "oxxo": {
      "title": "Pago en efectivo con OXXO",
      "description": "Paga en cualquier tienda OXXO de México. Es rápido, fácil y seguro.",
      "how_it_works": "¿Cómo funciona?",
      "step_1": "Completa tu compra y genera el código de barras",
      "step_2": "Acude a la tienda OXXO más cercana con el código",
      "step_3": "Indica al cajero que deseas hacer un pago de servicio OXXO Pay",
      "step_4": "Realiza el pago en efectivo y conserva tu ticket",
      "processing_time": "El procesamiento puede tardar hasta 24 horas después del pago"
    },
    "spei": {
      "title": "Transferencia bancaria SPEI",
      "description": "Transferencia electrónica instantánea entre bancos mexicanos. Disponible 24/7.",
      "advantages": "Ventajas del SPEI",
      "advantage_1": "Transferencia instantánea (menos de 1 minuto)",
      "advantage_2": "Disponible 24 horas, 7 días a la semana",
      "advantage_3": "Confirmación automática e inmediata del pago",
      "instant_confirmation": "Confirmación instantánea - Tu pedido se aprueba automáticamente"
    }
  }
}
```

### 5.2 Português (pt.json)

**Arquivo:** `/apps/web/src/lib/i18n/messages/pt.json`

Adicionar dentro de `"payment": { ... }`:

```json
{
  "payment": {
    "oxxo": {
      "title": "Pagamento em dinheiro com OXXO",
      "description": "Pague em qualquer loja OXXO do México. É rápido, fácil e seguro.",
      "how_it_works": "Como funciona?",
      "step_1": "Complete sua compra e gere o código de barras",
      "step_2": "Vá até a loja OXXO mais próxima com o código",
      "step_3": "Informe ao caixa que deseja fazer um pagamento de serviço OXXO Pay",
      "step_4": "Realize o pagamento em dinheiro e guarde seu comprovante",
      "processing_time": "O processamento pode levar até 24 horas após o pagamento"
    },
    "spei": {
      "title": "Transferência bancária SPEI",
      "description": "Transferência eletrônica instantânea entre bancos mexicanos. Disponível 24/7.",
      "advantages": "Vantagens do SPEI",
      "advantage_1": "Transferência instantânea (menos de 1 minuto)",
      "advantage_2": "Disponível 24 horas, 7 dias por semana",
      "advantage_3": "Confirmação automática e imediata do pagamento",
      "instant_confirmation": "Confirmação instantânea - Seu pedido é aprovado automaticamente"
    }
  }
}
```

### 5.3 Inglês (en.json)

**Arquivo:** `/apps/web/src/lib/i18n/messages/en.json`

Adicionar dentro de `"payment": { ... }`:

```json
{
  "payment": {
    "oxxo": {
      "title": "Cash payment with OXXO",
      "description": "Pay at any OXXO store in Mexico. It's fast, easy and secure.",
      "how_it_works": "How it works?",
      "step_1": "Complete your purchase and generate the barcode",
      "step_2": "Go to the nearest OXXO store with the code",
      "step_3": "Tell the cashier you want to make an OXXO Pay service payment",
      "step_4": "Make the cash payment and keep your receipt",
      "processing_time": "Processing can take up to 24 hours after payment"
    },
    "spei": {
      "title": "SPEI bank transfer",
      "description": "Instant electronic transfer between Mexican banks. Available 24/7.",
      "advantages": "SPEI advantages",
      "advantage_1": "Instant transfer (less than 1 minute)",
      "advantage_2": "Available 24 hours, 7 days a week",
      "advantage_3": "Automatic and immediate payment confirmation",
      "instant_confirmation": "Instant confirmation - Your order is approved automatically"
    }
  }
}
```

---

## 📦 PASSO 6: Atualizar CheckoutProvider (Imports)

**Arquivo:** `/apps/web/src/contexts/checkout-context.tsx`

### No início do arquivo (após outros imports de ícones ~linha 54-63)

```typescript
import {
  OxxoIcon,
  SpeiIcon,
} from "@/components/icons";
```

### Após imports dos forms (~linha 43-51)

```typescript
import { OxxoForm } from "@/components/payments/OxxoForm";
import { SpeiForm } from "@/components/payments/SpeiForm";
```

---

## 📦 PASSO 7: Testar

### 7.1 Verificar build

```bash
cd /Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2/apps/web
pnpm build
```

### 7.2 Rodar dev

```bash
pnpm dev:web
```

### 7.3 Simular México

1. Abrir DevTools → Network
2. Editar request headers (ou usar extensão ModHeader)
3. Adicionar: `CF-IPCountry: MX`
4. Acessar: `http://localhost:3001/es/mcrd948`
5. Verificar se OXXO e SPEI aparecem nos métodos

---

## ✅ Checklist de Validação

Depois de implementar tudo, verificar:

### Componentes
- [ ] OxxoIcon renderiza corretamente
- [ ] SpeiIcon renderiza corretamente
- [ ] OxxoForm mostra instruções em espanhol
- [ ] SpeiForm mostra vantagens
- [ ] Ícones aparecem nas tabs de pagamento

### Traduções
- [ ] Textos em espanhol estão corretos
- [ ] Textos em português estão corretos
- [ ] Textos em inglês estão corretos
- [ ] Sem textos hardcoded

### Integração
- [ ] mapIcons tem oxxo e spei
- [ ] mapForms tem OxxoForm e SpeiForm
- [ ] Types incluem "oxxo" e "spei"
- [ ] Sem erros de TypeScript
- [ ] Sem erros de lint

### Funcional
- [ ] Seletor de país mostra México
- [ ] Ao selecionar México, métodos mudam
- [ ] OXXO aparece como opção
- [ ] SPEI aparece como opção
- [ ] PIX e Boleto NÃO aparecem (são do Brasil)

---

## ⚠️ Notas Importantes

1. **API ainda não filtra por país**: Por enquanto, o frontend faz o filtro via `country.paymentMethods`. Quando a API estiver pronta para filtrar, será ainda melhor.

2. **Waiting Payments**: Criar OxxoPayment e SpeiPayment será a PRÓXIMA etapa (após confirmar que backend retorna os dados).

3. **Usar sempre useSettings()**: Não use cores hardcoded, sempre use o sistema de settings.

4. **Usar sempre useTranslation()**: Todos os textos via `t()`.

5. **Validar undefined**: Sempre verificar se `offer` e `offer.product` existem.

---

## 🎯 Resultado Esperado

Após implementação:

1. ✅ Usuário acessa `/es/mcrd948`
2. ✅ Sistema detecta idioma espanhol → assume México
3. ✅ PaymentMethods mostra: **[OXXO] [SPEI] [Tarjeta]**
4. ✅ Ao clicar em OXXO → Mostra OxxoForm
5. ✅ Ao clicar em SPEI → Mostra SpeiForm
6. ✅ Todas as instruções em espanhol
7. ✅ Visual consistente com resto do checkout

---

## 📋 Arquivos a Criar/Modificar

### Criar (4 arquivos):
1. `/apps/web/src/components/icons/payment/OxxoIcon.tsx`
2. `/apps/web/src/components/icons/payment/SpeiIcon.tsx`
3. `/apps/web/src/components/payments/OxxoForm.tsx`
4. `/apps/web/src/components/payments/SpeiForm.tsx`

### Modificar (4 arquivos):
1. `/apps/web/src/components/icons/index.ts` (exportar ícones)
2. `/apps/web/src/contexts/checkout-context.tsx` (mapIcons + mapForms)
3. `/apps/web/src/types/index.ts` (PaymentMethod + Payment)
4. `/apps/web/src/lib/i18n/messages/es.json` (traduções)
5. `/apps/web/src/lib/i18n/messages/pt.json` (traduções)
6. `/apps/web/src/lib/i18n/messages/en.json` (traduções)

---

## 🚀 Comando para Começar

```bash
cd /Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2
```

Então siga os passos 1-7 em ordem.

**Boa implementação! 🇲🇽**

