# Prompt para Agente de IA - Migração Checkout V2

## Contexto do Projeto

Você é um desenvolvedor especializado em Next.js e está trabalhando na migração de um checkout de e-commerce de Vite + React para Next.js 16 com Server-Side Rendering (SSR).

## Objetivo Principal

Criar uma versão 2 completa do checkout usando Next.js 16 (App Router), mantendo todas as funcionalidades do projeto original, mas com as seguintes melhorias:

1. **Server-Side Rendering (SSR)** para melhor compatibilidade com navegadores de redes sociais
2. **Multi-idioma** baseado em detecção de IP (Português, Espanhol, Inglês)
3. **Compatibilidade** com navegadores embutidos (Instagram, TikTok, Facebook)
4. **Mesmas APIs** - reutilizar todas as APIs existentes sem modificações

## Estrutura do Projeto Base

O projeto base está localizado em `cakto-checkoutv2` e já possui:
- Next.js 16 configurado com App Router
- Tailwind CSS 4
- TypeScript
- Estrutura de monorepo com Turborepo
- Backend API com Elysia + oRPC

## Projeto Original (Referência)

O projeto original está em `cakto-checkout` e possui:

### Stack Tecnológica
- Vite + React 18 + TypeScript
- React Router DOM
- React Query + Context API
- React Hook Form + Yup
- Tailwind CSS
- Axios para chamadas HTTP

### Funcionalidades Principais

#### 1. Sistema de Checkout
- Formulário completo de dados do cliente (nome, email, telefone, CPF/CNPJ)
- Múltiplos métodos de pagamento:
  - PIX
  - PIX Automático
  - Cartão de Crédito (com parcelamento)
  - Boleto
  - Apple Pay
  - Google Pay
  - PicPay
  - Nubank (Open Finance)
- Sistema de validação de cupons
- Sistema de bumps/upsells
- Sistema de busca de endereço por CEP
- Formulário de endereço completo

#### 2. Integrações
- **Tracking Pixels**: Facebook, TikTok, Google Ads, Kwai
- **Analytics**: PostHog (com Session Replay)
- **3DS**: Cielo e Pagar.me
- **Antifraude**: Nethone
- **Fingerprinting**: ClientJS

#### 3. Builder de Checkout
Sistema de componentes configuráveis via API:
- Header
- Texto
- Imagem
- Vídeo
- Testimonial
- Seal (selos de segurança)
- Countdown
- Lista
- Mapa
- Chat (integração com múltiplos provedores)
- Facebook (comentários)
- Notification
- Exit Popup
- Advantage

#### 4. APIs Utilizadas

**Base URL**: Configurada via `VITE_BASE_API_URL` ou `VITE_BASE_CHECKOUT_API_URL`

**Endpoints principais**:
- `GET /api/product/checkout/{id}/` - Buscar dados do checkout
- `POST /api/checkout/{id}/` - Iniciar pagamento
- `GET /api/payment/status/{id}/` - Verificar status do pagamento
- `GET /api/checkout/installments/{id}/?total={total}` - Calcular parcelas
- `POST /api/coupons/validate/` - Validar cupom
- `POST /api/checkout/{id}/abandonment/` - Registrar abandono
- `GET /api/financial/3ds/token/?provider={provider}` - Token 3DS
- `GET /api/checkout/antifraud/` - Token antifraude

**Headers importantes**:
- `withCredentials: true` em todas as requisições
- Cookies são utilizados para sessão

#### 5. Variáveis de Ambiente Necessárias

```env
VITE_BASE_API_URL=https://api.example.com
VITE_BASE_CHECKOUT_API_URL=https://checkout-api.example.com
VITE_BRANDING_IMAGES_URL=https://images.example.com
VITE_HOPYPAY_PUBLIC_KEY=your_key
VITE_PUBLIC_POSTHOG_KEY=your_key
VITE_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com
VITE_3DS_PROVIDER=cielo
VITE_CIELO_3DS_API_VERSION=v2
VITE_MOCK_API_URL=https://mock-api.example.com
```

## Requisitos Técnicos Detalhados

### 1. Estrutura de Pastas Next.js

```
apps/web/src/
├── app/
│   ├── [locale]/              # Rotas internacionalizadas
│   │   ├── [id]/              # Checkout dinâmico: /pt/abc123 ou /es/abc123
│   │   │   ├── page.tsx       # Server Component principal
│   │   │   ├── loading.tsx    # Loading state
│   │   │   └── error.tsx      # Error boundary
│   │   ├── preview/           # Preview mode: /pt/preview?id=abc123
│   │   │   └── page.tsx
│   │   └── layout.tsx         # Layout com providers
│   └── layout.tsx             # Root layout
├── components/
│   ├── checkout/              # Componentes do checkout
│   ├── payments/              # Formulários de pagamento
│   ├── builder/               # Componentes do builder
│   ├── common/                # Componentes reutilizáveis
│   └── pixels/                # Tracking pixels
├── lib/
│   ├── api/                   # Clientes de API
│   ├── i18n/                  # Configuração i18n
│   └── utils/                 # Utilitários
├── hooks/                     # Custom hooks
├── services/                  # Serviços (CEP, etc.)
├── contexts/                  # Context providers
├── types/                     # TypeScript types
└── middleware.ts              # Middleware para i18n e IP detection
```

### 2. Sistema de Internacionalização

**Idiomas suportados**: Português (pt), Espanhol (es), Inglês (en)

**Detecção de idioma**:
1. Verificar cookie `NEXT_LOCALE` (preferência do usuário)
2. Se não existir, detectar país via IP usando serviço externo
3. Mapear país para idioma:
   - Brasil → Português (pt)
   - Argentina, Chile, Colômbia, México, Peru, Equador, Bolívia, Paraguai, Uruguai, Venezuela → Espanhol (es)
   - Outros → Inglês (en) como fallback
4. Armazenar preferência em cookie

**Implementação**:
- Usar `next-intl` ou criar sistema customizado
- Middleware Next.js para interceptar requests e detectar idioma
- Cache de detecção de IP (evitar múltiplas chamadas)
- Fallback para pt-BR se detecção falhar

**Arquivos de tradução**:
- `lib/i18n/messages/pt.json`
- `lib/i18n/messages/es.json`
- `lib/i18n/messages/en.json`

**Traduzir**:
- Todos os textos da interface
- Mensagens de erro
- Labels de formulários
- Botões e CTAs
- Mensagens de validação

### 3. Server-Side Rendering

**O que renderizar no servidor**:
- Dados do produto (nome, preço, descrição, imagem)
- Métodos de pagamento disponíveis
- Configuração do checkout (builder config)
- Informações da oferta
- Layout base

**O que manter no cliente**:
- Formulários interativos
- Validações em tempo real
- Tracking pixels
- 3DS flows
- Apple Pay / Google Pay

**Implementação**:
- Usar Server Components para dados iniciais
- Usar Server Actions para submissão de formulários
- Usar Client Components apenas quando necessário (adicionar 'use client')
- Implementar streaming para melhor performance

### 4. Compatibilidade com Navegadores de Redes Sociais

**Problemas comuns**:
- JavaScript não executado corretamente
- APIs modernas não suportadas
- Cookies bloqueados
- iframes com restrições

**Soluções**:
- Renderizar HTML crítico no servidor
- Evitar uso de APIs modernas não suportadas
- Implementar polyfills quando necessário
- Testar extensivamente em navegadores embutidos
- Otimizar JavaScript (minificar, tree-shaking)
- Implementar Progressive Enhancement

### 5. Migração de Componentes

#### Componentes Principais a Migrar

**CheckoutPage** → `app/[locale]/[id]/page.tsx`
- Server Component que busca dados iniciais
- Renderiza layout base
- Passa dados para componentes filhos

**CheckoutForm** → `components/checkout/CheckoutForm.tsx`
- Client Component
- Usar React Hook Form
- Validação com Zod (server-side) + Yup (client-side para compatibilidade)
- Server Action para submissão

**PaymentMethods** → `components/checkout/PaymentMethods.tsx`
- Client Component
- Lista métodos de pagamento
- Gerencia seleção de método

**Formulários de Pagamento** → `components/payments/`
- CreditCardForm
- PixForm
- PixAutoForm
- BoletoForm
- ApplePayForm
- GooglePayForm
- PicPayForm
- NubankPayForm

**Builder Components** → `components/builder/`
- Todos os componentes do builder
- Adaptar para Server Components quando possível

#### Contextos

**CheckoutContext** → Adaptar para:
- Server Components para dados iniciais
- Client Components para estado interativo
- Server Actions para mutations

**CheckoutModeContext** → Manter similar, adaptar para Next.js

**NotificationContext** → Manter similar

### 6. Serviços de API

**Criar clientes de API**:

```typescript
// lib/api/checkout.ts
export async function getCheckoutData(id: string, affiliateShortId?: string) {
  // Server-side fetch
}

// lib/api/payment.ts
export async function startPayment(id: string, payload: PaymentPayload) {
  // Server Action
}

// lib/api/coupon.ts
export async function validateCoupon(code: string, offerId: string) {
  // Server Action
}
```

**Manter compatibilidade**:
- Mesmas URLs de API
- Mesmos headers
- Mesmos payloads
- Mesmas respostas

### 7. Integrações

#### Tracking Pixels
- Manter como Client Components
- Carregar apenas no cliente
- Verificar se está em ambiente de produção

#### PostHog
- Adaptar para Next.js
- Manter Session Replay
- Configurar para SSR

#### 3DS
- Manter fluxo similar
- Adaptar para Server Actions quando possível
- Manter compatibilidade com Cielo e Pagar.me

#### Antifraude (Nethone)
- Adaptar para SSR
- Manter fingerprinting no cliente

### 8. Validações

**Server-side**:
- Usar Zod para validação de formulários
- Validar antes de enviar para API
- Retornar erros formatados

**Client-side**:
- Manter validações em tempo real
- Usar React Hook Form
- Feedback visual imediato

### 9. Tratamento de Erros

- Implementar error boundaries
- Páginas de erro customizadas (404, 500)
- Logging de erros (server-side e client-side)
- Mensagens de erro traduzidas

### 10. Performance

- Implementar ISR quando possível
- Lazy loading de componentes pesados
- Otimizar imagens (Next.js Image)
- Code splitting automático
- Cache strategies

## Instruções Específicas de Implementação

### Passo 1: Configuração Inicial
1. Configurar Next.js 16 com App Router
2. Configurar TypeScript paths
3. Configurar Tailwind CSS
4. Configurar sistema de i18n
5. Criar estrutura de pastas

### Passo 2: Middleware de Internacionalização
1. Criar `middleware.ts` na raiz de `apps/web`
2. Implementar detecção de IP
3. Implementar detecção de idioma
4. Configurar rotas internacionalizadas

### Passo 3: Migração de Componentes Core
1. Migrar CheckoutPage para Server Component
2. Migrar CheckoutForm para Client Component
3. Migrar PaymentMethods
4. Migrar formulários de pagamento

### Passo 4: Serviços de API
1. Criar clientes de API server-side
2. Criar Server Actions para mutations
3. Manter compatibilidade com APIs existentes

### Passo 5: Integrações
1. Migrar tracking pixels
2. Adaptar PostHog
3. Adaptar 3DS
4. Adaptar antifraude

### Passo 6: Builder
1. Migrar todos os componentes do builder
2. Adaptar para Server Components quando possível
3. Manter funcionalidade completa

### Passo 7: Testes
1. Testar fluxo completo
2. Testar todos os métodos de pagamento
3. Testar em navegadores de redes sociais
4. Testar internacionalização

## Considerações Importantes

1. **Não modificar APIs existentes** - apenas consumir
2. **Manter compatibilidade** com comportamento atual
3. **Priorizar SSR** para conteúdo crítico
4. **Testar extensivamente** em navegadores embutidos
5. **Documentar** todas as mudanças
6. **Manter performance** similar ou melhor
7. **Garantir acessibilidade** (WCAG 2.1 AA)

## Formato de Entrega

Para cada componente/funcionalidade migrada, fornecer:

1. **Código completo** do componente
2. **Explicação** das mudanças realizadas
3. **Testes** realizados
4. **Considerações** de compatibilidade
5. **Próximos passos** se necessário

## Exemplo de Estrutura de Arquivo

```typescript
// app/[locale]/[id]/page.tsx
import { getCheckoutData } from '@/lib/api/checkout';
import { CheckoutClient } from '@/components/checkout/CheckoutClient';

export default async function CheckoutPage({
  params,
  searchParams
}: {
  params: { locale: string; id: string };
  searchParams: { affiliate?: string };
}) {
  const checkoutData = await getCheckoutData(
    params.id,
    searchParams.affiliate
  );

  return <CheckoutClient initialData={checkoutData} />;
}
```

## Prioridades

1. **Alta**: Sistema de checkout básico funcionando
2. **Alta**: Internacionalização (pt, es, en)
3. **Alta**: Compatibilidade com navegadores de redes sociais
4. **Média**: Builder de checkout
5. **Média**: Integrações (pixels, analytics)
6. **Baixa**: Otimizações avançadas

## Observações Finais

- Use TypeScript strict mode
- Siga as convenções do Next.js
- Mantenha código limpo e documentado
- Implemente error handling robusto
- Teste em múltiplos ambientes
- Considere acessibilidade desde o início

---

**Comece pela configuração inicial e depois migre os componentes principais. Trabalhe de forma incremental, testando cada funcionalidade conforme migra.**

