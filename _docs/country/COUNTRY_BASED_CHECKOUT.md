# Country-Based Checkout Implementation

**Status:** ✅ Completo  
**Data:** 11/11/2025  
**Versão:** 1.0.0

---

## 📋 Visão Geral

Sistema de checkout baseado em países que substitui o sistema anterior baseado apenas em idiomas. O país do usuário determina automaticamente:

- **Idioma padrão** (pt/es/en)
- **Métodos de pagamento disponíveis** 
- **Provider de pagamento** (Cakto ou EBANX)
- **Formatos de campos** (documento, telefone, CEP)
- **Labels dinâmicos** (CPF/CNPJ, RUT, DNI, etc.)

---

## 🎯 Objetivos Alcançados

### ✅ UI/UX Changes
- ❌ **REMOVIDO**: Header do checkout (`CheckoutHeader`) com "Checkout seguro" e seletor de idioma
- ✅ **ADICIONADO**: Seletor de país dentro da seção "Informações de Contato" (lado direito do título)
- ✅ **TRANSFORMADO**: Seletor agora combina país + idioma + moeda em um único dropdown elegante

### ✅ Country-Based System
- ✅ Detecção automática de país via IP/geolocalização (ipapi.co)
- ✅ Mapeamento país → idioma padrão
- ✅ Mapeamento país → métodos de pagamento disponíveis
- ✅ Mapeamento país → payment provider (configurável para futuro)
- ✅ Persistência em localStorage
- ✅ Mudança de idioma automática ao trocar país

### ✅ Providers Configurados
- **Brasil:** Provider Cakto (mantido como está)
- **LATAM + Internacional:** EBANX (configurado, backend processa)
- ✅ Controle configurável por país via `countries.ts`

---

## 🗺️ Países Suportados

### Brasil (Prioridade 1)
```typescript
{
  code: "BR",
  name: "Brasil",
  flag: "🇧🇷",
  language: "pt",
  currency: "BRL",
  provider: "cakto",
  paymentMethods: ["pix", "pix_auto", "boleto", "credit_card", "picpay", 
                   "googlepay", "applepay", "openfinance_nubank"],
  documentTypes: ["cpf", "cnpj"],
  phoneFormat: "(99) 99999-9999",
  zipCodeFormat: "99999-999",
  zipCodeLabel: "CEP",
  documentLabel: "CPF/CNPJ"
}
```

### México
```typescript
{
  code: "MX",
  name: "México",
  flag: "🇲🇽",
  language: "es",
  currency: "MXN",
  provider: "ebanx",
  paymentMethods: ["credit_card", "spei", "oxxo"],
  documentTypes: ["curp", "rfc"],
  phoneFormat: "+52 99 9999 9999",
  zipCodeFormat: "99999",
  zipCodeLabel: "Código Postal",
  documentLabel: "CURP/RFC"
}
```

### Argentina, Chile, Colômbia, Peru, USA
Ver configurações completas em `apps/web/src/constants/countries.ts`

---

## 📁 Arquivos Criados

### 1. Types
**`apps/web/src/types/country.ts`**
- `PaymentProvider` - "cakto" | "ebanx"
- `CountryCode` - BR, MX, AR, CL, CO, PE, US, OTHER
- `DocumentType` - cpf, cnpj, rut, dni, cuit, curp, rfc, cc, ce, nit, ruc, ssn
- `LocalPaymentMethod` - spei, oxxo, rapipago, pagofacil, servipag, multicaja, pse, efecty, baloto, pagoefectivo, safetypay
- `CountryConfig` - Configuração completa de cada país
- `UserCountryData` - Dados do país do usuário

### 2. Constants
**`apps/web/src/constants/countries.ts`**
- `COUNTRIES` - Record com configuração de todos os 8 países
- `COUNTRY_OPTIONS` - Array ordenado para o selector (BR em primeiro)
- `LANGUAGE_TO_DEFAULT_COUNTRY` - Mapeamento de idioma para país padrão

### 3. Context
**`apps/web/src/contexts/country-context.tsx`**
- `CountryProvider` - Provider do contexto de país
- `useCountry()` - Hook para consumir o contexto
- Detecção automática via GeoIP
- Persistência em localStorage
- Mudança automática de idioma/URL

### 4. API Route
**`apps/web/src/app/api/geoip/route.ts`**
- Endpoint: `/api/geoip`
- Integração com ipapi.co
- Detecta país por IP do usuário
- Fallback para Brasil em caso de erro
- Trata IPs localhost/privados

### 5. Component
**`apps/web/src/components/checkout/country-selector.tsx`**
- Dropdown elegante com flags
- Mostra país + moeda + idioma
- Checkmark no país selecionado
- Integrado na seção de contato

---

## 🔧 Arquivos Modificados

### 1. Environment Helpers
**`apps/web/src/lib/env.ts`**
```typescript
// Adicionado:
export const getGeoipApiUrl = () =>
  process.env.NEXT_PUBLIC_GEOIP_API_URL || "https://ipapi.co";
export const getEbanxPublicKey = () =>
  process.env.NEXT_PUBLIC_EBANX_PUBLIC_KEY || "";
```

### 2. Checkout Client
**`apps/web/src/components/checkout/checkout-client.tsx`**
- ❌ Removido: `<CheckoutHeader />`
- ✅ Adicionado: `<CountryProvider>` wrapper
- ✅ Simplificado: Layout sem header sticky

### 3. Checkout Component
**`apps/web/src/components/checkout/CheckoutComponent.tsx`**
- ✅ Adicionado: `<CountrySelector />` ao lado do título "Informações de Contato"
- ✅ Campo "document" agora dinâmico (baseado em `country.documentLabel`)
- ✅ Campo "phone" com máscara dinâmica (`country.phoneFormat`)
- ✅ Campo "cpfCnpj" ativo apenas para Brasil

### 4. Checkout Provider
**`apps/web/src/contexts/checkout-context.tsx`**
- ✅ Importa `useCountry()` do country-context
- ✅ Filtra `paymentTabs` baseado em `country.paymentMethods`
- ✅ Adiciona `country` como dependência do useMemo

### 5. Translations
**`apps/web/src/lib/i18n/messages/[pt|es|en].json`**
```json
{
  "country": {
    "selector": {
      "label": "País",
      "select_country": "Selecione seu país",
      "current_country": "País atual"
    },
    "names": {
      "BR": "Brasil",
      "MX": "México",
      // ...
    },
    "documents": {
      "CPF": "CPF",
      "CNPJ": "CNPJ",
      // ...
    }
  }
}
```

### 6. Documentation
**`apps/web/ENV.md`**
- ✅ Documentado: `NEXT_PUBLIC_GEOIP_API_URL`
- ✅ Documentado: `NEXT_PUBLIC_EBANX_PUBLIC_KEY`
- ✅ Adicionado: Nota sobre limites do ipapi.co

---

## 🗑️ Arquivos Deletados

### `apps/web/src/components/checkout/checkout-header.tsx`
**Motivo:** Header não é mais necessário. O seletor de país foi integrado diretamente na seção de contato, proporcionando melhor UX e aproveitando melhor o espaço da tela.

---

## 🔄 Fluxo de Funcionamento

### 1. Detecção Inicial
```
Usuário acessa /pt/7rohg3i
  ↓
CountryProvider inicializa
  ↓
1. Tenta ler localStorage ("user_country")
   - Se existe → usa país salvo
   - Se não existe → continua
  ↓
2. Verifica se já tem seleção manual
   - Se sim → não detecta novamente
   - Se não → chama /api/geoip
  ↓
3. API detecta país por IP
   - Brasil → BR
   - México → MX
   - Argentina → AR
   - Outros → mapeia ou OTHER
  ↓
4. Define país inicial
  ↓
5. Salva em localStorage
```

### 2. Seleção Manual
```
Usuário clica em 🇧🇷 Brasil ▼
  ↓
Dropdown abre mostrando todos os países
  ↓
Usuário seleciona 🇲🇽 México
  ↓
changeCountry("MX") é chamado
  ↓
1. Atualiza state: userCountryData
2. Marca: isManualSelection = true
3. Compara idiomas: pt !== es
4. Atualiza cookie: NEXT_LOCALE=es
5. Atualiza URL: /pt/7rohg3i → /es/7rohg3i
6. Router faz push + refresh
7. Salva em localStorage
  ↓
Página recarrega em espanhol
Métodos de pagamento atualizam (PIX removido, SPEI/OXXO adicionados)
```

### 3. Filtragem de Métodos de Pagamento
```
CheckoutProvider.paymentTabs useMemo
  ↓
1. Lê: offer.product.paymentMethods
   Exemplo: ["pix", "credit_card", "boleto", "spei", "oxxo"]
  ↓
2. Filtra por country.paymentMethods
   Brasil: ["pix", "credit_card", "boleto"] ✅
   México: ["credit_card", "spei", "oxxo"] ✅
  ↓
3. Aplica regras de negócio
   - Assinatura: apenas métodos permitidos
   - Único: remove pix_auto
  ↓
4. Ordena por paymentsOrder
  ↓
5. Mapeia para tabs com ícones e componentes
  ↓
Renderiza apenas métodos válidos para o país
```

### 4. Campos Dinâmicos
```
CheckoutComponent renderiza
  ↓
const { country } = useCountry()
  ↓
Campo "document":
  - Label: country.documentLabel
    Brasil → "CPF/CNPJ"
    México → "CURP/RFC"
    Chile → "RUT"
  - Máscara: cpfCnpj={country.code === "BR"}
  ↓
Campo "phone":
  - Máscara: country.phoneFormat
    Brasil → "(99) 99999-9999"
    México → "+52 99 9999 9999"
    Chile → "+56 9 9999 9999"
```

---

## 🌐 Integração GeoIP

### API Route: `/api/geoip`

**Request:**
```typescript
GET /api/geoip
Headers: {
  "x-forwarded-for": "*************",
  "x-real-ip": "*************"
}
```

**Response:**
```json
{
  "ip": "*************",
  "countryCode": "BR",
  "country": "Brazil",
  "city": "São Paulo",
  "region": "São Paulo"
}
```

**Fallbacks:**
- IP localhost/privado → BR (Brasil)
- Erro na API → BR (Brasil)
- País não suportado → OTHER (Internacional)

**Serviço Usado:**
- **ipapi.co** (padrão)
- **Limite:** 1000 requisições/dia (free tier)
- **Alternativas:** ipinfo.io (50k req/mês)

**Configuração:**
```env
NEXT_PUBLIC_GEOIP_API_URL=https://ipapi.co
```

---

## 💾 Persistência

### localStorage Key: `user_country`

**Estrutura:**
```json
{
  "countryCode": "BR",
  "detectedIp": "*************",
  "detectedAt": "2025-11-11T10:30:00.000Z",
  "isManualSelection": false
}
```

**Comportamento:**
1. **Primeira visita:** Vazio → Detecta país → Salva
2. **Visita subsequente:** Lê do storage → Usa país salvo
3. **Seleção manual:** isManualSelection = true → Não detecta novamente
4. **Clear storage:** Volta a detectar automaticamente

---

## 🎨 UI/UX

### Country Selector

**Localização:**
- Dentro da seção "Informações de Contato"
- À direita do título (justify-between)
- Sempre visível (não é um header separado)

**Visual:**
```
┌─────────────────────────────────────────────────┐
│  Informações de Contato    [🇧🇷 Brasil ▼]     │
│                                                 │
│  Nome completo                                  │
│  [_____________________________________]        │
│                                                 │
│  E-mail                                         │
│  [_____________________________________]        │
│                                                 │
│  CPF/CNPJ              Celular                  │
│  [_______________]     [_______________]        │
└─────────────────────────────────────────────────┘
```

**Dropdown:**
```
┌──────────────────────────────────────┐
│ 🇧🇷 Brasil                          │
│    BRL • PT                      ✓  │
├──────────────────────────────────────┤
│ 🇲🇽 México                          │
│    MXN • ES                          │
├──────────────────────────────────────┤
│ 🇦🇷 Argentina                       │
│    ARS • ES                          │
├──────────────────────────────────────┤
│ 🇨🇱 Chile                           │
│    CLP • ES                          │
├──────────────────────────────────────┤
│ 🇨🇴 Colombia                        │
│    COP • ES                          │
├──────────────────────────────────────┤
│ 🇵🇪 Perú                            │
│    PEN • ES                          │
├──────────────────────────────────────┤
│ 🇺🇸 United States                   │
│    USD • EN                          │
└──────────────────────────────────────┘
```

---

## 🧪 Testes Recomendados

### 1. Detecção Automática
```bash
# Limpar localStorage
localStorage.clear()

# Acessar checkout
http://localhost:3001/pt/7rohg3i

# Verificar:
- País detectado corretamente (ou BR como padrão)
- localStorage preenchido
- Métodos de pagamento corretos
```

### 2. Seleção Manual
```bash
# Selecionar México no dropdown

# Verificar:
- URL mudou: /pt/ → /es/
- Idioma mudou: português → espanhol
- Métodos: PIX removido, SPEI/OXXO adicionados
- Labels: CPF/CNPJ → CURP/RFC
- Máscaras: telefone atualizado
- localStorage atualizado (isManualSelection: true)
```

### 3. Persistência
```bash
# Com país selecionado manualmente
# Dar refresh na página

# Verificar:
- País mantido
- Não detecta novamente
- localStorage preservado
```

### 4. Métodos de Pagamento por País

**Brasil (BR):**
- ✅ PIX
- ✅ PIX Auto
- ✅ Boleto
- ✅ Cartão de Crédito
- ✅ PicPay
- ✅ Google Pay
- ✅ Apple Pay
- ✅ Nubank

**México (MX):**
- ✅ Cartão de Crédito
- ✅ SPEI
- ✅ OXXO
- ❌ PIX (removido)
- ❌ Boleto (removido)

**Argentina (AR):**
- ✅ Cartão de Crédito
- ✅ Rapipago
- ✅ PagoFácil

**Chile (CL):**
- ✅ Cartão de Crédito
- ✅ Servipag
- ✅ Multicaja

**Colômbia (CO):**
- ✅ Cartão de Crédito
- ✅ PSE
- ✅ Efecty
- ✅ Baloto

**Peru (PE):**
- ✅ Cartão de Crédito
- ✅ PagoEfectivo
- ✅ SafetyPay

**USA/Internacional:**
- ✅ Cartão de Crédito
- ✅ Google Pay
- ✅ Apple Pay

### 5. Campos Dinâmicos

**Brasil:**
- Documento: "CPF/CNPJ"
- Telefone: "(99) 99999-9999"
- CEP: "99999-999"

**México:**
- Documento: "CURP/RFC"
- Telefone: "+52 99 9999 9999"
- Código Postal: "99999"

**Chile:**
- Documento: "RUT"
- Telefone: "+56 9 9999 9999"
- Código Postal: "9999999"

---

## 🔧 Configuração

### Adicionar Novo País

1. **Adicionar tipo em `types/country.ts`:**
```typescript
export type CountryCode = 
  | "BR"
  | "MX"
  | "NEW_COUNTRY" // ← Adicionar aqui
```

2. **Adicionar configuração em `constants/countries.ts`:**
```typescript
export const COUNTRIES: Record<CountryCode, CountryConfig> = {
  // ... países existentes
  NEW_COUNTRY: {
    code: "NEW_COUNTRY",
    name: "Novo País",
    flag: "🏴",
    language: "pt",
    currency: "XXX",
    provider: "ebanx",
    paymentMethods: ["credit_card"],
    documentTypes: ["tax_id"],
    phoneFormat: "+99 ************",
    zipCodeFormat: "99999",
    zipCodeLabel: "Postal Code",
    documentLabel: "Tax ID",
  },
};

// Adicionar em COUNTRY_OPTIONS se quiser no dropdown
export const COUNTRY_OPTIONS: CountryConfig[] = [
  COUNTRIES.BR,
  COUNTRIES.NEW_COUNTRY, // ← Adicionar aqui
  // ... outros
];
```

3. **Adicionar traduções:**
```json
// pt.json, es.json, en.json
{
  "country": {
    "names": {
      "NEW_COUNTRY": "Novo País"
    }
  }
}
```

4. **Atualizar GeoIP mapping (opcional):**
```typescript
// apps/web/src/app/api/geoip/route.ts
const countryCodeMap: Record<string, string> = {
  "BR": "BR",
  "NC": "NEW_COUNTRY", // ← Mapear código ISO
  // ...
};
```

---

## 🚀 Deploy

### Variáveis de Ambiente Necessárias

**Desenvolvimento:**
```env
NEXT_PUBLIC_GEOIP_API_URL=https://ipapi.co
NEXT_PUBLIC_EBANX_PUBLIC_KEY=test_key_here
```

**Produção:**
```env
NEXT_PUBLIC_GEOIP_API_URL=https://ipapi.co
NEXT_PUBLIC_EBANX_PUBLIC_KEY=live_key_here
```

### Checklist de Deploy

- [ ] Variáveis de ambiente configuradas
- [ ] GeoIP API funcionando (testar em produção)
- [ ] Verificar limites do ipapi.co (1000 req/dia)
- [ ] Considerar cache de detecção de país
- [ ] Logs de mudança de país habilitados
- [ ] Analytics trackeando país do usuário

---

## 📊 Métricas Sugeridas

### PostHog / Analytics

```typescript
// Trackear país selecionado
posthog.capture('country_selected', {
  country_code: country.code,
  is_manual: userCountryData.isManualSelection,
  previous_country: previousCountry,
});

// Trackear mudança de idioma automática
posthog.capture('language_auto_changed', {
  from_language: locale,
  to_language: country.language,
  country: country.code,
});

// Trackear métodos de pagamento disponíveis
posthog.capture('payment_methods_filtered', {
  country: country.code,
  methods: paymentTabs.map(t => t.id),
  count: paymentTabs.length,
});
```

---

## 🐛 Troubleshooting

### País não detecta corretamente
**Causa:** API GeoIP retornando erro ou IP inválido  
**Solução:** Verificar logs em `/api/geoip`, confirmar que ipapi.co está acessível

### Métodos de pagamento não atualizam
**Causa:** CheckoutProvider não recebe `country` do context  
**Solução:** Verificar se `CountryProvider` envolve `CheckoutProvider` corretamente

### Idioma não muda ao trocar país
**Causa:** Cookie NEXT_LOCALE não está sendo atualizado  
**Solução:** Verificar `changeCountry()` em `country-context.tsx`, confirmar que cookie é gravado

### localStorage não persiste
**Causa:** Browser em modo privado ou bloqueando localStorage  
**Solução:** Adicionar fallback para cookies

### Máscaras de telefone não funcionam
**Causa:** Máscara incompatível com TextField component  
**Solução:** Ajustar formato em `countries.ts` ou atualizar TextField

---

## 📚 Referências

### APIs de GeoIP
- **ipapi.co:** https://ipapi.co/api/ (1000 req/dia grátis)
- **ipinfo.io:** https://ipinfo.io/ (50k req/mês grátis)
- **ip-api.com:** https://ip-api.com/ (sem https no free tier)

### Payment Providers
- **EBANX Docs:** https://developers.ebanx.com/
- **EBANX Payment Methods:** https://developers.ebanx.com/api-reference/payment-methods
- **EBANX Countries:** https://developers.ebanx.com/api-reference/countries

### Documentos por País
- **Brasil:** CPF (999.999.999-99), CNPJ (99.999.999/9999-99)
- **México:** CURP (18 chars), RFC (13 chars)
- **Argentina:** DNI (8 digits), CUIT (99-99999999-9)
- **Chile:** RUT (99.999.999-9)
- **Colômbia:** CC (10 digits), CE (7 digits)
- **Peru:** DNI (8 digits), RUC (11 digits)

---

## ✅ Status Final

**Implementação:** ✅ 100% Completa  
**Testes:** ⏳ Pendente (testes em desenvolvimento)  
**Documentação:** ✅ Completa  
**Deploy:** ⏳ Aguardando variáveis de ambiente

**Última atualização:** 11/11/2025 às 10:45  
**Responsável:** Cursor AI (Implementação automática)  
**Aprovado por:** Pendente

---

## 📝 Notas Adicionais

- O sistema foi projetado para ser facilmente extensível
- Novos países podem ser adicionados em minutos
- Todos os pagamentos são processados pelo backend da Cakto
- O campo `provider` está configurado mas não utilizado (preparado para futuro)
- As máscaras de telefone podem precisar de ajuste fino por país
- Considerar implementar cache de GeoIP para reduzir chamadas de API

---

**Documento gerado automaticamente pelo sistema de implementação.**

