# Country-Based Checkout - Quick Start Guide

⚡ Guia rápido para trabalhar com o sistema de países no checkout.

---

## 🚀 Uso Básico

### 1. Usar o <PERSON>

```typescript
import { useCountry } from "@/contexts/country-context";

function MeuComponente() {
  const { country, changeCountry, isLoadingCountry } = useCountry();
  
  // Acessar configurações do país atual
  console.log(country.code);          // "BR"
  console.log(country.name);          // "Brasil"
  console.log(country.flag);          // "🇧🇷"
  console.log(country.language);      // "pt"
  console.log(country.currency);      // "BRL"
  console.log(country.paymentMethods); // ["pix", "credit_card", ...]
  console.log(country.documentLabel);  // "CPF/CNPJ"
  console.log(country.phoneFormat);    // "(99) 99999-9999"
  
  // Mudar país programaticamente
  const handleChangeToMexico = () => {
    changeCountry("MX");
  };
  
  return (
    <div>
      <p>País atual: {country.name}</p>
      {isLoadingCountry && <p>Detectando país...</p>}
    </div>
  );
}
```

### 2. Campos Dinâmicos por País

```typescript
import { useCountry } from "@/contexts/country-context";
import TextField from "@/components/ui/text-field";

function FormularioContato() {
  const { country } = useCountry();
  
  return (
    <>
      {/* Campo de documento (dinâmico) */}
      <TextField
        name="document"
        label={country.documentLabel}        // "CPF/CNPJ" ou "RUT" ou "DNI"
        placeholder={country.documentLabel}
        cpfCnpj={country.code === "BR"}     // Validação BR apenas
      />
      
      {/* Campo de telefone (dinâmico) */}
      <TextField
        name="phone"
        label="Telefone"
        mask={country.phoneFormat}          // "(99) 99999-9999" ou "+56 9 9999 9999"
      />
      
      {/* Campo de CEP (dinâmico) */}
      <TextField
        name="zipcode"
        label={country.zipCodeLabel}        // "CEP" ou "Código Postal"
        mask={country.zipCodeFormat}        // "99999-999" ou "99999"
      />
    </>
  );
}
```

### 3. Filtrar Métodos de Pagamento

```typescript
import { useCountry } from "@/contexts/country-context";

function PaymentSelector() {
  const { country } = useCountry();
  const { paymentTabs } = useCheckout();
  
  // paymentTabs já vem filtrado automaticamente pelo país!
  // Brasil → PIX, Boleto, Cartão
  // México → SPEI, OXXO, Cartão
  
  return (
    <div>
      {paymentTabs.map((tab) => (
        <button key={tab.id}>
          <tab.Icon />
          {tab.label}
        </button>
      ))}
    </div>
  );
}
```

### 4. Renderização Condicional por País

```typescript
import { useCountry } from "@/contexts/country-context";

function MinhaFeature() {
  const { country } = useCountry();
  
  // Mostrar feature apenas para Brasil
  if (country.code === "BR") {
    return <FeatureBrasileira />;
  }
  
  // Mostrar feature para LATAM
  if (["MX", "AR", "CL", "CO", "PE"].includes(country.code)) {
    return <FeatureLatam />;
  }
  
  // Feature padrão
  return <FeatureInternacional />;
}
```

---

## 📋 Países Disponíveis

```typescript
import { COUNTRIES, COUNTRY_OPTIONS } from "@/constants/countries";

// Acessar configuração específica
const brasil = COUNTRIES.BR;
const mexico = COUNTRIES.MX;

// Listar todos os países no dropdown
const allCountries = COUNTRY_OPTIONS; // [BR, MX, AR, CL, CO, PE, US]
```

### Códigos de País

| Code | País | Idioma | Moeda | Provider |
|------|------|--------|-------|----------|
| BR | Brasil | pt | BRL | cakto |
| MX | México | es | MXN | ebanx |
| AR | Argentina | es | ARS | ebanx |
| CL | Chile | es | CLP | ebanx |
| CO | Colômbia | es | COP | ebanx |
| PE | Peru | es | PEN | ebanx |
| US | United States | en | USD | ebanx |
| OTHER | Internacional | en | USD | ebanx |

---

## 🎯 Casos de Uso Comuns

### Caso 1: Mostrar Texto Diferente por País

```typescript
const { country } = useCountry();

const getWelcomeMessage = () => {
  switch (country.code) {
    case "BR":
      return "Bem-vindo ao checkout!";
    case "MX":
      return "¡Bienvenido al checkout!";
    case "US":
      return "Welcome to checkout!";
    default:
      return "Welcome!";
  }
};
```

### Caso 2: Validação de Documento por País

```typescript
const { country } = useCountry();

const validateDocument = (value: string) => {
  switch (country.code) {
    case "BR":
      return isValidCPF(value) || isValidCNPJ(value);
    case "CL":
      return isValidRUT(value);
    case "AR":
      return isValidDNI(value) || isValidCUIT(value);
    default:
      return value.length > 0;
  }
};
```

### Caso 3: Preço com Moeda do País

```typescript
const { country } = useCountry();

const formatPrice = (amount: number) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: country.currency,
  }).format(amount);
};

// Brasil: R$ 100,00
// México: MX$ 100,00
// USA: US$ 100,00
```

### Caso 4: Detectar Se É LATAM

```typescript
const { country } = useCountry();

const isLatam = () => {
  return ["MX", "AR", "CL", "CO", "PE"].includes(country.code);
};

if (isLatam()) {
  // Lógica específica para América Latina
}
```

### Caso 5: Link de Suporte por País

```typescript
const { country } = useCountry();

const getSupportLink = () => {
  const links = {
    BR: "https://suporte.cakto.com.br",
    MX: "https://soporte.cakto.com",
    US: "https://support.cakto.com",
  };
  return links[country.code] || links.US;
};
```

---

## 🔧 Customização

### Adicionar Novo Método de Pagamento para um País

```typescript
// Em: apps/web/src/constants/countries.ts

export const COUNTRIES: Record<CountryCode, CountryConfig> = {
  MX: {
    // ... outras configs
    paymentMethods: [
      "credit_card",
      "spei",
      "oxxo",
      "new_payment_method", // ← Adicionar aqui
    ],
  },
};
```

### Mudar Formato de Telefone

```typescript
// Em: apps/web/src/constants/countries.ts

export const COUNTRIES: Record<CountryCode, CountryConfig> = {
  BR: {
    // ... outras configs
    phoneFormat: "(99) 9999-9999", // ← Atualizar máscara
  },
};
```

### Adicionar Novo Tipo de Documento

```typescript
// 1. Em: apps/web/src/types/country.ts
export type DocumentType = 
  | "cpf" 
  | "new_doc_type" // ← Adicionar tipo
  
// 2. Em: apps/web/src/constants/countries.ts
export const COUNTRIES: Record<CountryCode, CountryConfig> = {
  NEW_COUNTRY: {
    documentTypes: ["new_doc_type"], // ← Usar tipo
    documentLabel: "New Doc", // ← Label do campo
  },
};

// 3. Em: apps/web/src/lib/i18n/messages/pt.json
{
  "country": {
    "documents": {
      "NEW_DOC_TYPE": "Novo Documento" // ← Tradução
    }
  }
}
```

---

## 🌐 GeoIP

### Como Funciona

```typescript
// Automático ao carregar a página
1. CountryProvider detecta país via /api/geoip
2. Salva em localStorage
3. Não detecta novamente se já tem seleção

// Manual
changeCountry("MX") // Força mudança + marca como manual
```

### Forçar Re-detecção

```typescript
// No console do navegador:
localStorage.removeItem("user_country");
location.reload();
```

### Testar com País Específico

```typescript
// No console do navegador:
localStorage.setItem("user_country", JSON.stringify({
  countryCode: "MX",
  isManualSelection: true,
  detectedAt: new Date().toISOString()
}));
location.reload();
```

---

## 🐛 Debug

### Ver País Atual

```typescript
// No console do navegador:
JSON.parse(localStorage.getItem("user_country"));
```

### Ver Métodos de Pagamento Disponíveis

```typescript
// No componente:
const { country } = useCountry();
const { paymentTabs } = useCheckout();

console.log("País:", country.code);
console.log("Métodos disponíveis:", country.paymentMethods);
console.log("Métodos renderizados:", paymentTabs.map(t => t.id));
```

### Ver Detecção de GeoIP

```bash
# No terminal:
curl http://localhost:3001/api/geoip

# Resposta:
{
  "ip": "*************",
  "countryCode": "BR",
  "country": "Brazil",
  "city": "São Paulo",
  "region": "São Paulo"
}
```

---

## 📚 Helpers Úteis

```typescript
import { COUNTRIES, LANGUAGE_TO_DEFAULT_COUNTRY } from "@/constants/countries";

// Obter país padrão por idioma
const defaultCountryForPortuguese = LANGUAGE_TO_DEFAULT_COUNTRY["pt"]; // "BR"
const defaultCountryForSpanish = LANGUAGE_TO_DEFAULT_COUNTRY["es"];    // "MX"

// Verificar se país existe
const countryExists = (code: string): boolean => {
  return code in COUNTRIES;
};

// Obter config de país com fallback
const getCountryConfig = (code: string) => {
  return COUNTRIES[code] || COUNTRIES.BR;
};
```

---

## ⚠️ Gotchas

### 1. Sempre Envolver com CountryProvider

```typescript
// ❌ ERRADO
<CheckoutProvider>
  <MeuComponente /> {/* useCountry() não funciona */}
</CheckoutProvider>

// ✅ CORRETO
<CheckoutProvider>
  <CountryProvider>
    <MeuComponente /> {/* useCountry() funciona! */}
  </CountryProvider>
</CheckoutProvider>
```

### 2. Country Muda Automaticamente ao Mudar Idioma

```typescript
// Se usuário está em Brasil (pt) e seleciona México:
changeCountry("MX");
// → Automaticamente muda idioma para "es"
// → URL muda de /pt/ para /es/
// → Página recarrega
```

### 3. Métodos de Pagamento São Filtrados Automaticamente

```typescript
// Não precisa filtrar manualmente!
const { paymentTabs } = useCheckout(); // ← Já filtrado pelo país

// ❌ NÃO FAZER:
const filtered = paymentTabs.filter(tab => 
  country.paymentMethods.includes(tab.id)
);
```

### 4. localStorage vs Manual Selection

```typescript
// Se usuário seleciona manualmente:
// → isManualSelection = true
// → GeoIP não detecta novamente
// → Persiste entre sessões

// Para resetar:
localStorage.removeItem("user_country");
```

---

## 🎓 Exemplos Avançados

### Tracking de Mudança de País

```typescript
import { useCountry } from "@/contexts/country-context";
import { useEffect, useRef } from "react";
import posthog from "posthog-js";

function MyComponent() {
  const { country } = useCountry();
  const previousCountry = useRef(country.code);
  
  useEffect(() => {
    if (previousCountry.current !== country.code) {
      posthog.capture("country_changed", {
        from: previousCountry.current,
        to: country.code,
        currency: country.currency,
        language: country.language,
      });
      previousCountry.current = country.code;
    }
  }, [country]);
  
  return <div>{country.name}</div>;
}
```

### Componente de Seletor Customizado

```typescript
import { useCountry } from "@/contexts/country-context";
import { COUNTRY_OPTIONS } from "@/constants/countries";

function CustomCountrySelector() {
  const { country, changeCountry } = useCountry();
  
  return (
    <select 
      value={country.code} 
      onChange={(e) => changeCountry(e.target.value as CountryCode)}
    >
      {COUNTRY_OPTIONS.map((option) => (
        <option key={option.code} value={option.code}>
          {option.flag} {option.name} ({option.currency})
        </option>
      ))}
    </select>
  );
}
```

---

## 📞 Suporte

- **Documentação Completa:** `_docs/COUNTRY_BASED_CHECKOUT.md`
- **Regras do Projeto:** `.cursorrules`
- **Environment Vars:** `apps/web/ENV.md`

---

**Última atualização:** 11/11/2025  
**Versão:** 1.0.0

