# 🌎 PROMPT: Implementação de Checkout Country-Based com Multi-Provider

## 📋 VISÃO GERAL

Transformar o sistema atual de checkout de **language-based** para **country-based**, com foco em Brasil e América Latina. O país do usuário determina:
- Idioma padrão
- Métodos de pagamento disponíveis  
- Provider de pagamento (Cakto vs EBANX)

---

## 🎯 OBJETIVOS

### 1. **UI/UX Changes**
- ❌ **REMOVER** o header atual (`CheckoutHeader`) que contém "Checkout seguro" e seletor de idioma
- ✅ **MOVER** seletor de idioma para dentro da seção "Informações de Contato" (lado direito do título)
- ✅ **TRANSFORMAR** seletor de idioma em seletor de país/idioma combinado

### 2. **Country-Based System**
- ✅ Detectar país do usuário via IP/geolocalização
- ✅ Mapear país → idioma padrão
- ✅ Mapear país → métodos de pagamento disponíveis
- ✅ Mapear país → payment provider (Cakto ou EBANX)

### 3. **Providers**
- **Brasil:** Provider atual da Cakto (manter como está)
- **LATAM + Internacional:** EBANX
- ✅ Controle configurável por país

---

## 🗺️ PAÍSES E CONFIGURAÇÕES

### Brasil (Prioridade Máxima)
```typescript
{
  code: "BR",
  name: "Brasil",
  flag: "🇧🇷",
  language: "pt",
  currency: "BRL",
  provider: "cakto",
  paymentMethods: ["pix", "pix_auto", "boleto", "credit_card", "picpay", "googlepay", "applepay", "openfinance_nubank"],
  documentTypes: ["cpf", "cnpj"],
  phoneFormat: "(99) 99999-9999",
  zipCodeFormat: "99999-999"
}
```

### América Latina (EBANX)

#### México
```typescript
{
  code: "MX",
  name: "México",
  flag: "🇲🇽",
  language: "es",
  currency: "MXN",
  provider: "ebanx",
  paymentMethods: ["credit_card", "spei", "oxxo"],
  documentTypes: ["curp", "rfc"],
  phoneFormat: "+52 99 9999 9999",
  zipCodeFormat: "99999"
}
```

#### Argentina
```typescript
{
  code: "AR",
  name: "Argentina",
  flag: "🇦🇷",
  language: "es",
  currency: "ARS",
  provider: "ebanx",
  paymentMethods: ["credit_card", "rapipago", "pagofacil"],
  documentTypes: ["dni", "cuit"],
  phoneFormat: "+54 9 99 9999 9999",
  zipCodeFormat: "9999"
}
```

#### Chile
```typescript
{
  code: "CL",
  name: "Chile",
  flag: "🇨🇱",
  language: "es",
  currency: "CLP",
  provider: "ebanx",
  paymentMethods: ["credit_card", "servipag", "multicaja"],
  documentTypes: ["rut"],
  phoneFormat: "+56 9 9999 9999",
  zipCodeFormat: "9999999"
}
```

#### Colômbia
```typescript
{
  code: "CO",
  name: "Colombia",
  flag: "🇨🇴",
  language: "es",
  currency: "COP",
  provider: "ebanx",
  paymentMethods: ["credit_card", "pse", "efecty", "baloto"],
  documentTypes: ["cc", "ce", "nit"],
  phoneFormat: "+57 ************",
  zipCodeFormat: "999999"
}
```

#### Peru
```typescript
{
  code: "PE",
  name: "Perú",
  flag: "🇵🇪",
  language: "es",
  currency: "PEN",
  provider: "ebanx",
  paymentMethods: ["credit_card", "pagoefectivo", "safetypay"],
  documentTypes: ["dni", "ruc"],
  phoneFormat: "+51 999 999 999",
  zipCodeFormat: "99999"
}
```

### Internacional (Fallback)
```typescript
{
  code: "US",
  name: "United States",
  flag: "🇺🇸",
  language: "en",
  currency: "USD",
  provider: "ebanx",
  paymentMethods: ["credit_card", "googlepay", "applepay"],
  documentTypes: ["ssn"],
  phoneFormat: "+****************",
  zipCodeFormat: "99999"
}
```

---

## 📁 ESTRUTURA DE ARQUIVOS A CRIAR/MODIFICAR

### 1. **Criar Types**
**Arquivo:** `apps/web/src/types/country.ts`

```typescript
export type PaymentProvider = "cakto" | "ebanx";

export type CountryCode = 
  | "BR" // Brasil
  | "MX" // México
  | "AR" // Argentina
  | "CL" // Chile
  | "CO" // Colômbia
  | "PE" // Peru
  | "US" // Estados Unidos
  | "OTHER"; // Fallback

export type DocumentType = 
  | "cpf" 
  | "cnpj" 
  | "rut" 
  | "dni" 
  | "cuit" 
  | "curp" 
  | "rfc" 
  | "cc" 
  | "ce" 
  | "nit" 
  | "ruc" 
  | "ssn";

export type LocalPaymentMethod =
  | "pix"
  | "pix_auto"
  | "boleto"
  | "spei"
  | "oxxo"
  | "rapipago"
  | "pagofacil"
  | "servipag"
  | "multicaja"
  | "pse"
  | "efecty"
  | "baloto"
  | "pagoefectivo"
  | "safetypay";

export type CountryConfig = {
  code: CountryCode;
  name: string;
  flag: string;
  language: "pt" | "es" | "en";
  currency: string;
  provider: PaymentProvider;
  paymentMethods: (PaymentMethod | LocalPaymentMethod)[];
  documentTypes: DocumentType[];
  phoneFormat: string;
  zipCodeFormat: string;
  zipCodeLabel: string; // "CEP" | "Código Postal" | "ZIP Code"
  documentLabel: string; // "CPF/CNPJ" | "RUT" | "DNI" | etc
};

export type UserCountryData = {
  countryCode: CountryCode;
  detectedIp?: string;
  detectedAt?: string;
  isManualSelection?: boolean;
};
```

### 2. **Criar Configurações de Países**
**Arquivo:** `apps/web/src/constants/countries.ts`

```typescript
import type { CountryConfig, CountryCode } from "@/types/country";

export const COUNTRIES: Record<CountryCode, CountryConfig> = {
  BR: {
    code: "BR",
    name: "Brasil",
    flag: "🇧🇷",
    language: "pt",
    currency: "BRL",
    provider: "cakto",
    paymentMethods: [
      "pix",
      "pix_auto",
      "boleto",
      "credit_card",
      "picpay",
      "googlepay",
      "applepay",
      "openfinance_nubank",
    ],
    documentTypes: ["cpf", "cnpj"],
    phoneFormat: "(99) 99999-9999",
    zipCodeFormat: "99999-999",
    zipCodeLabel: "CEP",
    documentLabel: "CPF/CNPJ",
  },
  MX: {
    code: "MX",
    name: "México",
    flag: "🇲🇽",
    language: "es",
    currency: "MXN",
    provider: "ebanx",
    paymentMethods: ["credit_card", "spei", "oxxo"],
    documentTypes: ["curp", "rfc"],
    phoneFormat: "+52 99 9999 9999",
    zipCodeFormat: "99999",
    zipCodeLabel: "Código Postal",
    documentLabel: "CURP/RFC",
  },
  AR: {
    code: "AR",
    name: "Argentina",
    flag: "🇦🇷",
    language: "es",
    currency: "ARS",
    provider: "ebanx",
    paymentMethods: ["credit_card", "rapipago", "pagofacil"],
    documentTypes: ["dni", "cuit"],
    phoneFormat: "+54 9 99 9999 9999",
    zipCodeFormat: "9999",
    zipCodeLabel: "Código Postal",
    documentLabel: "DNI/CUIT",
  },
  CL: {
    code: "CL",
    name: "Chile",
    flag: "🇨🇱",
    language: "es",
    currency: "CLP",
    provider: "ebanx",
    paymentMethods: ["credit_card", "servipag", "multicaja"],
    documentTypes: ["rut"],
    phoneFormat: "+56 9 9999 9999",
    zipCodeFormat: "9999999",
    zipCodeLabel: "Código Postal",
    documentLabel: "RUT",
  },
  CO: {
    code: "CO",
    name: "Colombia",
    flag: "🇨🇴",
    language: "es",
    currency: "COP",
    provider: "ebanx",
    paymentMethods: ["credit_card", "pse", "efecty", "baloto"],
    documentTypes: ["cc", "ce", "nit"],
    phoneFormat: "+57 ************",
    zipCodeFormat: "999999",
    zipCodeLabel: "Código Postal",
    documentLabel: "CC/CE/NIT",
  },
  PE: {
    code: "PE",
    name: "Perú",
    flag: "🇵🇪",
    language: "es",
    currency: "PEN",
    provider: "ebanx",
    paymentMethods: ["credit_card", "pagoefectivo", "safetypay"],
    documentTypes: ["dni", "ruc"],
    phoneFormat: "+51 999 999 999",
    zipCodeFormat: "99999",
    zipCodeLabel: "Código Postal",
    documentLabel: "DNI/RUC",
  },
  US: {
    code: "US",
    name: "United States",
    flag: "🇺🇸",
    language: "en",
    currency: "USD",
    provider: "ebanx",
    paymentMethods: ["credit_card", "googlepay", "applepay"],
    documentTypes: ["ssn"],
    phoneFormat: "+****************",
    zipCodeFormat: "99999",
    zipCodeLabel: "ZIP Code",
    documentLabel: "SSN",
  },
  OTHER: {
    code: "OTHER",
    name: "International",
    flag: "🌎",
    language: "en",
    currency: "USD",
    provider: "ebanx",
    paymentMethods: ["credit_card", "googlepay", "applepay"],
    documentTypes: ["ssn"],
    phoneFormat: "+****************",
    zipCodeFormat: "99999",
    zipCodeLabel: "ZIP Code",
    documentLabel: "Tax ID",
  },
};

// Lista de países para o selector (ordenada por prioridade)
export const COUNTRY_OPTIONS: CountryConfig[] = [
  COUNTRIES.BR,  // Brasil (prioridade máxima)
  COUNTRIES.MX,
  COUNTRIES.AR,
  COUNTRIES.CL,
  COUNTRIES.CO,
  COUNTRIES.PE,
  COUNTRIES.US,
];

// Mapeamento de idioma → país padrão (para compatibilidade com URLs /pt/, /es/, /en/)
export const LANGUAGE_TO_DEFAULT_COUNTRY: Record<string, CountryCode> = {
  pt: "BR",
  es: "MX", // México como padrão para espanhol
  en: "US",
};
```

### 3. **Criar Context de País**
**Arquivo:** `apps/web/src/contexts/country-context.tsx`

```typescript
"use client";

import { createContext, useContext, useState, useEffect, ReactNode, useMemo } from "react";
import { useRouter, usePathname } from "next/navigation";
import type { CountryCode, CountryConfig, UserCountryData } from "@/types/country";
import { COUNTRIES, LANGUAGE_TO_DEFAULT_COUNTRY } from "@/constants/countries";
import { useLocale } from "./intl-context";

type CountryContextType = {
  country: CountryConfig;
  userCountryData: UserCountryData;
  changeCountry: (countryCode: CountryCode) => void;
  isLoadingCountry: boolean;
};

const CountryContext = createContext<CountryContextType | undefined>(undefined);

export function CountryProvider({ children }: { children: ReactNode }) {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  
  const [userCountryData, setUserCountryData] = useState<UserCountryData>(() => {
    // 1. Tentar ler do cookie/localStorage
    if (typeof window !== "undefined") {
      const stored = localStorage.getItem("user_country");
      if (stored) {
        try {
          return JSON.parse(stored);
        } catch {}
      }
    }
    
    // 2. Fallback para país padrão do idioma
    const defaultCountry = LANGUAGE_TO_DEFAULT_COUNTRY[locale] || "BR";
    return {
      countryCode: defaultCountry,
      isManualSelection: false,
    };
  });

  const [isLoadingCountry, setIsLoadingCountry] = useState(true);

  // Detectar país via IP na primeira montagem
  useEffect(() => {
    const detectCountry = async () => {
      // Se já tem seleção manual, não sobrescrever
      if (userCountryData.isManualSelection) {
        setIsLoadingCountry(false);
        return;
      }

      try {
        // TODO: Implementar chamada para API de geolocalização
        // const response = await fetch('/api/geoip');
        // const data = await response.json();
        // const detectedCountry = data.countryCode as CountryCode;
        
        // Por enquanto, usar idioma como base
        const detectedCountry = LANGUAGE_TO_DEFAULT_COUNTRY[locale] || "BR";
        
        setUserCountryData({
          countryCode: detectedCountry,
          detectedIp: "0.0.0.0", // TODO: IP real
          detectedAt: new Date().toISOString(),
          isManualSelection: false,
        });
      } catch (error) {
        console.error("Erro ao detectar país:", error);
        // Fallback para Brasil
        setUserCountryData({
          countryCode: "BR",
          isManualSelection: false,
        });
      } finally {
        setIsLoadingCountry(false);
      }
    };

    detectCountry();
  }, [locale, userCountryData.isManualSelection]);

  // Salvar no localStorage sempre que mudar
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("user_country", JSON.stringify(userCountryData));
    }
  }, [userCountryData]);

  const country = useMemo(() => {
    return COUNTRIES[userCountryData.countryCode] || COUNTRIES.BR;
  }, [userCountryData.countryCode]);

  const changeCountry = (countryCode: CountryCode) => {
    const newCountry = COUNTRIES[countryCode];
    if (!newCountry) return;

    // Atualizar estado
    setUserCountryData({
      countryCode,
      isManualSelection: true,
      detectedAt: new Date().toISOString(),
    });

    // Mudar idioma se necessário
    if (newCountry.language !== locale) {
      const nextPath = pathname?.replace(`/${locale}/`, `/${newCountry.language}/`) || "/";
      
      if (typeof document !== "undefined") {
        document.cookie = `NEXT_LOCALE=${newCountry.language}; max-age=31536000; path=/; SameSite=Lax`;
      }
      
      router.push(nextPath);
      router.refresh();
    }
  };

  const value = useMemo(
    () => ({
      country,
      userCountryData,
      changeCountry,
      isLoadingCountry,
    }),
    [country, userCountryData, isLoadingCountry]
  );

  return (
    <CountryContext.Provider value={value}>
      {children}
    </CountryContext.Provider>
  );
}

export function useCountry() {
  const context = useContext(CountryContext);
  if (context === undefined) {
    throw new Error("useCountry must be used within CountryProvider");
  }
  return context;
}
```

### 4. **Criar Country Selector Component**
**Arquivo:** `apps/web/src/components/checkout/country-selector.tsx`

```typescript
"use client";

import { useMemo, useState } from "react";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useCountry } from "@/contexts/country-context";
import { COUNTRY_OPTIONS } from "@/constants/countries";
import type { CountryCode } from "@/types/country";

export function CountrySelector() {
  const { country, changeCountry } = useCountry();
  const [open, setOpen] = useState(false);

  const handleSelect = (countryCode: CountryCode) => {
    if (countryCode === country.code) {
      setOpen(false);
      return;
    }

    changeCountry(countryCode);
    setOpen(false);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger className="flex items-center gap-2 rounded-md px-3 py-1.5 text-sm font-medium transition-colors hover:bg-foreground/5 border border-gray-200">
        <span className="text-lg leading-none">{country.flag}</span>
        <span className="hidden sm:inline">{country.name}</span>
        <ChevronDownIcon className="size-4 opacity-60" />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {COUNTRY_OPTIONS.map((option) => (
          <DropdownMenuItem
            key={option.code}
            onSelect={(event) => {
              event.preventDefault();
              handleSelect(option.code);
            }}
            className="flex items-center gap-3"
          >
            <span className="text-lg leading-none">{option.flag}</span>
            <span className="flex-1">
              <span className="block font-medium">{option.name}</span>
              <span className="block text-xs text-muted-foreground">
                {option.currency} • {option.language.toUpperCase()}
              </span>
            </span>
            {option.code === country.code && (
              <span className="text-emerald-500">✓</span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
```

### 5. **Modificar CheckoutComponent**
**Arquivo:** `apps/web/src/components/checkout/CheckoutComponent.tsx`

**MODIFICAR a linha 36-44:**

```typescript
// ANTES:
<div className="flex items-center gap-2">
  <h2
    className="text-lg font-bold"
    style={{ color: settings.text?.color.primary }}
  >
    {t("checkout.contact_info_title")}
  </h2>
</div>

// DEPOIS:
<div className="flex items-center justify-between gap-4">
  <h2
    className="text-lg font-bold"
    style={{ color: settings.text?.color.primary }}
  >
    {t("checkout.contact_info_title")}
  </h2>
  <CountrySelector />
</div>
```

**ADICIONAR import no topo:**
```typescript
import { CountrySelector } from "./country-selector";
import { useCountry } from "@/contexts/country-context";
```

**MODIFICAR campos de documento (linha 63-71):**

```typescript
// ANTES:
<TextField
  name="cpf"
  size="sm"
  label={t("form.labels.cpf_cnpj")}
  cpfCnpj
  placeholder={t("form.placeholders.cpf_cnpj")}
/>

// DEPOIS:
const { country } = useCountry();

<TextField
  name="document"
  size="sm"
  label={country.documentLabel}
  placeholder={country.documentLabel}
  mask={country.documentTypes[0] === "cpf" ? undefined : country.phoneFormat}
  cpfCnpj={country.code === "BR"}
/>
```

### 6. **Modificar checkout-client.tsx**
**Arquivo:** `apps/web/src/components/checkout/checkout-client.tsx`

**REMOVER** a linha 17 com `<CheckoutHeader />`:

```typescript
// ANTES:
export function CheckoutClient({ initialData, checkoutId }: CheckoutClientProps) {
  return (
    <CheckoutProvider initialData={initialData as any} checkoutId={checkoutId}>
      <div className="flex min-h-screen flex-col">
        <CheckoutHeader />  {/* ← REMOVER ESTA LINHA */}
        <main className="flex-1">
          <CheckoutForm />
        </main>
      </div>
    </CheckoutProvider>
  );
}

// DEPOIS:
export function CheckoutClient({ initialData, checkoutId }: CheckoutClientProps) {
  return (
    <CheckoutProvider initialData={initialData as any} checkoutId={checkoutId}>
      <CountryProvider>
        <div className="flex min-h-screen">
          <main className="flex-1">
            <CheckoutForm />
          </main>
        </div>
      </CountryProvider>
    </CheckoutProvider>
  );
}
```

**ADICIONAR import:**
```typescript
import { CountryProvider } from "@/contexts/country-context";
```

### 7. **Modificar CheckoutProvider para filtrar métodos por país**
**Arquivo:** `apps/web/src/contexts/checkout-context.tsx`

**ADICIONAR** no início da função `CheckoutProvider`:

```typescript
import { useCountry } from "./country-context";

export function CheckoutProvider({ ... }) {
  const { country } = useCountry(); // ← ADICIONAR
  
  // ... resto do código ...
  
  // MODIFICAR paymentTabs useMemo (linha ~385)
  const paymentTabs = useMemo(() => {
    const paymentMethods = offer?.product?.paymentMethods || [];
    
    // FILTRAR por métodos disponíveis no país
    const availableInCountry = paymentMethods.filter(({ type }) => 
      country.paymentMethods.includes(type as any)
    );
    
    let allowedPaymentsMethods: PaymentMethod[] =
      offer?.type === "subscription"
        ? availableInCountry?.map(({ type }) => type) || ["credit_card"]
        : availableInCountry?.map(({ type }) => type);
    
    // ... resto da lógica ...
  }, [offer, country]); // ← ADICIONAR country na dependência
}
```

### 8. **Criar API Route para GeoIP (Opcional)**
**Arquivo:** `apps/web/src/app/api/geoip/route.ts`

```typescript
import { NextRequest, NextResponse } from "next/server";
import { getGeoipResolverUrl } from "@/lib/env";

export async function GET(request: NextRequest) {
  try {
    const ip = request.headers.get("x-forwarded-for") || 
               request.headers.get("x-real-ip") || 
               "unknown";
    
    // Usar serviço de GeoIP (pode ser ipapi.co, ipinfo.io, etc)
    const geoipUrl = getGeoipResolverUrl();
    const response = await fetch(`${geoipUrl}/${ip}`);
    const data = await response.json();
    
    return NextResponse.json({
      ip,
      countryCode: data.country_code || "BR",
      country: data.country || "Brazil",
      city: data.city,
      region: data.region,
    });
  } catch (error) {
    console.error("GeoIP error:", error);
    return NextResponse.json(
      { countryCode: "BR", country: "Brazil" },
      { status: 200 }
    );
  }
}
```

### 9. **Atualizar variáveis de ambiente**
**Arquivo:** `apps/web/.env.local` (ou `.env`)

```bash
# GeoIP Service
NEXT_PUBLIC_GEOIP_API_URL=https://ipapi.co
# ou
# NEXT_PUBLIC_GEOIP_API_URL=https://ipinfo.io

# Payment Providers
NEXT_PUBLIC_EBANX_PUBLIC_KEY=seu_ebanx_public_key
EBANX_INTEGRATION_KEY=seu_ebanx_integration_key
```

**Adicionar em:** `apps/web/src/lib/env.ts`

```typescript
export function getGeoipApiUrl(): string {
  return process.env.NEXT_PUBLIC_GEOIP_API_URL || "https://ipapi.co";
}

export function getEbanxPublicKey(): string {
  return process.env.NEXT_PUBLIC_EBANX_PUBLIC_KEY || "";
}
```

---

## 🔄 FLUXO DE FUNCIONAMENTO

### 1. **Detecção de País**
```
Usuário acessa checkout
  ↓
CountryProvider detecta:
  1. localStorage/cookie (se existe seleção prévia)
  2. Se não, faz chamada para /api/geoip
  3. Detecta país por IP
  4. Define país padrão
  ↓
Country detectado → Define idioma padrão
  ↓
Redireciona para URL localizada (/pt/, /es/, /en/)
```

### 2. **Seleção Manual de País**
```
Usuário clica no CountrySelector (🇧🇷 Brasil)
  ↓
Dropdown mostra países disponíveis
  ↓
Usuário seleciona país (ex: 🇲🇽 México)
  ↓
Sistema:
  1. Atualiza country no context
  2. Muda idioma automaticamente (pt → es)
  3. Atualiza URL (/pt/xxx → /es/xxx)
  4. Filtra métodos de pagamento
  5. Atualiza labels de documentos
  6. Salva seleção no localStorage
```

### 3. **Renderização de Métodos de Pagamento**
```
CheckoutProvider recebe country do CountryContext
  ↓
Filtra paymentMethods baseado em country.paymentMethods
  ↓
Renderiza apenas métodos disponíveis no país
  ↓
Exemplo:
  - Brasil: PIX, Boleto, Cartão
  - México: SPEI, OXXO, Cartão
  - Argentina: Rapipago, PagoFácil, Cartão
```

### 4. **Processamento de Pagamento**
```
Usuário preenche formulário e clica "Pagar"
  ↓
Sistema verifica country.provider:
  - "cakto" → Usa API atual da Cakto
  - "ebanx" → Usa API do EBANX
  ↓
Payload inclui:
  - country: "BR" | "MX" | ...
  - currency: "BRL" | "MXN" | ...
  - document: CPF | RUT | DNI | ...
  - paymentMethod com informações específicas do país
```

---

## ✅ CHECKLIST DE IMPLEMENTAÇÃO

### Fase 1: Setup Base
- [ ] Criar `types/country.ts` com todos os tipos
- [ ] Criar `constants/countries.ts` com configurações
- [ ] Criar `contexts/country-context.tsx`
- [ ] Criar `components/checkout/country-selector.tsx`

### Fase 2: Integração UI
- [ ] Remover `<CheckoutHeader />` de `checkout-client.tsx`
- [ ] Adicionar `<CountryProvider>` em `checkout-client.tsx`
- [ ] Adicionar `<CountrySelector />` em `CheckoutComponent.tsx`
- [ ] Atualizar título "Informações de Contato" para ter flex justify-between

### Fase 3: Lógica de Negócio
- [ ] Modificar `CheckoutProvider` para filtrar payment methods por país
- [ ] Atualizar campos de documento para serem dinâmicos
- [ ] Atualizar máscaras de telefone e CEP por país
- [ ] Implementar detecção de país via GeoIP

### Fase 4: Tradução e i18n
- [ ] Adicionar traduções em `pt.json` para novos países
- [ ] Adicionar traduções em `es.json` para América Latina
- [ ] Adicionar traduções em `en.json` para internacional
- [ ] Atualizar mensagens de validação por país

### Fase 5: Payment Providers
- [ ] Criar helpers para detectar provider (`getPaymentProvider()`)
- [ ] Criar adapter para EBANX API
- [ ] Modificar `startPayment()` para escolher provider correto
- [ ] Adicionar testes de integração com EBANX

### Fase 6: Testes
- [ ] Testar mudança de país em tempo real
- [ ] Testar detecção automática por IP
- [ ] Testar cada método de pagamento por país
- [ ] Testar fluxo completo BR → MX → AR → CL

---

## 📝 NOTAS IMPORTANTES

### 1. **Compatibilidade com URLs existentes**
- Manter URLs atuais funcionando: `/pt/7rohg3i`, `/en/7rohg3i`, `/es/7rohg3i`
- País é detectado/selecionado independente da URL
- URL define idioma inicial, país pode ser diferente

### 2. **Persistência**
- Salvar país selecionado em `localStorage`
- Salvar também em cookie para SSR
- Prioridade: Manual > Cookie > GeoIP > Default

### 3. **Performance**
- Detecção de país deve ser async e non-blocking
- Mostrar skeleton enquanto detecta
- Fallback rápido se API GeoIP falhar

### 4. **UX**
- Indicar país detectado automaticamente
- Permitir mudança fácil via dropdown
- Mostrar flag + nome do país
- Mostrar moeda e idioma no dropdown

### 5. **Segurança**
- Validar país no backend
- Não confiar apenas no frontend
- Incluir país em todas as transações
- Logs de mudança de país para auditoria

---

## 🎯 RESULTADO ESPERADO

### Visual Final

```
┌─────────────────────────────────────────────────────┐
│                                                     │
│  [Timer: Oferta por tempo limitado - 00:15:00]     │
│                                                     │
│  ┌────────────────────────────────────────────┐    │
│  │  Informações de Contato    [🇧🇷 Brasil ▼] │    │
│  │                                            │    │
│  │  Nome completo                             │    │
│  │  [João Silva________________]              │    │
│  │                                            │    │
│  │  E-mail                                    │    │
│  │  [joao@email.com____________]              │    │
│  │                                            │    │
│  │  CPF/CNPJ              Celular             │    │
│  │  [123.456.789-09]      [(11) 99999-9999]  │    │
│  │                                            │    │
│  │  Forma de Pagamento                        │    │
│  │  [PIX] [Boleto] [Cartão de Crédito]       │    │
│  │                                            │    │
│  └────────────────────────────────────────────┘    │
│                                                     │
└─────────────────────────────────────────────────────┘
```

**Se usuário selecionar 🇲🇽 México:**
```
┌─────────────────────────────────────────────────────┐
│                                                     │
│  [Timer: Oferta por tiempo limitado - 00:15:00]    │
│                                                     │
│  ┌────────────────────────────────────────────┐    │
│  │  Información de Contacto   [🇲🇽 México ▼] │    │
│  │                                            │    │
│  │  Nombre completo                           │    │
│  │  [Juan García______________]               │    │
│  │                                            │    │
│  │  Correo electrónico                        │    │
│  │  [juan@email.com___________]               │    │
│  │                                            │    │
│  │  CURP/RFC              Teléfono            │    │
│  │  [XXXXX000000XXXX00]   [+52 55 1234 5678] │    │
│  │                                            │    │
│  │  Método de Pago                            │    │
│  │  [Tarjeta] [SPEI] [OXXO]                  │    │
│  │                                            │    │
│  └────────────────────────────────────────────┘    │
│                                                     │
└─────────────────────────────────────────────────────┘
```

---

## 🚀 ORDEM DE IMPLEMENTAÇÃO RECOMENDADA

1. **Criar types e constants** (1-2 horas)
2. **Criar CountryContext** (2-3 horas)
3. **Criar CountrySelector component** (1-2 horas)
4. **Remover CheckoutHeader e integrar CountrySelector** (1 hora)
5. **Modificar CheckoutProvider para filtrar payment methods** (2-3 horas)
6. **Atualizar campos dinâmicos (documento, telefone, CEP)** (2-3 horas)
7. **Implementar GeoIP detection** (2-3 horas)
8. **Adicionar traduções** (1-2 horas)
9. **Criar adapter EBANX** (4-6 horas)
10. **Testes completos** (3-4 horas)

**Total estimado:** 20-30 horas

---

## 📚 REFERÊNCIAS

### APIs de GeoIP
- **ipapi.co:** https://ipapi.co/ (free tier: 1000 req/day)
- **ipinfo.io:** https://ipinfo.io/ (free tier: 50k req/month)
- **ip-api.com:** https://ip-api.com/ (free, sem https no free)

### EBANX Documentation
- **Docs:** https://developers.ebanx.com/
- **Payment Methods:** https://developers.ebanx.com/api-reference/payment-methods
- **Countries:** https://developers.ebanx.com/api-reference/countries

### Máscaras de Documento por País
- **Brasil:** CPF (999.999.999-99), CNPJ (99.999.999/9999-99)
- **México:** CURP (18 chars), RFC (13 chars)
- **Argentina:** DNI (8 digits), CUIT (99-99999999-9)
- **Chile:** RUT (99.999.999-9)
- **Colômbia:** CC (10 digits), CE (7 digits)
- **Peru:** DNI (8 digits), RUC (11 digits)

---

**Criado em:** 11/11/2025  
**Versão:** 1.0.0  
**Status:** Pronto para implementação  
**Complexidade:** Alta  
**Prioridade:** Alta

---

Fim do prompt. Este documento contém TODAS as informações necessárias para implementar o sistema country-based no checkout.

