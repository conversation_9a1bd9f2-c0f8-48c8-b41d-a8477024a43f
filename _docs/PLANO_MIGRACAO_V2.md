# Plano Completo de Migração - Checkout V2

## 📋 Visão Geral

Este documento descreve o plano completo para migração do checkout atual (Vite + React) para uma versão 2 usando Next.js com Server-Side Rendering (SSR), multi-idioma baseado em IP e melhor compatibilidade com navegadores de redes sociais.

## 🎯 Objetivos Principais

1. **Server-Side Rendering (SSR)**: Renderizar conteúdo crítico no servidor para melhor compatibilidade
2. **Multi-idioma**: Suporte para Português, Espanhol e Inglês baseado em detecção de IP
3. **Compatibilidade com Navegadores de Redes Sociais**: Resolver problemas com Instagram, TikTok e outros navegadores embutidos
4. **Manter Mesmas APIs**: Reutilizar todas as APIs existentes sem modificações
5. **Expansão para América Latina**: Preparar para operação em toda a região

## 📊 Análise do Projeto Atual

### Stack Tecnológica Atual
- **Frontend**: Vite + React 18 + TypeScript
- **Roteamento**: React Router DOM
- **Estado**: React Query + Context API
- **Formulários**: React Hook Form + Yup
- **Estilização**: Tailwind CSS
- **HTTP Client**: Axios
- **Build**: Vite

### Funcionalidades Principais
1. **Sistema de Checkout Completo**
   - Formulário de dados do cliente
   - Múltiplos métodos de pagamento (PIX, Boleto, Cartão, Apple Pay, Google Pay, PicPay, Nubank)
   - Sistema de parcelamento
   - Validação de cupons
   - Sistema de bumps/upsells
   - Sistema de endereço (CEP)

2. **Integrações**
   - Tracking Pixels (Facebook, TikTok, Google Ads, Kwai)
   - PostHog (Analytics e Session Replay)
   - 3DS (Cielo e Pagar.me)
   - Antifraude (Nethone)
   - Fingerprinting (ClientJS)

3. **Builder de Checkout**
   - Sistema de componentes configuráveis
   - Layout responsivo
   - Temas customizáveis

4. **Internacionalização**
   - Apenas mensagens de erro (pt-BR e en-US)
   - Sem detecção automática de idioma

## 🏗️ Arquitetura da Versão 2

### Stack Tecnológica Nova
- **Frontend**: Next.js 16 (App Router) + React 19 + TypeScript
- **Roteamento**: Next.js App Router (Server Components)
- **Estado**: React Query + Server Actions
- **Formulários**: React Hook Form + Zod (validação server-side)
- **Estilização**: Tailwind CSS 4
- **HTTP Client**: Fetch API (server-side) + Axios (client-side quando necessário)
- **i18n**: next-intl ou similar com detecção de IP
- **Build**: Next.js (SSR/SSG)

### Estrutura de Pastas Proposta

```
cakto-checkoutv2/
├── apps/
│   ├── web/                    # Next.js App
│   │   ├── src/
│   │   │   ├── app/
│   │   │   │   ├── [locale]/   # Rotas internacionalizadas
│   │   │   │   │   ├── [id]/    # Checkout dinâmico
│   │   │   │   │   │   ├── page.tsx
│   │   │   │   │   │   └── loading.tsx
│   │   │   │   │   ├── preview/
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   └── layout.tsx
│   │   │   │   ├── api/         # API Routes (quando necessário)
│   │   │   │   └── layout.tsx
│   │   │   ├── components/      # Componentes React
│   │   │   │   ├── checkout/
│   │   │   │   ├── payments/
│   │   │   │   ├── builder/
│   │   │   │   ├── common/
│   │   │   │   └── pixels/
│   │   │   ├── lib/             # Utilitários
│   │   │   ├── hooks/           # Custom Hooks
│   │   │   ├── services/       # Serviços de API
│   │   │   ├── contexts/       # Context Providers
│   │   │   ├── types/           # TypeScript Types
│   │   │   └── i18n/           # Traduções
│   │   │       ├── pt.json
│   │   │       ├── es.json
│   │   │       └── en.json
│   │   └── middleware.ts        # Detecção de idioma/IP
│   └── server/                  # Backend API (Elysia + oRPC)
│       └── src/
├── packages/
│   ├── api/                     # API Layer
│   └── db/                      # Database
└── package.json
```

## 📝 Tarefas Detalhadas

### Fase 1: Configuração Inicial e Infraestrutura

#### 1.1 Setup do Projeto Next.js
- [ ] Configurar Next.js 16 com App Router
- [ ] Configurar TypeScript com paths aliases
- [ ] Configurar Tailwind CSS 4
- [ ] Configurar ESLint e Prettier
- [ ] Configurar variáveis de ambiente (.env.example)
- [ ] Configurar estrutura de pastas base

#### 1.2 Sistema de Internacionalização
- [ ] Instalar e configurar next-intl ou similar
- [ ] Criar estrutura de traduções (pt, es, en)
- [ ] Implementar middleware de detecção de idioma baseado em IP
- [ ] Configurar fallback de idioma (pt-BR como padrão)
- [ ] Criar hook useTranslation customizado
- [ ] Migrar mensagens de erro existentes para sistema de i18n

#### 1.3 Detecção de IP e Geolocalização
- [ ] Implementar serviço de detecção de IP (usar API como ipapi.co, ip-api.com ou similar)
- [ ] Criar middleware Next.js para detectar país/idioma
- [ ] Mapear países da América Latina para idiomas:
  - Brasil → Português (pt)
  - Argentina, Chile, Colômbia, México, etc. → Espanhol (es)
  - Outros → Inglês (en) como fallback
- [ ] Implementar cache de detecção de IP (evitar múltiplas chamadas)
- [ ] Criar fallback para quando detecção falhar

### Fase 2: Migração de Componentes Core

#### 2.1 Componentes de Layout
- [ ] Migrar layout principal (CheckoutPage)
- [ ] Migrar componentes de loading e error
- [ ] Adaptar sistema de rotas para Next.js App Router
- [ ] Implementar Server Components onde possível

#### 2.2 Sistema de Contextos
- [ ] Migrar CheckoutContext para Server Components + Client Components
- [ ] Migrar CheckoutModeContext
- [ ] Migrar NotificationContext
- [ ] Adaptar para Next.js (usar Server Actions quando possível)

#### 2.3 Componentes de Formulário
- [ ] Migrar CheckoutForm (adaptar para Server Actions)
- [ ] Migrar AddressForm
- [ ] Migrar componentes de input (TextField, TextFieldPhoneNumber)
- [ ] Migrar validações (adaptar Yup para Zod)
- [ ] Implementar validação server-side

#### 2.4 Componentes de Pagamento
- [ ] Migrar PaymentMethods
- [ ] Migrar todos os formulários de pagamento:
  - [ ] CreditCardForm
  - [ ] PixForm
  - [ ] PixAutoForm
  - [ ] BoletoForm
  - [ ] ApplePayForm
  - [ ] GooglePayForm
  - [ ] PicPayForm
  - [ ] NubankPayForm
- [ ] Adaptar sistema de 3DS para SSR
- [ ] Migrar sistema de parcelamento

### Fase 3: Serviços e Integrações

#### 3.1 Serviços de API
- [ ] Criar serviços de API server-side (usar fetch nativo)
- [ ] Migrar getCheckoutData (adaptar para Server Component)
- [ ] Migrar startPayment (adaptar para Server Action)
- [ ] Migrar getPaymentStatus
- [ ] Migrar getInstallments
- [ ] Migrar validateCouponService
- [ ] Migrar createAbandonment
- [ ] Manter compatibilidade com APIs existentes

#### 3.2 Integrações de Terceiros
- [ ] Migrar sistema de tracking pixels:
  - [ ] Facebook Pixel
  - [ ] TikTok Pixel
  - [ ] Google Ads
  - [ ] Kwai Pixel
- [ ] Adaptar PostHog para SSR
- [ ] Migrar sistema de 3DS (Cielo e Pagar.me)
- [ ] Adaptar Nethone (antifraude) para SSR
- [ ] Adaptar ClientJS (fingerprinting) - manter client-side
- [ ] Migrar sistema de CEP (busca de endereço)

#### 3.3 Sistema de Builder
- [ ] Migrar CheckoutConfigRenderer
- [ ] Migrar todos os componentes do builder:
  - [ ] CheckoutComponentAdvantage
  - [ ] CheckoutComponentCountdown
  - [ ] CheckoutComponentHeader
  - [ ] CheckoutComponentImage
  - [ ] CheckoutComponentText
  - [ ] CheckoutComponentVideo
  - [ ] CheckoutComponentTestimonial
  - [ ] CheckoutComponentSeal
  - [ ] CheckoutComponentList
  - [ ] CheckoutComponentMap
  - [ ] CheckoutComponentChat
  - [ ] CheckoutComponentFacebook
  - [ ] CheckoutComponentNotification
  - [ ] CheckoutComponentExitPopup
- [ ] Adaptar para Server Components quando possível

### Fase 4: Otimizações e Compatibilidade

#### 4.1 Compatibilidade com Navegadores de Redes Sociais
- [ ] Implementar polyfills para APIs não suportadas
- [ ] Otimizar JavaScript para navegadores antigos
- [ ] Testar em navegadores embutidos (Instagram, TikTok, Facebook)
- [ ] Implementar fallbacks para recursos não suportados
- [ ] Otimizar carregamento de recursos externos
- [ ] Implementar Progressive Enhancement

#### 4.2 Performance e SEO
- [ ] Implementar Server-Side Rendering para conteúdo crítico
- [ ] Otimizar imagens (Next.js Image)
- [ ] Implementar lazy loading de componentes
- [ ] Otimizar bundle size
- [ ] Implementar code splitting
- [ ] Adicionar meta tags dinâmicas
- [ ] Implementar Open Graph tags

#### 4.3 Tratamento de Erros
- [ ] Migrar sistema de tratamento de erros
- [ ] Implementar error boundaries
- [ ] Adaptar error logging para SSR
- [ ] Criar páginas de erro customizadas (404, 500)
- [ ] Implementar retry logic para APIs

### Fase 5: Testes e Validação

#### 5.1 Testes Funcionais
- [ ] Testar fluxo completo de checkout
- [ ] Testar todos os métodos de pagamento
- [ ] Testar sistema de cupons
- [ ] Testar sistema de bumps
- [ ] Testar validações de formulário
- [ ] Testar sistema de 3DS
- [ ] Testar tracking pixels

#### 5.2 Testes de Compatibilidade
- [ ] Testar em navegadores principais (Chrome, Firefox, Safari, Edge)
- [ ] Testar em navegadores móveis (iOS Safari, Chrome Mobile)
- [ ] Testar em navegadores embutidos (Instagram, TikTok, Facebook)
- [ ] Testar em diferentes resoluções
- [ ] Testar com conexões lentas

#### 5.3 Testes de Internacionalização
- [ ] Testar detecção de idioma por IP
- [ ] Testar troca manual de idioma
- [ ] Testar todas as traduções (pt, es, en)
- [ ] Validar formatação de moeda por país
- [ ] Validar formatação de data por país
- [ ] Testar países da América Latina

### Fase 6: Deploy e Monitoramento

#### 6.1 Configuração de Deploy
- [ ] Configurar Vercel/plataforma de deploy
- [ ] Configurar variáveis de ambiente
- [ ] Configurar domínios e SSL
- [ ] Configurar CDN
- [ ] Configurar cache strategies

#### 6.2 Monitoramento
- [ ] Configurar analytics
- [ ] Configurar error tracking
- [ ] Configurar performance monitoring
- [ ] Configurar uptime monitoring
- [ ] Implementar logging estruturado

#### 6.3 Documentação
- [ ] Documentar arquitetura
- [ ] Documentar APIs
- [ ] Documentar processo de deploy
- [ ] Criar guia de desenvolvimento
- [ ] Criar guia de troubleshooting

## 🔧 Considerações Técnicas Importantes

### Server-Side Rendering
- Renderizar conteúdo crítico no servidor (produto, preço, métodos de pagamento)
- Manter interatividade no cliente (formulários, validações)
- Usar Server Actions para submissão de formulários
- Implementar streaming para melhor performance

### Compatibilidade com Navegadores de Redes Sociais
- Evitar uso de APIs modernas não suportadas
- Implementar polyfills quando necessário
- Testar extensivamente em navegadores embutidos
- Otimizar JavaScript para carregamento rápido
- Minimizar dependências externas

### Internacionalização
- Detectar idioma no servidor (middleware)
- Armazenar preferência do usuário em cookie
- Permitir troca manual de idioma
- Formatar moeda e data conforme localização
- Validar campos conforme país (CPF para BR, etc.)

### Performance
- Implementar ISR (Incremental Static Regeneration) quando possível
- Usar Server Components para reduzir bundle size
- Implementar lazy loading de componentes pesados
- Otimizar imagens e assets
- Implementar cache strategies

## 📦 Dependências Principais

### Novas Dependências Necessárias
```json
{
  "next": "^16.0.0",
  "next-intl": "^3.0.0", // ou similar para i18n
  "zod": "^3.22.0", // validação server-side
  "@tanstack/react-query": "^5.0.0",
  "react-hook-form": "^7.50.0",
  "@hookform/resolvers": "^3.3.0"
}
```

### Dependências a Manter
- React Hook Form
- Tailwind CSS
- Sonner (toast notifications)
- React Query (para client-side)
- Axios (para client-side quando necessário)

### Dependências a Remover/Substituir
- React Router DOM → Next.js App Router
- Vite → Next.js build system
- Yup → Zod (para validação server-side)

## 🚀 Próximos Passos

1. **Revisar este plano** com a equipe
2. **Priorizar tarefas** conforme necessidade de negócio
3. **Criar issues** no sistema de gestão de projetos
4. **Iniciar Fase 1** (Configuração Inicial)
5. **Estabelecer milestones** e revisões periódicas

## 📚 Recursos e Referências

- [Next.js Documentation](https://nextjs.org/docs)
- [Next.js App Router](https://nextjs.org/docs/app)
- [next-intl Documentation](https://next-intl-docs.vercel.app/)
- [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations)
- [React Server Components](https://react.dev/blog/2023/03/22/react-labs-what-we-have-been-working-on-march-2023#react-server-components)

---

**Última atualização**: 2024
**Versão do documento**: 1.0

