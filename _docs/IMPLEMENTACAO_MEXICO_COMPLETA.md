# Implementação Completa: Métodos de Pagamento do México

**País:** 🇲🇽 México  
**Métodos:** OXXO (cash) + SPEI (transferência)  
**Tempo estimado:** 8-12 horas  
**Prioridade:** 🔴 CRÍTICA

---

## ✅ Checklist Rápido

### Backend (FastAPI)
- [ ] API retorna `oxxo` e `spei` quando `country=MX`
- [ ] Endpoint processa pagamento OXXO
- [ ] Endpoint processa pagamento SPEI
- [ ] Webhook OXXO configurado
- [ ] Webhook SPEI configurado

### Frontend (Next.js)
- [ ] OxxoIcon criado
- [ ] SpeiIcon criado
- [ ] OxxoForm criado
- [ ] SpeiForm criado
- [ ] OxxoPayment criado
- [ ] SpeiPayment criado
- [ ] Traduções es-MX adicionadas
- [ ] Types atualizados
- [ ] CheckoutProvider mapeado
- [ ] WaitingPayment atualizado

---

## 📦 IMPLEMENTAÇÃO PASSO A PASSO

### PASSO 1: Criar Ícones

#### 1.1 OxxoIcon

**Arquivo:** `/apps/web/src/components/icons/payment/OxxoIcon.tsx`

```typescript
export const OxxoIcon = ({ className }: { className?: string }) => (
  <svg 
    className={className} 
    viewBox="0 0 24 24" 
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="24" height="24" rx="4" fill="#FFDD00"/>
    <text 
      x="12" 
      y="16" 
      textAnchor="middle" 
      fontWeight="bold" 
      fontSize="10" 
      fill="#EC1C24"
    >
      OXXO
    </text>
  </svg>
);
```

#### 1.2 SpeiIcon

**Arquivo:** `/apps/web/src/components/icons/payment/SpeiIcon.tsx`

```typescript
export const SpeiIcon = ({ className }: { className?: string }) => (
  <svg 
    className={className} 
    viewBox="0 0 24 24" 
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="24" height="24" rx="4" fill="#0052CC"/>
    <path
      d="M8 8h8M8 12h8M8 16h8"
      stroke="white"
      strokeWidth="2"
      strokeLinecap="round"
    />
    <circle cx="5" cy="8" r="1" fill="white"/>
    <circle cx="5" cy="12" r="1" fill="white"/>
    <circle cx="5" cy="16" r="1" fill="white"/>
  </svg>
);
```

#### 1.3 Exportar Ícones

**Arquivo:** `/apps/web/src/components/icons/index.ts` (ou payment/index.ts)

```typescript
export { OxxoIcon } from "./payment/OxxoIcon";
export { SpeiIcon } from "./payment/SpeiIcon";
```

---

### PASSO 2: Criar Formulários

#### 2.1 OxxoForm

**Arquivo:** `/apps/web/src/components/payments/OxxoForm.tsx`

```typescript
"use client";

import { useTranslation } from "@/hooks/useTranslation";
import useSettings from "@/hooks/useSettings";
import { InformationCircleIcon } from "@heroicons/react/24/outline";

export function OxxoForm() {
  const { t } = useTranslation();
  const settings = useSettings();

  return (
    <div className="space-y-4">
      {/* Informações sobre OXXO */}
      <div 
        className="p-4 rounded-lg flex items-start gap-3"
        style={{ backgroundColor: settings.box.default.background.color }}
      >
        <InformationCircleIcon 
          className="w-6 h-6 flex-shrink-0"
          style={{ color: settings.text.color.active }}
        />
        <div>
          <h3 
            className="font-semibold mb-1 text-base"
            style={{ color: settings.text.color.primary }}
          >
            {t("payment.oxxo.title")}
          </h3>
          <p 
            className="text-sm"
            style={{ color: settings.text.color.secondary }}
          >
            {t("payment.oxxo.description")}
          </p>
        </div>
      </div>

      {/* Como funciona */}
      <div className="space-y-3">
        <p 
          className="text-sm font-semibold"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.oxxo.how_it_works")}
        </p>
        <ol className="space-y-2">
          {[1, 2, 3, 4].map((step) => (
            <li key={step} className="flex items-start gap-2">
              <span 
                className="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold"
                style={{ 
                  backgroundColor: settings.box.selected.header.background.color,
                  color: settings.box.selected.header.text.color.primary
                }}
              >
                {step}
              </span>
              <span 
                className="text-sm pt-0.5"
                style={{ color: settings.text.color.secondary }}
              >
                {t(`payment.oxxo.step_${step}`)}
              </span>
            </li>
          ))}
        </ol>
      </div>

      {/* Aviso de processamento */}
      <div 
        className="p-3 rounded-lg border"
        style={{ 
          backgroundColor: settings.box.unselected.background.color,
          borderColor: settings.box.unselected.header.background.color 
        }}
      >
        <p 
          className="text-xs flex items-start gap-2"
          style={{ color: settings.text.color.secondary }}
        >
          <span className="text-base">⏱️</span>
          <span>{t("payment.oxxo.processing_time")}</span>
        </p>
      </div>
    </div>
  );
}
```

#### 2.2 SpeiForm

**Arquivo:** `/apps/web/src/components/payments/SpeiForm.tsx`

```typescript
"use client";

import { useTranslation } from "@/hooks/useTranslation";
import useSettings from "@/hooks/useSettings";
import { CheckCircleIcon } from "@heroicons/react/24/solid";

export function SpeiForm() {
  const { t } = useTranslation();
  const settings = useSettings();

  return (
    <div className="space-y-4">
      {/* Informações sobre SPEI */}
      <div 
        className="p-4 rounded-lg"
        style={{ backgroundColor: settings.box.default.background.color }}
      >
        <h3 
          className="font-semibold mb-2 text-base"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.spei.title")}
        </h3>
        <p 
          className="text-sm"
          style={{ color: settings.text.color.secondary }}
        >
          {t("payment.spei.description")}
        </p>
      </div>

      {/* Vantagens */}
      <div className="space-y-3">
        <p 
          className="text-sm font-semibold"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.spei.advantages")}
        </p>
        <ul className="space-y-2">
          {[1, 2, 3].map((num) => (
            <li key={num} className="flex items-start gap-2">
              <CheckCircleIcon 
                className="w-5 h-5 flex-shrink-0 mt-0.5"
                style={{ color: settings.text.color.active }}
              />
              <span 
                className="text-sm"
                style={{ color: settings.text.color.secondary }}
              >
                {t(`payment.spei.advantage_${num}`)}
              </span>
            </li>
          ))}
        </ul>
      </div>

      {/* Confirmação instantânea */}
      <div 
        className="p-3 rounded-lg"
        style={{ 
          backgroundColor: settings.box.selected.background.color,
          borderColor: settings.box.selected.header.background.color,
          borderWidth: "1px"
        }}
      >
        <p 
          className="text-sm flex items-start gap-2 font-medium"
          style={{ color: settings.text.color.active }}
        >
          <span className="text-base">⚡</span>
          <span>{t("payment.spei.instant_confirmation")}</span>
        </p>
      </div>
    </div>
  );
}
```

---

### PASSO 3: Atualizar CheckoutProvider

**Arquivo:** `/apps/web/src/contexts/checkout-context.tsx`

```typescript
// 1. Importar novos componentes e ícones
import { OxxoForm } from "@/components/payments/OxxoForm";
import { SpeiForm } from "@/components/payments/SpeiForm";
import { OxxoIcon, SpeiIcon } from "@/components/icons";

// 2. Atualizar mapIcons (linha ~365)
const mapIcons: Record<string, React.ComponentType<{ className?: string }>> = {
  // Brasil
  credit_card: CreditCardIcon,
  boleto: BarcodeIcon,
  pix: PixIcon,
  pix_auto: PixAutoIcon,
  picpay: PicPayIcon,
  openfinance_nubank: NubankIcon,
  
  // México
  oxxo: OxxoIcon,
  spei: SpeiIcon,
  
  // Global
  applepay: ApplePayIcon,
  googlepay: GooglePayIcon,
};

// 3. Atualizar mapForms (linha ~378)
const mapForms: Record<string, React.ComponentType<unknown>> = {
  // Brasil
  credit_card: CreditCardForm,
  boleto: BoletoForm,
  pix: PixForm,
  pix_auto: PixAutoForm,
  picpay: PicPayForm,
  openfinance_nubank: NubankPayForm,
  
  // México
  oxxo: OxxoForm,
  spei: SpeiForm,
  
  // Global
  applepay: ApplePayForm,
  googlepay: GooglePayForm,
};
```

---

### PASSO 4: Criar Telas de Waiting Payment

#### 4.1 OxxoPayment

**Arquivo:** `/apps/web/src/components/payments/waiting/OxxoPayment.tsx`

```typescript
"use client";

import { useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import { formatPrice } from "@/lib/utils/format";
import useSettings from "@/hooks/useSettings";
import { 
  ClipboardDocumentIcon, 
  CheckCircleIcon 
} from "@heroicons/react/24/outline";

interface OxxoPaymentProps {
  voucher: {
    code: string;
    barcode: string;
    expirationDate: string;
    amount: number;
  };
}

export function OxxoPayment({ voucher }: OxxoPaymentProps) {
  const { t } = useTranslation();
  const settings = useSettings();
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(voucher.code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleDownload = () => {
    // Gerar PDF ou abrir em nova aba
    window.open(`/api/voucher/oxxo/${voucher.code}`, "_blank");
  };

  return (
    <div 
      className="max-w-md mx-auto rounded-lg shadow-lg p-6"
      style={{ backgroundColor: settings.form.background.color }}
    >
      {/* Header com emoji */}
      <div className="text-center mb-6">
        <div 
          className="w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center"
          style={{ backgroundColor: settings.box.selected.background.color }}
        >
          <span className="text-4xl">🏪</span>
        </div>
        <h2 
          className="text-2xl font-bold mb-2"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.oxxo.voucher_generated")}
        </h2>
        <p style={{ color: settings.text.color.secondary }}>
          {t("payment.oxxo.payment_instructions")}
        </p>
      </div>

      {/* Valor destacado */}
      <div 
        className="rounded-lg p-4 mb-6"
        style={{ backgroundColor: settings.box.default.background.color }}
      >
        <p 
          className="text-sm mb-1"
          style={{ color: settings.text.color.secondary }}
        >
          {t("payment.oxxo.amount_to_pay")}
        </p>
        <p 
          className="text-3xl font-bold"
          style={{ color: settings.text.color.primary }}
        >
          ${voucher.amount.toFixed(2)} MXN
        </p>
      </div>

      {/* Código de Barras */}
      <div className="mb-6">
        <label 
          className="block text-sm font-medium mb-2"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.oxxo.barcode_label")}
        </label>
        <div className="flex items-center gap-2">
          <input
            type="text"
            value={voucher.code}
            readOnly
            className="flex-1 px-4 py-3 border rounded-lg font-mono text-center text-lg"
            style={{
              backgroundColor: settings.box.unselected.background.color,
              color: settings.text.color.primary,
              borderColor: settings.box.default.background.color,
            }}
          />
          <button
            onClick={handleCopy}
            className="px-4 py-3 rounded-lg transition-colors"
            style={{
              backgroundColor: settings.payButton.color,
              color: settings.payButton.text.color,
            }}
          >
            {copied ? (
              <CheckCircleIcon className="w-5 h-5" />
            ) : (
              <ClipboardDocumentIcon className="w-5 h-5" />
            )}
          </button>
        </div>
        {copied && (
          <p 
            className="text-sm mt-2"
            style={{ color: settings.text.color.active }}
          >
            ✓ {t("payment.oxxo.code_copied")}
          </p>
        )}
      </div>

      {/* Código de Barras Visual */}
      {voucher.barcode && (
        <div className="mb-6 p-4 bg-white rounded-lg">
          <img 
            src={voucher.barcode} 
            alt="Código de barras OXXO"
            className="w-full h-24 object-contain"
          />
        </div>
      )}

      {/* Data de Vencimento */}
      <div 
        className="rounded-lg p-4 mb-6 border"
        style={{
          backgroundColor: "#FFF3CD",
          borderColor: "#FFE69C",
        }}
      >
        <p className="text-sm text-yellow-800 flex items-center gap-2">
          <span className="text-base">⏰</span>
          <span>
            {t("payment.oxxo.expires_in")}: <strong>{voucher.expirationDate}</strong>
          </span>
        </p>
      </div>

      {/* Instruções passo a passo */}
      <div className="space-y-4 mb-6">
        <h3 
          className="font-semibold"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.oxxo.how_to_pay")}
        </h3>
        <ol className="space-y-3">
          {[1, 2, 3, 4].map((step) => (
            <li key={step} className="flex items-start gap-3">
              <span 
                className="flex-shrink-0 w-7 h-7 rounded-full flex items-center justify-center text-sm font-bold"
                style={{
                  backgroundColor: settings.box.selected.header.background.color,
                  color: settings.box.selected.header.text.color.primary,
                }}
              >
                {step}
              </span>
              <span 
                className="text-sm pt-1"
                style={{ color: settings.text.color.secondary }}
              >
                {t(`payment.oxxo.instruction_${step}`)}
              </span>
            </li>
          ))}
        </ol>
      </div>

      {/* Botão de Download */}
      <button 
        onClick={handleDownload}
        className="w-full px-6 py-3 rounded-lg font-medium transition-colors"
        style={{
          backgroundColor: settings.payButton.color,
          color: settings.payButton.text.color,
        }}
      >
        📄 {t("payment.oxxo.download_voucher")}
      </button>
    </div>
  );
}
```

#### 2.2 SpeiForm

**Arquivo:** `/apps/web/src/components/payments/SpeiForm.tsx`

```typescript
"use client";

import { useTranslation } from "@/hooks/useTranslation";
import useSettings from "@/hooks/useSettings";
import { CheckCircleIcon, BanknotesIcon } from "@heroicons/react/24/outline";

export function SpeiForm() {
  const { t } = useTranslation();
  const settings = useSettings();

  const advantages = [1, 2, 3];

  return (
    <div className="space-y-4">
      {/* Info Box */}
      <div 
        className="p-4 rounded-lg flex items-start gap-3"
        style={{ backgroundColor: settings.box.default.background.color }}
      >
        <BanknotesIcon 
          className="w-6 h-6 flex-shrink-0"
          style={{ color: settings.text.color.active }}
        />
        <div>
          <h3 
            className="font-semibold mb-1 text-base"
            style={{ color: settings.text.color.primary }}
          >
            {t("payment.spei.title")}
          </h3>
          <p 
            className="text-sm"
            style={{ color: settings.text.color.secondary }}
          >
            {t("payment.spei.description")}
          </p>
        </div>
      </div>

      {/* Vantagens */}
      <div className="space-y-3">
        <p 
          className="text-sm font-semibold"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.spei.advantages")}
        </p>
        <ul className="space-y-2">
          {advantages.map((num) => (
            <li key={num} className="flex items-start gap-2">
              <CheckCircleIcon 
                className="w-5 h-5 flex-shrink-0 mt-0.5"
                style={{ color: settings.text.color.active }}
              />
              <span 
                className="text-sm"
                style={{ color: settings.text.color.secondary }}
              >
                {t(`payment.spei.advantage_${num}`)}
              </span>
            </li>
          ))}
        </ul>
      </div>

      {/* Confirmação instantânea */}
      <div 
        className="p-4 rounded-lg flex items-center gap-3"
        style={{ 
          backgroundColor: settings.box.selected.background.color,
        }}
      >
        <span className="text-2xl">⚡</span>
        <p 
          className="text-sm font-medium"
          style={{ color: settings.text.color.active }}
        >
          {t("payment.spei.instant_confirmation")}
        </p>
      </div>
    </div>
  );
}
```

---

### PASSO 5: Criar Telas de Waiting Payment

#### 5.1 OxxoPayment (detalhado acima)

#### 5.2 SpeiPayment

**Arquivo:** `/apps/web/src/components/payments/waiting/SpeiPayment.tsx`

```typescript
"use client";

import { useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import useSettings from "@/hooks/useSettings";
import { ClipboardDocumentIcon, CheckCircleIcon } from "@heroicons/react/24/outline";

interface SpeiPaymentProps {
  transfer: {
    clabe: string;
    bank: string;
    reference: string;
    amount: number;
    beneficiary: string;
  };
}

export function SpeiPayment({ transfer }: SpeiPaymentProps) {
  const { t } = useTranslation();
  const settings = useSettings();
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const handleCopy = async (text: string, field: string) => {
    await navigator.clipboard.writeText(text);
    setCopiedField(field);
    setTimeout(() => setCopiedField(null), 2000);
  };

  const CopyButton = ({ text, field }: { text: string; field: string }) => (
    <button
      onClick={() => handleCopy(text, field)}
      className="px-3 py-2 rounded-lg transition-colors"
      style={{
        backgroundColor: copiedField === field 
          ? settings.text.color.active 
          : settings.payButton.color,
        color: settings.payButton.text.color,
      }}
    >
      {copiedField === field ? (
        <CheckCircleIcon className="w-5 h-5" />
      ) : (
        <ClipboardDocumentIcon className="w-5 h-5" />
      )}
    </button>
  );

  return (
    <div 
      className="max-w-md mx-auto rounded-lg shadow-lg p-6"
      style={{ backgroundColor: settings.form.background.color }}
    >
      {/* Header */}
      <div className="text-center mb-6">
        <div 
          className="w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center"
          style={{ backgroundColor: settings.box.selected.background.color }}
        >
          <span className="text-4xl">🏦</span>
        </div>
        <h2 
          className="text-2xl font-bold mb-2"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.spei.transfer_details")}
        </h2>
        <p style={{ color: settings.text.color.secondary }}>
          {t("payment.spei.transfer_instructions")}
        </p>
      </div>

      {/* Valor */}
      <div 
        className="rounded-lg p-4 mb-6"
        style={{ backgroundColor: settings.box.default.background.color }}
      >
        <p 
          className="text-sm mb-1"
          style={{ color: settings.text.color.secondary }}
        >
          {t("payment.spei.amount_to_transfer")}
        </p>
        <p 
          className="text-3xl font-bold"
          style={{ color: settings.text.color.primary }}
        >
          ${transfer.amount.toFixed(2)} MXN
        </p>
      </div>

      {/* Dados Bancários */}
      <div className="space-y-4 mb-6">
        {/* CLABE */}
        <div>
          <label 
            className="block text-sm font-medium mb-2"
            style={{ color: settings.text.color.primary }}
          >
            {t("payment.spei.clabe")}
          </label>
          <div className="flex items-center gap-2">
            <input
              type="text"
              value={transfer.clabe}
              readOnly
              className="flex-1 px-4 py-3 border rounded-lg font-mono"
              style={{
                backgroundColor: settings.box.unselected.background.color,
                color: settings.text.color.primary,
                borderColor: settings.box.default.background.color,
              }}
            />
            <CopyButton text={transfer.clabe} field="clabe" />
          </div>
          {copiedField === "clabe" && (
            <p 
              className="text-sm mt-2"
              style={{ color: settings.text.color.active }}
            >
              ✓ {t("common.copied")}
            </p>
          )}
        </div>

        {/* Banco */}
        <div>
          <label 
            className="block text-sm font-medium mb-2"
            style={{ color: settings.text.color.primary }}
          >
            {t("payment.spei.bank")}
          </label>
          <input
            type="text"
            value={transfer.bank}
            readOnly
            className="w-full px-4 py-3 border rounded-lg"
            style={{
              backgroundColor: settings.box.unselected.background.color,
              color: settings.text.color.primary,
              borderColor: settings.box.default.background.color,
            }}
          />
        </div>

        {/* Beneficiário */}
        <div>
          <label 
            className="block text-sm font-medium mb-2"
            style={{ color: settings.text.color.primary }}
          >
            {t("payment.spei.beneficiary")}
          </label>
          <input
            type="text"
            value={transfer.beneficiary}
            readOnly
            className="w-full px-4 py-3 border rounded-lg"
            style={{
              backgroundColor: settings.box.unselected.background.color,
              color: settings.text.color.primary,
              borderColor: settings.box.default.background.color,
            }}
          />
        </div>

        {/* Referência */}
        <div>
          <label 
            className="block text-sm font-medium mb-2"
            style={{ color: settings.text.color.primary }}
          >
            {t("payment.spei.reference")}
          </label>
          <div className="flex items-center gap-2">
            <input
              type="text"
              value={transfer.reference}
              readOnly
              className="flex-1 px-4 py-3 border rounded-lg font-mono"
              style={{
                backgroundColor: settings.box.unselected.background.color,
                color: settings.text.color.primary,
                borderColor: settings.box.default.background.color,
              }}
            />
            <CopyButton text={transfer.reference} field="reference" />
          </div>
          {copiedField === "reference" && (
            <p 
              className="text-sm mt-2"
              style={{ color: settings.text.color.active }}
            >
              ✓ {t("common.copied")}
            </p>
          )}
        </div>
      </div>

      {/* Aviso Instantâneo */}
      <div 
        className="rounded-lg p-4 mb-6 border"
        style={{
          backgroundColor: "#D1FAE5",
          borderColor: "#6EE7B7",
        }}
      >
        <p className="text-sm text-green-800 flex items-center gap-2">
          <span className="text-base">⚡</span>
          <span>{t("payment.spei.instant_confirmation_notice")}</span>
        </p>
      </div>

      {/* Instruções */}
      <div className="space-y-4">
        <h3 
          className="font-semibold"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.spei.how_to_transfer")}
        </h3>
        <ol className="space-y-3">
          {[1, 2, 3, 4].map((step) => (
            <li key={step} className="flex items-start gap-3">
              <span 
                className="flex-shrink-0 w-7 h-7 rounded-full flex items-center justify-center text-sm font-bold"
                style={{
                  backgroundColor: settings.box.selected.header.background.color,
                  color: settings.box.selected.header.text.color.primary,
                }}
              >
                {step}
              </span>
              <span 
                className="text-sm pt-1"
                style={{ color: settings.text.color.secondary }}
              >
                {t(`payment.spei.transfer_instruction_${step}`)}
              </span>
            </li>
          ))}
        </ol>
      </div>
    </div>
  );
}
```

---

### PASSO 6: Atualizar WaitingPayment

**Arquivo:** `/apps/web/src/components/payments/WaitingPayment.tsx`

```typescript
// Importar novos componentes
import { OxxoPayment } from "./waiting/OxxoPayment";
import { SpeiPayment } from "./waiting/SpeiPayment";

export default function WaitingPayment() {
  const { firstPayment } = useCheckout();
  
  const paymentMethod = firstPayment?.paymentMethod;
  
  // OXXO (México)
  if (paymentMethod === "oxxo" && firstPayment?.oxxo) {
    return <OxxoPayment voucher={firstPayment.oxxo} />;
  }
  
  // SPEI (México)
  if (paymentMethod === "spei" && firstPayment?.spei) {
    return <SpeiPayment transfer={firstPayment.spei} />;
  }
  
  // PIX (Brasil) - JÁ EXISTE
  if (paymentMethod === "pix" && firstPayment?.pix) {
    return <PixPayment qrCode={firstPayment.pix} />;
  }
  
  // Boleto (Brasil) - JÁ EXISTE
  if (paymentMethod === "boleto" && firstPayment?.boleto) {
    return <BoletoPayment boleto={firstPayment.boleto} />;
  }
  
  // ... resto dos métodos
}
```

---

### PASSO 7: Atualizar Types

**Arquivo:** `/apps/web/src/types/index.ts`

```typescript
export type PaymentMethod =
  // Brasil
  | "pix"
  | "pix_auto"
  | "credit_card"
  | "boleto"
  | "picpay"
  | "openfinance_nubank"
  
  // México
  | "oxxo"
  | "spei"
  
  // Global
  | "googlepay"
  | "applepay"
  | "threeDs";

export type Payment = {
  id: string;
  status: PaymentStatus;
  paymentMethod: PaymentMethod;
  // ... existente
  
  // México
  oxxo?: {
    code: string;
    barcode: string;
    expirationDate: string;
  };
  spei?: {
    clabe: string;
    bank: string;
    beneficiary: string;
    reference: string;
  };
};
```

---

### PASSO 8: Adicionar Traduções

**Arquivos:** 
- `/apps/web/src/lib/i18n/messages/es.json` (expandir)
- `/apps/web/src/lib/i18n/messages/pt.json` (adicionar)
- `/apps/web/src/lib/i18n/messages/en.json` (adicionar)

#### 8.1 Espanhol (es.json)

```json
{
  "payment": {
    "oxxo": {
      "title": "Pago en efectivo con OXXO",
      "description": "Paga en cualquier tienda OXXO de México. Es rápido, fácil y seguro.",
      "how_it_works": "¿Cómo funciona?",
      "step_1": "Completa tu compra y genera el código de barras",
      "step_2": "Acude a la tienda OXXO más cercana con el código",
      "step_3": "Indica al cajero que deseas hacer un pago de servicio OXXO Pay",
      "step_4": "Realiza el pago en efectivo y conserva tu ticket",
      "processing_time": "El procesamiento puede tardar hasta 24 horas después del pago",
      "voucher_generated": "Comprobante OXXO generado",
      "payment_instructions": "Acude a cualquier tienda OXXO para completar tu pago",
      "amount_to_pay": "Monto a pagar",
      "barcode_label": "Código de barras",
      "code_copied": "Código copiado al portapapeles",
      "expires_in": "Vence el",
      "how_to_pay": "¿Cómo pagar en OXXO?",
      "instruction_1": "Acude a la tienda OXXO más cercana",
      "instruction_2": "Indica que quieres hacer un pago de servicio OXXO Pay",
      "instruction_3": "Muestra el código de barras al cajero (impreso o desde tu celular)",
      "instruction_4": "Realiza el pago en efectivo y guarda tu comprobante",
      "download_voucher": "Descargar comprobante OXXO"
    },
    "spei": {
      "title": "Transferencia bancaria SPEI",
      "description": "Transferencia electrónica instantánea entre bancos mexicanos. Disponible 24/7.",
      "advantages": "Ventajas del SPEI",
      "advantage_1": "Transferencia instantánea (menos de 1 minuto)",
      "advantage_2": "Disponible 24 horas, 7 días a la semana",
      "advantage_3": "Confirmación automática e inmediata del pago",
      "instant_confirmation": "Confirmación instantánea - Tu pedido se aprueba automáticamente",
      "transfer_details": "Datos para transferencia SPEI",
      "transfer_instructions": "Usa los siguientes datos para realizar la transferencia desde tu banco",
      "amount_to_transfer": "Monto a transferir",
      "clabe": "CLABE Interbancaria",
      "bank": "Banco receptor",
      "beneficiary": "Beneficiario",
      "reference": "Referencia de pago",
      "instant_confirmation_notice": "La transferencia SPEI es instantánea. Tu pedido será confirmado automáticamente al recibir el pago.",
      "how_to_transfer": "¿Cómo realizar la transferencia?",
      "transfer_instruction_1": "Accede a tu banca en línea o app de tu banco",
      "transfer_instruction_2": "Selecciona la opción 'Transferencia SPEI' o 'Transferencia a otra cuenta'",
      "transfer_instruction_3": "Ingresa la CLABE interbancaria y la referencia proporcionadas",
      "transfer_instruction_4": "Confirma la transferencia por el monto exacto mostrado arriba"
    }
  },
  "common": {
    "copied": "Copiado"
  }
}
```

#### 8.2 Português (pt.json) - Para brasileiros que mudam para MX

```json
{
  "payment": {
    "oxxo": {
      "title": "Pagamento em dinheiro com OXXO",
      "description": "Pague em qualquer loja OXXO do México. É rápido, fácil e seguro.",
      // ... traduzir todos os textos
    },
    "spei": {
      "title": "Transferência bancária SPEI",
      "description": "Transferência eletrônica instantânea entre bancos mexicanos.",
      // ... traduzir todos os textos
    }
  }
}
```

#### 8.3 Inglês (en.json)

```json
{
  "payment": {
    "oxxo": {
      "title": "Cash payment with OXXO",
      "description": "Pay at any OXXO store in Mexico. It's fast, easy and secure.",
      // ... translate all texts
    },
    "spei": {
      "title": "SPEI bank transfer",
      "description": "Instant electronic transfer between Mexican banks.",
      // ... translate all texts
    }
  }
}
```

---

## 🔄 Atualização da API (Backend)

### Opção 1: Query Parameter (SIMPLES)

```python
# No endpoint existente
@router.get("/api/product/checkout/{offer_id}/")
async def get_checkout(
    offer_id: str,
    country: str = Query(default=None),
    checkoutUrl: str = Query(default=None),
):
    # Se país não fornecido, tentar detectar via headers
    if not country:
        country = request.headers.get("CF-IPCountry", "BR")
    
    offer = await get_offer(offer_id)
    
    # Filtrar métodos disponíveis para o país
    available_methods = get_payment_methods_for_country(country)
    
    # Intersecção entre métodos do produto e métodos do país
    payment_methods = [
        method for method in offer.payment_methods
        if method["type"] in available_methods
    ]
    
    return {
        "detectedCountry": country,
        "product": {
            "paymentMethods": payment_methods,
            # ... resto
        }
    }
```

### Opção 2: Header-Based (AUTOMÁTICO)

```python
from fastapi import Request

@router.get("/api/product/checkout/{offer_id}/")
async def get_checkout(
    request: Request,
    offer_id: str,
):
    # Detectar país via headers (Cloudflare, Vercel, etc)
    country = (
        request.headers.get("CF-IPCountry") or
        request.headers.get("X-Vercel-IP-Country") or
        "BR"
    )
    
    # ... resto igual opção 1
```

---

## 🧪 Testes Necessários

### 1. Teste de Detecção de País

```typescript
// Test 1: Via locale
// URL: /es/mcrd948 → Detecta MX
// URL: /pt/mcrd948 → Detecta BR

// Test 2: Via mudança manual
// User seleciona México no CountrySelector
// Métodos de pagamento atualizam para OXXO + SPEI

// Test 3: Via VPN
// User acessa com VPN do México
// Sistema detecta e mostra métodos mexicanos
```

### 2. Teste de Fluxo OXXO

```
1. User seleciona OXXO
2. Vê instruções do OxxoForm
3. Clica "Pagar"
4. Vê OxxoPayment com:
   - Código de barras
   - Valor em MXN
   - Data de vencimento
   - Instruções
5. Pode copiar código
6. Pode baixar voucher
```

### 3. Teste de Fluxo SPEI

```
1. User seleciona SPEI
2. Vê vantagens do SpeiForm
3. Clica "Pagar"
4. Vê SpeiPayment com:
   - CLABE
   - Banco
   - Referência
   - Instruções
5. Pode copiar todos os dados
6. Recebe confirmação instantânea após transferir
```

---

## 📋 Checklist de Deploy

### Pre-Deploy
- [ ] Backend retorna métodos corretos para MX
- [ ] Todos os componentes criados e testados
- [ ] Traduções completas em 3 idiomas
- [ ] Types atualizados
- [ ] Sem erros de lint
- [ ] Sem erros de TypeScript

### Testing
- [ ] Testar com VPN do México
- [ ] Testar mudança manual de país
- [ ] Testar fluxo OXXO completo
- [ ] Testar fluxo SPEI completo
- [ ] Testar em mobile
- [ ] Testar em desktop
- [ ] Testar nas 3 línguas

### Deploy
- [ ] Deploy do backend com filtros de país
- [ ] Deploy do frontend com novos componentes
- [ ] Monitorar erros (Sentry/LogRocket)
- [ ] Monitorar conversão por método
- [ ] Documentar para time

---

## 🎯 Resumo Executivo

### Situação Atual
- ✅ **Infraestrutura:** 100% pronta (CountryProvider, types, configs)
- 🚧 **Componentes:** 0% (nenhum método do México implementado)
- ✅ **Backend:** Precisa atualizar para filtrar por país

### O que Precisa Fazer
1. **Backend:** Adicionar filtro de métodos por país (2-3h)
2. **Frontend - Ícones:** Criar OxxoIcon e SpeiIcon (30min)
3. **Frontend - Forms:** Criar OxxoForm e SpeiForm (2h)
4. **Frontend - Waiting:** Criar OxxoPayment e SpeiPayment (3h)
5. **i18n:** Adicionar traduções (1h)
6. **Integração:** Mapear no CheckoutProvider (30min)
7. **Testes:** Validar tudo (2h)

**Total: 11-12 horas**

### Resultado Esperado
- ✅ Usuários do México veem OXXO + SPEI + Cartão
- ✅ Usuários do Brasil veem PIX + Boleto + Cartão
- ✅ Sistema 100% multi-país funcional
- ✅ Base sólida para adicionar outros países (AR, CL, CO, PE)

---

**Próximo passo:** Confirmar com backend que API pode filtrar métodos por país, depois começar implementação dos componentes.

