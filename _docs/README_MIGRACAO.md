# Guia de Migração - Checkout V2

## 📚 Documentos Disponíveis

Este repositório contém os seguintes documentos para guiar a migração do checkout:

### 1. **PLANO_MIGRACAO_V2.md**
Plano completo e detalhado da migração, incluindo:
- Visão geral do projeto
- Análise do projeto atual
- Arquitetura da versão 2
- Tarefas detalhadas por fase
- Considerações técnicas
- Dependências necessárias

**Use este documento para**: Entender o escopo completo da migração e planejar o trabalho.

### 2. **PROMPT_AGENTE_IA.md**
Prompt completo e detalhado para ser usado com um agente de IA (Claude Sonnet ou similar), incluindo:
- Contexto completo do projeto
- Objetivos e requisitos
- Estrutura detalhada esperada
- Instruções específicas de implementação
- Exemplos de código
- Prioridades

**Use este documento para**: Fornecer ao agente de IA todas as informações necessárias para realizar a migração.

### 3. **CHECKLIST_TAREFAS.md**
Checklist completo de todas as tarefas organizadas por fase, incluindo:
- Fase 1: Configuração Inicial
- Fase 2: Migração de Componentes Core
- Fase 3: Serviços e Integrações
- Fase 4: Otimizações e Compatibilidade
- Fase 5: Testes e Validação
- Fase 6: Deploy e Monitoramento

**Use este documento para**: Acompanhar o progresso da migração e garantir que nada seja esquecido.

## 🚀 Como Usar Estes Documentos

### Para Desenvolvedores

1. **Leia o PLANO_MIGRACAO_V2.md** para entender o escopo completo
2. **Use o CHECKLIST_TAREFAS.md** para acompanhar o progresso
3. **Siga as tarefas em ordem** (Fase 1 → Fase 6)
4. **Marque as tarefas concluídas** conforme avança

### Para Agentes de IA

1. **Leia o PROMPT_AGENTE_IA.md** completamente
2. **Use o PLANO_MIGRACAO_V2.md** como referência técnica
3. **Siga o CHECKLIST_TAREFAS.md** para garantir completude
4. **Trabalhe de forma incremental** (uma fase por vez)
5. **Teste cada funcionalidade** conforme migra

## 📋 Resumo das Fases

### Fase 1: Configuração Inicial e Infraestrutura
- Setup do Next.js 16
- Sistema de internacionalização
- Detecção de IP e geolocalização

**Tempo estimado**: 2-3 dias

### Fase 2: Migração de Componentes Core
- Layout e rotas
- Contextos
- Formulários
- Componentes de pagamento

**Tempo estimado**: 5-7 dias

### Fase 3: Serviços e Integrações
- Serviços de API
- Integrações de terceiros
- Sistema de builder

**Tempo estimado**: 5-7 dias

### Fase 4: Otimizações e Compatibilidade
- Compatibilidade com navegadores de redes sociais
- Performance e SEO
- Tratamento de erros

**Tempo estimado**: 3-5 dias

### Fase 5: Testes e Validação
- Testes funcionais
- Testes de compatibilidade
- Testes de internacionalização

**Tempo estimado**: 5-7 dias

### Fase 6: Deploy e Monitoramento
- Configuração de deploy
- Monitoramento
- Documentação

**Tempo estimado**: 2-3 dias

**Tempo total estimado**: 22-32 dias (4-6 semanas)

## 🎯 Objetivos Principais

1. ✅ **Server-Side Rendering (SSR)**: Renderizar conteúdo crítico no servidor
2. ✅ **Multi-idioma**: Suporte para Português, Espanhol e Inglês baseado em IP
3. ✅ **Compatibilidade**: Resolver problemas com navegadores de redes sociais
4. ✅ **Mesmas APIs**: Reutilizar todas as APIs existentes
5. ✅ **Expansão**: Preparar para operação em toda a América Latina

## 🔑 Pontos Importantes

### Não Modificar APIs Existentes
- Apenas consumir as APIs existentes
- Manter compatibilidade total
- Não alterar contratos de API

### Priorizar SSR
- Renderizar conteúdo crítico no servidor
- Manter interatividade no cliente
- Usar Server Actions para mutations

### Testar Extensivamente
- Testar em navegadores de redes sociais
- Testar todos os métodos de pagamento
- Testar internacionalização

### Manter Performance
- Otimizar bundle size
- Implementar lazy loading
- Usar ISR quando possível

## 📞 Suporte

Se tiver dúvidas durante a migração:

1. Consulte os documentos disponíveis
2. Verifique o checklist de tarefas
3. Revise as considerações técnicas no plano
4. Consulte a documentação do Next.js

## 📝 Notas

- Este é um projeto de migração complexo
- Trabalhe de forma incremental
- Teste cada funcionalidade conforme migra
- Documente todas as decisões técnicas
- Mantenha o código limpo e documentado

---

**Boa sorte com a migração! 🚀**

