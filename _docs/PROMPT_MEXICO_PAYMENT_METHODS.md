# Implementação: Métodos de Pagamento do México

**Baseado em:** EBANX Mexico Payment Methods + API Response  
**Objetivo:** Adicionar suporte completo aos métodos de pagamento do México  
**Status:** 🚧 Planejamento

---

## 🎯 Contexto

A Cakto está expandindo para o México e precisa suportar os métodos de pagamento locais. A API já retorna os métodos disponíveis por país no campo `product.paymentMethods`, e o frontend já tem infraestrutura multi-país (CountryProvider).

### Métodos de Pagamento do México (EBANX)

Segundo a [EBANX](https://www.ebanx.com/en/latin-america/mexico/), o México suporta:

1. **🏪 OXXO** - Cash payment (voucher)
2. **💳 Cartões de Crédito** - Visa, Mastercard, American Express
3. **🏦 SPEI** - Transferência bancária (similar ao PIX)
4. **💰 Cartões de Débito** - Local debit cards

### Arquitetura Atual

```
API Response (paymentMethods)
    ↓
CountryProvider (país selecionado)
    ↓
PaymentTabs (filtra métodos disponíveis)
    ↓
PaymentForms (renderiza formulário específico)
```

---

## 📋 Análise da API Response

### Estrutura Atual

```json
{
  "product": {
    "paymentMethods": [
      {"type": "applepay", "name": "Apple Pay"},
      {"type": "boleto", "name": "Boleto"},
      {"type": "credit_card", "name": "Cartão de Crédito"},
      {"type": "googlepay", "name": "Google Pay"},
      {"type": "pix", "name": "PIX"}
    ],
    "paymentsOrder": ["pix", "boleto", "credit_card", "picpay"]
  }
}
```

### O que precisamos adicionar

Para o México, a API deve retornar:

```json
{
  "product": {
    "paymentMethods": [
      {"type": "oxxo", "name": "OXXO"},
      {"type": "spei", "name": "SPEI"},
      {"type": "credit_card", "name": "Tarjeta de Crédito"},
      {"type": "debit_card", "name": "Tarjeta de Débito"}
    ],
    "paymentsOrder": ["oxxo", "spei", "credit_card", "debit_card"]
  }
}
```

---

## 🏗️ Arquitetura da Solução

### 1. API Layer (Backend - FastAPI)

**Responsabilidade:** Retornar métodos de pagamento com base no país

```python
# Pseudo-código
def get_payment_methods_by_country(country_code: str, offer_id: str):
    if country_code == "MX":
        return [
            {"type": "oxxo", "name": "OXXO", "enabled": True},
            {"type": "spei", "name": "SPEI", "enabled": True},
            {"type": "credit_card", "name": "Tarjeta de Crédito", "enabled": True},
        ]
    elif country_code == "BR":
        return [
            {"type": "pix", "name": "PIX", "enabled": True},
            {"type": "boleto", "name": "Boleto", "enabled": True},
            {"type": "credit_card", "name": "Cartão de Crédito", "enabled": True},
        ]
    # etc...
```

**Endpoint atualizado:**

```
GET /api/product/checkout/{offer_id}/?country={country_code}
```

### 2. Frontend Layer (Next.js)

**Responsabilidade:** Detectar país, buscar dados e orquestrar componentes

```typescript
// apps/web/src/app/[locale]/[id]/page.tsx
export default async function CheckoutPage({ params }) {
  const { locale, id } = await params;
  
  // Detectar país do usuário (IP, locale, ou seleção manual)
  const country = await detectUserCountry();
  
  // Buscar dados do checkout COM país
  const checkoutData = await getCheckoutData(id, country);
  
  return (
    <CheckoutClient 
      initialData={checkoutData} 
      checkoutId={id}
      country={country}
    />
  );
}
```

### 3. Component Layer

**Responsabilidade:** Renderizar formulários específicos por método

```
PaymentMethods (orquestrador)
    ↓
├─ OxxoForm (MX)
├─ SpeiForm (MX)
├─ DebitCardForm (MX)
├─ PixForm (BR)
├─ BoletoForm (BR)
└─ CreditCardForm (Global)
```

---

## 📦 FASE 1: Componentes de Pagamento do México

### 1.1 OxxoForm (Cash Payment)

**Arquivo:** `/apps/web/src/components/payments/OxxoForm.tsx`

```typescript
"use client";

import { useTranslation } from "@/hooks/useTranslation";
import useSettings from "@/hooks/useSettings";

export function OxxoForm() {
  const { t } = useTranslation();
  const settings = useSettings();

  return (
    <div className="space-y-4">
      {/* Informações sobre OXXO */}
      <div 
        className="p-4 rounded-lg"
        style={{ backgroundColor: settings.box.default.background.color }}
      >
        <h3 
          className="font-semibold mb-2"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.oxxo.title")}
        </h3>
        <p 
          className="text-sm"
          style={{ color: settings.text.color.secondary }}
        >
          {t("payment.oxxo.description")}
        </p>
      </div>

      {/* Instruções */}
      <div className="space-y-2">
        <p 
          className="text-sm font-medium"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.oxxo.how_it_works")}
        </p>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li style={{ color: settings.text.color.secondary }}>
            {t("payment.oxxo.step_1")}
          </li>
          <li style={{ color: settings.text.color.secondary }}>
            {t("payment.oxxo.step_2")}
          </li>
          <li style={{ color: settings.text.color.secondary }}>
            {t("payment.oxxo.step_3")}
          </li>
        </ol>
      </div>

      {/* Aviso de tempo de processamento */}
      <div 
        className="p-3 rounded border"
        style={{ 
          backgroundColor: settings.box.selected.background.color,
          borderColor: settings.box.selected.header.background.color 
        }}
      >
        <p 
          className="text-xs"
          style={{ color: settings.text.color.secondary }}
        >
          ⚠️ {t("payment.oxxo.processing_time")}
        </p>
      </div>
    </div>
  );
}
```

### 1.2 SpeiForm (Bank Transfer)

**Arquivo:** `/apps/web/src/components/payments/SpeiForm.tsx`

```typescript
"use client";

import { useTranslation } from "@/hooks/useTranslation";
import useSettings from "@/hooks/useSettings";

export function SpeiForm() {
  const { t } = useTranslation();
  const settings = useSettings();

  return (
    <div className="space-y-4">
      {/* Informações sobre SPEI */}
      <div 
        className="p-4 rounded-lg"
        style={{ backgroundColor: settings.box.default.background.color }}
      >
        <h3 
          className="font-semibold mb-2"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.spei.title")}
        </h3>
        <p 
          className="text-sm"
          style={{ color: settings.text.color.secondary }}
        >
          {t("payment.spei.description")}
        </p>
      </div>

      {/* Vantagens */}
      <div className="space-y-2">
        <p 
          className="text-sm font-medium"
          style={{ color: settings.text.color.primary }}
        >
          {t("payment.spei.advantages")}
        </p>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li style={{ color: settings.text.color.secondary }}>
            {t("payment.spei.advantage_1")}
          </li>
          <li style={{ color: settings.text.color.secondary }}>
            {t("payment.spei.advantage_2")}
          </li>
          <li style={{ color: settings.text.color.secondary }}>
            {t("payment.spei.advantage_3")}
          </li>
        </ul>
      </div>

      {/* Aviso */}
      <div 
        className="p-3 rounded border"
        style={{ 
          backgroundColor: settings.box.selected.background.color,
          borderColor: settings.box.selected.header.background.color 
        }}
      >
        <p 
          className="text-xs"
          style={{ color: settings.text.color.secondary }}
        >
          ℹ️ {t("payment.spei.instant_confirmation")}
        </p>
      </div>
    </div>
  );
}
```

### 1.3 DebitCardForm

**Arquivo:** `/apps/web/src/components/payments/DebitCardForm.tsx`

```typescript
"use client";

import TextField from "@/components/ui/text-field";
import { useTranslation } from "@/hooks/useTranslation";
import { CreditCardIcon, CalendarIcon, LockClosedIcon } from "@heroicons/react/24/outline";

export function DebitCardForm() {
  const { t } = useTranslation();

  return (
    <div className="space-y-4">
      {/* Número do Cartão */}
      <TextField
        name="cardNumber"
        label={t("payment.debit_card.card_number")}
        placeholder="0000 0000 0000 0000"
        icon={<CreditCardIcon className="w-5 h-5" />}
        iconPosition="left"
        mask="9999 9999 9999 9999"
      />

      {/* Nome no Cartão */}
      <TextField
        name="cardHolderName"
        label={t("payment.debit_card.cardholder_name")}
        placeholder={t("payment.debit_card.cardholder_placeholder")}
      />

      {/* Validade e CVV */}
      <div className="grid grid-cols-2 gap-4">
        <TextField
          name="cardExpiry"
          label={t("payment.debit_card.expiration_date")}
          placeholder="MM/AA"
          icon={<CalendarIcon className="w-5 h-5" />}
          iconPosition="left"
          mask="99/99"
        />
        <TextField
          name="cardCvv"
          label={t("payment.debit_card.cvv")}
          placeholder="000"
          icon={<LockClosedIcon className="w-5 h-5" />}
          iconPosition="left"
          mask="999"
        />
      </div>

      {/* Aviso de Segurança */}
      <p className="text-xs text-gray-600">
        🔒 {t("payment.debit_card.security_notice")}
      </p>
    </div>
  );
}
```

---

## 📦 FASE 2: Ícones dos Métodos de Pagamento

### 2.1 Criar Ícones

**Arquivo:** `/apps/web/src/components/icons/payment/OxxoIcon.tsx`

```typescript
export const OxxoIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    {/* SVG do logo OXXO */}
    <path d="..." />
  </svg>
);
```

**Arquivo:** `/apps/web/src/components/icons/payment/SpeiIcon.tsx`

```typescript
export const SpeiIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    {/* SVG do logo SPEI */}
    <path d="..." />
  </svg>
);
```

### 2.2 Atualizar Mapeamento de Ícones

**Arquivo:** `/apps/web/src/contexts/checkout-context.tsx`

```typescript
import { OxxoIcon, SpeiIcon, DebitCardIcon } from "@/components/icons/payment";

// No CheckoutProvider
const mapIcons: Record<string, React.ComponentType<{ className?: string }>> = {
  // Brasil
  credit_card: CreditCardIcon,
  boleto: BarcodeIcon,
  pix: PixIcon,
  pix_auto: PixAutoIcon,
  picpay: PicPayIcon,
  
  // México
  oxxo: OxxoIcon,
  spei: SpeiIcon,
  debit_card: DebitCardIcon,
  
  // Global
  applepay: ApplePayIcon,
  googlepay: GooglePayIcon,
  openfinance_nubank: NubankIcon,
};
```

### 2.3 Atualizar Mapeamento de Formulários

**Arquivo:** `/apps/web/src/contexts/checkout-context.tsx`

```typescript
import { OxxoForm, SpeiForm, DebitCardForm } from "@/components/payments";

const mapForms: Record<string, React.ComponentType<unknown>> = {
  // Brasil
  credit_card: CreditCardForm,
  boleto: BoletoForm,
  pix: PixForm,
  pix_auto: PixAutoForm,
  picpay: PicPayForm,
  
  // México
  oxxo: OxxoForm,
  spei: SpeiForm,
  debit_card: DebitCardForm,
  
  // Global
  applepay: ApplePayForm,
  googlepay: GooglePayForm,
  openfinance_nubank: NubankPayForm,
};
```

---

## 📦 FASE 3: Telas de Pagamento Pendente (Waiting Payment)

### 3.1 OxxoPayment

**Arquivo:** `/apps/web/src/components/payments/waiting/OxxoPayment.tsx`

```typescript
"use client";

import { useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import { formatPrice } from "@/lib/utils/format";
import { ClipboardDocumentIcon } from "@heroicons/react/24/outline";

interface OxxoPaymentProps {
  voucher: {
    code: string;
    barcode: string;
    expirationDate: string;
    amount: number;
  };
}

export function OxxoPayment({ voucher }: OxxoPaymentProps) {
  const { t } = useTranslation();
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(voucher.code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="text-center mb-6">
        <div className="w-20 h-20 bg-yellow-100 rounded-full mx-auto mb-4 flex items-center justify-center">
          <span className="text-4xl">🏪</span>
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {t("payment.oxxo.voucher_generated")}
        </h2>
        <p className="text-gray-600">
          {t("payment.oxxo.payment_instructions")}
        </p>
      </div>

      {/* Valor */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <p className="text-sm text-gray-600 mb-1">
          {t("payment.oxxo.amount_to_pay")}
        </p>
        <p className="text-3xl font-bold text-gray-900">
          {formatPrice(voucher.amount)}
        </p>
      </div>

      {/* Código de Barras */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {t("payment.oxxo.barcode_label")}
        </label>
        <div className="flex items-center gap-2">
          <input
            type="text"
            value={voucher.code}
            readOnly
            className="flex-1 px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg font-mono text-center"
          />
          <button
            onClick={handleCopy}
            className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ClipboardDocumentIcon className="w-5 h-5" />
          </button>
        </div>
        {copied && (
          <p className="text-sm text-green-600 mt-2">
            ✓ {t("payment.oxxo.code_copied")}
          </p>
        )}
      </div>

      {/* Código de Barras Visual */}
      <div className="mb-6">
        <img 
          src={voucher.barcode} 
          alt="Código de barras"
          className="w-full h-24 object-contain"
        />
      </div>

      {/* Data de Vencimento */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <p className="text-sm text-yellow-800">
          ⏰ {t("payment.oxxo.expires_in")}: {voucher.expirationDate}
        </p>
      </div>

      {/* Instruções */}
      <div className="space-y-4">
        <h3 className="font-semibold text-gray-900">
          {t("payment.oxxo.how_to_pay")}
        </h3>
        <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
          <li>{t("payment.oxxo.instruction_1")}</li>
          <li>{t("payment.oxxo.instruction_2")}</li>
          <li>{t("payment.oxxo.instruction_3")}</li>
          <li>{t("payment.oxxo.instruction_4")}</li>
        </ol>
      </div>

      {/* Botão de Download */}
      <button className="w-full mt-6 px-6 py-3 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors">
        📄 {t("payment.oxxo.download_voucher")}
      </button>
    </div>
  );
}
```

### 3.2 SpeiPayment

**Arquivo:** `/apps/web/src/components/payments/waiting/SpeiPayment.tsx`

```typescript
"use client";

import { useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import { formatPrice } from "@/lib/utils/format";
import { ClipboardDocumentIcon } from "@heroicons/react/24/outline";

interface SpeiPaymentProps {
  transfer: {
    clabe: string;
    bank: string;
    reference: string;
    amount: number;
    expirationDate: string;
  };
}

export function SpeiPayment({ transfer }: SpeiPaymentProps) {
  const { t } = useTranslation();
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const handleCopy = async (text: string, field: string) => {
    await navigator.clipboard.writeText(text);
    setCopiedField(field);
    setTimeout(() => setCopiedField(null), 2000);
  };

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="text-center mb-6">
        <div className="w-20 h-20 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
          <span className="text-4xl">🏦</span>
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {t("payment.spei.transfer_details")}
        </h2>
        <p className="text-gray-600">
          {t("payment.spei.transfer_instructions")}
        </p>
      </div>

      {/* Valor */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <p className="text-sm text-gray-600 mb-1">
          {t("payment.spei.amount_to_transfer")}
        </p>
        <p className="text-3xl font-bold text-gray-900">
          {formatPrice(transfer.amount)} MXN
        </p>
      </div>

      {/* Dados Bancários */}
      <div className="space-y-4 mb-6">
        {/* CLABE */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t("payment.spei.clabe")}
          </label>
          <div className="flex items-center gap-2">
            <input
              type="text"
              value={transfer.clabe}
              readOnly
              className="flex-1 px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg font-mono"
            />
            <button
              onClick={() => handleCopy(transfer.clabe, "clabe")}
              className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ClipboardDocumentIcon className="w-5 h-5" />
            </button>
          </div>
          {copiedField === "clabe" && (
            <p className="text-sm text-green-600 mt-2">
              ✓ {t("common.copied")}
            </p>
          )}
        </div>

        {/* Banco */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t("payment.spei.bank")}
          </label>
          <input
            type="text"
            value={transfer.bank}
            readOnly
            className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg"
          />
        </div>

        {/* Referência */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t("payment.spei.reference")}
          </label>
          <div className="flex items-center gap-2">
            <input
              type="text"
              value={transfer.reference}
              readOnly
              className="flex-1 px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg font-mono"
            />
            <button
              onClick={() => handleCopy(transfer.reference, "reference")}
              className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ClipboardDocumentIcon className="w-5 h-5" />
            </button>
          </div>
          {copiedField === "reference" && (
            <p className="text-sm text-green-600 mt-2">
              ✓ {t("common.copied")}
            </p>
          )}
        </div>
      </div>

      {/* Aviso */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <p className="text-sm text-blue-800">
          ℹ️ {t("payment.spei.instant_confirmation_notice")}
        </p>
      </div>

      {/* Instruções */}
      <div className="space-y-4">
        <h3 className="font-semibold text-gray-900">
          {t("payment.spei.how_to_transfer")}
        </h3>
        <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
          <li>{t("payment.spei.transfer_instruction_1")}</li>
          <li>{t("payment.spei.transfer_instruction_2")}</li>
          <li>{t("payment.spei.transfer_instruction_3")}</li>
          <li>{t("payment.spei.transfer_instruction_4")}</li>
        </ol>
      </div>
    </div>
  );
}
```

---

## 📦 FASE 4: Traduções i18n

### 4.1 Espanhol (México)

**Arquivo:** `/apps/web/src/lib/i18n/messages/es-MX.json` (CRIAR NOVO)

```json
{
  "payment": {
    "oxxo": {
      "title": "Pago en efectivo con OXXO",
      "description": "Paga en cualquier tienda OXXO de México",
      "how_it_works": "¿Cómo funciona?",
      "step_1": "Completa tu compra y genera el código de barras",
      "step_2": "Dirígete a la tienda OXXO más cercana",
      "step_3": "Muestra el código de barras al cajero",
      "step_4": "Realiza el pago en efectivo",
      "processing_time": "El procesamiento puede tardar hasta 24 horas después del pago",
      "voucher_generated": "Comprobante generado",
      "payment_instructions": "Completa tu pago en cualquier tienda OXXO",
      "amount_to_pay": "Monto a pagar",
      "barcode_label": "Código de barras",
      "code_copied": "Código copiado",
      "expires_in": "Vence en",
      "how_to_pay": "¿Cómo pagar?",
      "instruction_1": "Acude a la tienda OXXO más cercana",
      "instruction_2": "Indica al cajero que quieres realizar un pago de servicio OXXO Pay",
      "instruction_3": "Entrega el código de barras impreso o muéstralo desde tu celular",
      "instruction_4": "Realiza el pago en efectivo",
      "download_voucher": "Descargar comprobante"
    },
    "spei": {
      "title": "Transferencia bancaria SPEI",
      "description": "Transferencia electrónica instantánea entre bancos mexicanos",
      "advantages": "Ventajas",
      "advantage_1": "Transferencia instantánea",
      "advantage_2": "Disponible 24/7",
      "advantage_3": "Confirmación inmediata",
      "instant_confirmation": "La confirmación es instantánea después de realizar la transferencia",
      "transfer_details": "Datos de transferencia",
      "transfer_instructions": "Realiza una transferencia SPEI con los siguientes datos",
      "amount_to_transfer": "Monto a transferir",
      "clabe": "CLABE Interbancaria",
      "bank": "Banco",
      "reference": "Referencia",
      "instant_confirmation_notice": "La transferencia SPEI es instantánea y tu pedido se confirmará automáticamente",
      "how_to_transfer": "¿Cómo transferir?",
      "transfer_instruction_1": "Accede a tu banca en línea o app bancaria",
      "transfer_instruction_2": "Selecciona 'Transferencia SPEI'",
      "transfer_instruction_3": "Ingresa la CLABE y referencia proporcionadas",
      "transfer_instruction_4": "Confirma la transferencia por el monto exacto"
    },
    "debit_card": {
      "card_number": "Número de tarjeta",
      "cardholder_name": "Nombre en la tarjeta",
      "cardholder_placeholder": "Nombre como aparece en la tarjeta",
      "expiration_date": "Fecha de vencimiento",
      "cvv": "CVV",
      "security_notice": "Tus datos de pago son encriptados y procesados de forma segura"
    }
  },
  "country": {
    "names": {
      "MX": "México"
    },
    "documents": {
      "RFC": "RFC",
      "CURP": "CURP"
    }
  },
  "common": {
    "copied": "Copiado"
  }
}
```

---

## 📦 FASE 5: Atualizar CountryProvider

### 5.1 Adicionar Configuração do México

**Arquivo:** `/apps/web/src/contexts/country-context.tsx`

```typescript
export const COUNTRIES = {
  BR: {
    code: "BR",
    name: "Brasil",
    currency: "BRL",
    currencySymbol: "R$",
    locale: "pt-BR",
    documentLabel: "CPF/CNPJ",
    documentMask: "999.999.999-99",
    phoneFormat: "(99) 99999-9999",
    paymentMethods: ["pix", "pix_auto", "boleto", "credit_card", "picpay", "applepay", "googlepay", "openfinance_nubank"],
  },
  MX: {
    code: "MX",
    name: "México",
    currency: "MXN",
    currencySymbol: "$",
    locale: "es-MX",
    documentLabel: "RFC",
    documentMask: "AAAA999999XXX",
    phoneFormat: "+52 99 9999 9999",
    paymentMethods: ["oxxo", "spei", "credit_card", "debit_card", "applepay", "googlepay"],
  },
  // Outros países...
} as const;
```

---

## 📦 FASE 6: Atualizar API Client

### 6.1 Passar País no Request

**Arquivo:** `/apps/web/src/lib/api/checkout.ts`

```typescript
export async function getCheckoutData(
  checkoutId: string, 
  affiliate?: string,
  country?: string // Novo parâmetro
) {
  const params = new URLSearchParams();
  
  if (affiliate) {
    params.append("affiliate", affiliate);
  }
  
  if (country) {
    params.append("country", country); // Adicionar país
  }
  
  const url = `${API_BASE_URL}/api/product/checkout/${checkoutId}/?${params.toString()}`;
  
  const response = await fetch(url);
  
  if (!response.ok) {
    throw new Error("Failed to fetch checkout data");
  }
  
  return response.json();
}
```

### 6.2 Detectar País no Server Side

**Arquivo:** `/apps/web/src/lib/utils/detect-country.ts` (CRIAR NOVO)

```typescript
import { headers } from "next/headers";

export async function detectUserCountry(): Promise<string> {
  const headersList = await headers();
  
  // 1. Tentar pelo header CF-IPCountry (Cloudflare)
  const cfCountry = headersList.get("cf-ipcountry");
  if (cfCountry && cfCountry !== "XX") {
    return cfCountry;
  }
  
  // 2. Tentar pelo header X-Vercel-IP-Country (Vercel)
  const vercelCountry = headersList.get("x-vercel-ip-country");
  if (vercelCountry) {
    return vercelCountry;
  }
  
  // 3. Fallback: Brasil (padrão)
  return "BR";
}
```

### 6.3 Usar no Page

**Arquivo:** `/apps/web/src/app/[locale]/[id]/page.tsx`

```typescript
import { detectUserCountry } from "@/lib/utils/detect-country";
import { getCheckoutData } from "@/lib/api/checkout";

export default async function CheckoutPage({ params, searchParams }: CheckoutPageProps) {
  const { locale, id } = await params;
  const { affiliate } = await searchParams;
  
  // Detectar país
  const country = await detectUserCountry();
  
  // Buscar dados COM país
  const checkoutData = await getCheckoutData(id, affiliate, country);
  
  return (
    <CheckoutClient 
      initialData={checkoutData} 
      checkoutId={id}
    />
  );
}
```

---

## 📦 FASE 7: Atualizar WaitingPayment

### 7.1 Adicionar Casos para México

**Arquivo:** `/apps/web/src/components/payments/WaitingPayment.tsx`

```typescript
import { OxxoPayment } from "./waiting/OxxoPayment";
import { SpeiPayment } from "./waiting/SpeiPayment";

export default function WaitingPayment() {
  const { firstPayment } = useCheckout();
  
  const paymentMethod = firstPayment?.paymentMethod;
  
  // OXXO
  if (paymentMethod === "oxxo" && firstPayment?.oxxo) {
    return <OxxoPayment voucher={firstPayment.oxxo} />;
  }
  
  // SPEI
  if (paymentMethod === "spei" && firstPayment?.spei) {
    return <SpeiPayment transfer={firstPayment.spei} />;
  }
  
  // PIX
  if (paymentMethod === "pix" && firstPayment?.pix) {
    return <PixPayment qrCode={firstPayment.pix} />;
  }
  
  // Boleto
  if (paymentMethod === "boleto" && firstPayment?.boleto) {
    return <BoletoPayment boleto={firstPayment.boleto} />;
  }
  
  // ... outros métodos
}
```

---

## 📦 FASE 8: Atualizar Types

### 8.1 Adicionar Tipos do México

**Arquivo:** `/apps/web/src/types/index.ts`

```typescript
export type PaymentMethod =
  // Brasil
  | "pix"
  | "pix_auto"
  | "credit_card"
  | "boleto"
  | "picpay"
  | "openfinance_nubank"
  
  // México
  | "oxxo"
  | "spei"
  | "debit_card"
  
  // Global
  | "googlepay"
  | "applepay"
  | "threeDs";

export type Payment = {
  // ... existente
  
  // México
  oxxo?: {
    code: string;
    barcode: string;
    expirationDate: string;
  };
  spei?: {
    clabe: string;
    bank: string;
    reference: string;
  };
};
```

---

## 🎯 Checklist de Implementação

### Backend (FastAPI)

- [ ] Endpoint retorna métodos por país
- [ ] Suporte a OXXO payment
- [ ] Suporte a SPEI payment
- [ ] Webhook para OXXO payment confirmation
- [ ] Webhook para SPEI payment confirmation
- [ ] Integração com gateway de pagamento (EBANX/Stripe)

### Frontend (Next.js)

#### Componentes
- [ ] OxxoForm criado
- [ ] SpeiForm criado
- [ ] DebitCardForm criado
- [ ] OxxoPayment (waiting) criado
- [ ] SpeiPayment (waiting) criado

#### Ícones
- [ ] OxxoIcon criado
- [ ] SpeiIcon criado
- [ ] DebitCardIcon atualizado
- [ ] Mapeamento de ícones atualizado

#### Contextos e Hooks
- [ ] CountryProvider atualizado com MX
- [ ] CheckoutProvider mapeia novos métodos
- [ ] Payment types atualizados

#### i18n
- [ ] Traduções es-MX.json criadas
- [ ] Todas as chaves de OXXO traduzidas
- [ ] Todas as chaves de SPEI traduzidas
- [ ] Todas as chaves de debit_card traduzidas

#### API Integration
- [ ] detectUserCountry implementado
- [ ] getCheckoutData aceita país
- [ ] Page passa país para API
- [ ] Tratamento de erros por país

#### Testes
- [ ] Testar fluxo OXXO completo
- [ ] Testar fluxo SPEI completo
- [ ] Testar detecção de país
- [ ] Testar com VPN do México
- [ ] Testar mudança manual de país
- [ ] Testar traduções es-MX
- [ ] Testar responsive em todos os métodos

---

## 🚀 Ordem de Implementação Recomendada

### Sprint 1: Infraestrutura (3-5 dias)
1. Atualizar types
2. Atualizar CountryProvider com México
3. Criar detectUserCountry
4. Atualizar getCheckoutData
5. Adicionar traduções es-MX

### Sprint 2: Componentes (5-7 dias)
1. Criar ícones (Oxxo, SPEI)
2. Criar OxxoForm
3. Criar SpeiForm  
4. Criar DebitCardForm
5. Atualizar mapeamentos no CheckoutProvider

### Sprint 3: Waiting Payments (3-5 dias)
1. Criar OxxoPayment
2. Criar SpeiPayment
3. Atualizar WaitingPayment
4. Testar fluxos completos

### Sprint 4: Testes e Polish (2-3 dias)
1. Testes end-to-end
2. Testes com VPN
3. Ajustes de UX
4. Documentação

**Total: 13-20 dias**

---

## 📊 Arquitetura de Orquestração

```
┌──────────────────────────────────────────────────────┐
│  1. User Access Checkout                             │
│     └─> detectUserCountry() → "MX"                  │
└──────────────────────────────────────────────────────┘
                        ↓
┌──────────────────────────────────────────────────────┐
│  2. Server-Side Data Fetch                           │
│     └─> getCheckoutData(id, affiliate, "MX")       │
│         API: /api/product/checkout/{id}?country=MX   │
└──────────────────────────────────────────────────────┘
                        ↓
┌──────────────────────────────────────────────────────┐
│  3. API Response                                     │
│     paymentMethods: [                                │
│       {type: "oxxo", name: "OXXO"},                 │
│       {type: "spei", name: "SPEI"},                 │
│       {type: "credit_card", name: "Tarjeta"}        │
│     ]                                                │
└──────────────────────────────────────────────────────┘
                        ↓
┌──────────────────────────────────────────────────────┐
│  4. CountryProvider                                  │
│     └─> Sets: MX config (currency, documents, etc)  │
└──────────────────────────────────────────────────────┘
                        ↓
┌──────────────────────────────────────────────────────┐
│  5. CheckoutProvider (paymentTabs)                   │
│     └─> Filters methods by country                   │
│     └─> Maps icons: oxxo → OxxoIcon                 │
│     └─> Maps forms: oxxo → OxxoForm                 │
└──────────────────────────────────────────────────────┘
                        ↓
┌──────────────────────────────────────────────────────┐
│  6. PaymentMethods Component                         │
│     └─> Renders tabs: OXXO | SPEI | Tarjeta        │
└──────────────────────────────────────────────────────┘
                        ↓
┌──────────────────────────────────────────────────────┐
│  7. User Selects Payment Method                      │
│     └─> Renders: OxxoForm                           │
└──────────────────────────────────────────────────────┘
                        ↓
┌──────────────────────────────────────────────────────┐
│  8. User Submits Payment                             │
│     └─> startPaymentAPI(data, "oxxo")              │
└──────────────────────────────────────────────────────┘
                        ↓
┌──────────────────────────────────────────────────────┐
│  9. Payment Processing                               │
│     └─> Returns: {paymentMethod: "oxxo", oxxo: {...}}│
└──────────────────────────────────────────────────────┘
                        ↓
┌──────────────────────────────────────────────────────┐
│  10. WaitingPayment Component                        │
│      └─> Detects "oxxo" → Renders OxxoPayment      │
│          Shows voucher with barcode                  │
└──────────────────────────────────────────────────────┘
```

---

## 🔒 Considerações de Segurança

1. **Validação de País**: Validar país no backend (não confiar apenas no frontend)
2. **Currency Mismatch**: Prevenir pagamentos em moeda errada
3. **Webhook Security**: Verificar assinatura dos webhooks de pagamento
4. **Rate Limiting**: Limitar tentativas de pagamento por IP/usuário
5. **Fraud Detection**: Integrar com sistema de detecção de fraudes

---

## 📚 Referências

- [EBANX Mexico Payment Methods](https://www.ebanx.com/en/latin-america/mexico/)
- [OXXO Pay Documentation](https://developers.ebanx.com/guides/oxxo)
- [SPEI Documentation](https://developers.ebanx.com/guides/spei)
- [Next.js i18n](https://nextjs.org/docs/app/building-your-application/routing/internationalization)
- [Geo-location Headers](https://vercel.com/docs/edge-network/headers)

---

**Documento criado em:** 2025-01-15  
**Versão:** 1.0  
**Status:** 📋 Planejamento

