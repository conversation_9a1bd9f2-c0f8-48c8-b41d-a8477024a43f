# Análise de Gaps e Melhorias - Cakto Checkout v2

**Data:** 10 de Novembro de 2025  
**Projeto:** Next.js 16 + React 19  
**Objetivo:** Migração completa + i18n + otimizações SSR/SSG

---

## 📊 Status Atual vs Projeto Original

### ✅ J<PERSON> (Fase 1-5)

#### Infraestrutura
- ✅ Next.js 16 com App Router
- ✅ React 19 + React Compiler
- ✅ TailwindCSS 4.1
- ✅ TypeScript 5.8+
- ✅ Turbopack
- ✅ i18n básico (pt, es, en)
- ✅ Middleware de locale
- ✅ IntlProvider + Context

#### Componentes Core
- ✅ CheckoutProvider + useCheckout()
- ✅ CheckoutClient + CheckoutForm
- ✅ 8 Formulários de pagamento
- ✅ 4 Componentes de QR/Boleto
- ✅ 3 Componentes de status
- ✅ 6 Componentes auxiliares
- ✅ 11 Componentes do builder
- ✅ Componentes UI base (button, card, text-field, etc)

#### Hooks e Serviços
- ✅ useSettings
- ✅ usePrice
- ✅ useServiceFee
- ✅ useCepSearch
- ✅ Pixel hooks (Facebook, Google, TikTok, Kwai)
- ✅ APIs client-side e server-side

---

## ❌ Componentes e Funcionalidades FALTANDO

### 1. Componentes Importantes NÃO Migrados

#### Endereço e Entrega
- ❌ **AddressForm** - Formulário de endereço com busca de CEP
  - Localização: `_docs/cakto-checkout/src/components/checkout/AddressForm.tsx`
  - Funcionalidades:
    - Busca automática de CEP
    - Estados brasileiros
    - Validação de endereço
    - Suporte a Nommi (opção de frete)
    - Feedback visual (loading, sucesso, erro)

#### Order Bumps
- ❌ **BumpItem** - Item de bump offer (upsell)
  - Localização: `_docs/cakto-checkout/src/components/checkout/Bumps/BumpItem.tsx`
  - Funcionalidades:
    - Checkbox de seleção
    - Imagem do produto
    - Cálculo de desconto
    - Seletor de parcelas (se aplicável)
    - Animações de transição
    - Integração com cupom

#### Informações e Modais
- ❌ **AboutCpf** - Modal explicativo sobre CPF/CNPJ
  - Localização: `_docs/cakto-checkout/src/components/info/AboutCpf.tsx`
  - Funcionalidades:
    - Modal com createPortal
    - Link button customizado
    - Styled com settings

- ❌ **AboutPixSecurityAlert** - Alerta de segurança PIX
  - Localização: `_docs/cakto-checkout/src/components/info/AboutPixSecurityAlert.tsx`

- ❌ **CheckoutTitles** - Títulos de seção do checkout
  - Localização: `_docs/cakto-checkout/src/components/checkout/CheckoutTitles.tsx`

#### Componentes de Contato
- ❌ **ProductComponent** - Exibição do produto
- ❌ **ProductPurchaseComponent** - Detalhes da compra

#### Componentes Comuns
- ❌ **EmailAutoComplete** - Autocomplete de email
- ❌ **TextFieldPhoneNumber** - Campo de telefone com máscara
- ❌ **ErrorModal** - Modal de erro
- ❌ **ErrorTopAlert** - Alerta de erro no topo
- ❌ **Toast** - Notificações toast
- ❌ **LinkButton** - Botão tipo link
- ❌ **ServiceFeeTooltip** - Tooltip de taxa de serviço
- ❌ **Modal** - Modal genérico

#### Chats
- ❌ **Crisp** - Integração Crisp
- ❌ **Facebook** - Facebook Messenger
- ❌ **FreshChat** - FreshChat
- ❌ **Intercom** - Intercom
- ❌ **JivoChat** - JivoChat
- ❌ **ManyChat** - ManyChat
- ❌ **Tawk** - Tawk.to
- ❌ **Whatsapp** - WhatsApp widget
- ❌ **Zendesk** - Zendesk Chat
- ❌ **CustomChatCard** - Card customizado de chat

---

### 2. Hooks NÃO Migrados

- ❌ **useNethone** - Antifraude Nethone
- ❌ **useMercadoPagoDeviceId** - Device ID do Mercado Pago
- ❌ **usePageLeave** - Exit intent
- ❌ **useThrowError** - Error handling
- ❌ **useDebounce** - Debounce
- ❌ **useCouponDiscount** - Cálculo de desconto de cupom
- ❌ **useMessage** - Hook de i18n do projeto original

---

### 3. Serviços e Utils NÃO Migrados

#### Serviços
- ❌ **3DS Service** (Cielo/Pagarme) - Autenticação 3D Secure
- ❌ **Error Interceptor** - Interceptor de erros Axios
- ❌ **Error Handler** - Sistema de tratamento de erros
- ❌ **Abandonment tracking** - Tracking de abandono

#### Utils
- ❌ **errorHandler.ts** - Sistema completo de tratamento de erros
- ❌ **errorMessages.ts** - Mensagens de erro i18n (pt, en, es)
- ❌ **cookies.ts** - Helpers de cookies
- ❌ **validation helpers** - Yup schemas

---

### 4. Sistema de i18n INCOMPLETO

#### Projeto Original (Completo)
```typescript
// _docs/cakto-checkout/src/i18n/errorMessages.ts
- Mensagens em pt-BR, en-US, es-ES
- Categorias: validation, network, payment, threeds, system, generic
- ErrorMessageService com métodos específicos
- Suporte a interpolação de variáveis
```

#### Projeto Next.js (Básico)
```json
// apps/web/src/lib/i18n/messages/*.json
- Apenas 3 categorias: checkout, common, errors
- Total de ~5 mensagens por idioma
- SEM mensagens de formulário
- SEM mensagens de pagamento
- SEM mensagens de erro detalhadas
```

#### **Gap Crítico:**
- ❌ Faltam **200+ mensagens** de tradução
- ❌ Componentes ainda têm texto hardcoded em português
- ❌ Validações sem i18n
- ❌ Erros sem i18n
- ❌ Labels de formulário sem i18n

---

## 🚨 Problemas Identificados

### 1. SSR/SSG Não Aproveitado

#### ❌ Dados Buscados no Cliente
Atualmente, a página de checkout busca os dados no servidor (✅), mas:
- Componentes ainda fazem chamadas desnecessárias no cliente
- CheckoutProvider recebe `initialData` mas não está totalmente otimizado
- Validação de cupom ainda é 100% client-side

#### ❌ Oportunidades Perdidas
- Pré-renderizar componentes do builder no servidor
- Cache de dados de checkout
- ISR (Incremental Static Regeneration) para checkouts frequentes
- Server Components para partes estáticas

### 2. Navegadores de Redes Sociais

#### ❌ Meta Tags Incompletas
```tsx
// apps/web/src/app/[locale]/[id]/page.tsx
// FALTAM:
- Open Graph tags completas
- Twitter Card tags
- Favicon dinâmico por produto
- Title e description dinâmicos
```

#### ❌ User-Agent Detection
- Não há detecção específica de in-app browsers
- Falta tratamento especial para TikTok, Instagram, Facebook browsers
- Possíveis problemas com:
  - Apple Pay no in-app browser
  - Google Pay no in-app browser
  - Redirecionamentos de pagamento

### 3. Header de Locale INEXISTENTE

#### ❌ Não há componente de seleção de idioma
- Header atual (`apps/web/src/components/header.tsx`) é genérico
- Não aparece na página de checkout
- Não tem seletor de idioma/país

#### ✅ O que PRECISA ser criado:
- Componente de seletor de locale (dropdown ou flags)
- Persistência em cookie ao trocar
- Redirecionamento correto (/pt/id → /en/id)
- Exibição no checkout (discreta, no canto superior)

### 4. Performance e Bundle Size

#### ⚠️ Dependências Pesadas
```json
{
  "react-qrcode-logo": "Grande bundle",
  "react-confetti": "Grande bundle",
  "react-player": "Grande bundle",
  "posthog-js": "Grande bundle"
}
```

#### ❌ Não há Code Splitting
- Todos os formulários de pagamento carregam juntos
- QR Code library carrega mesmo sem usar PIX
- Confetti carrega antes da compra ser concluída

#### ✅ Soluções:
- Dynamic imports para formulários de pagamento
- Lazy load de QR Code e Confetti
- Tree-shaking otimizado

---

## 🎯 Benefícios do Next.js NÃO Aproveitados

### 1. Server Components

#### ❌ Tudo é Client Component
Atualmente 100% dos componentes têm `"use client"`.

#### ✅ Poderiam ser Server Components:
- CheckoutConfigRenderer (renderização do builder)
- Componentes estáticos do builder:
  - CheckoutComponentText
  - CheckoutComponentImage
  - CheckoutComponentHeader
  - CheckoutComponentSeal
  - CheckoutComponentAdvantage
- Seção de produtos
- Resumo do pedido (parte estática)

### 2. Streaming SSR

#### ❌ Não há uso de Suspense boundaries
```tsx
// ATUAL
<CheckoutClient initialData={data} />

// IDEAL
<Suspense fallback={<CheckoutSkeleton />}>
  <CheckoutData id={id}>
    <Suspense fallback={<ProductSkeleton />}>
      <ProductSection />
    </Suspense>
    <Suspense fallback={<FormSkeleton />}>
      <CheckoutForm />
    </Suspense>
  </CheckoutData>
</Suspense>
```

### 3. Middleware Avançado

#### ✅ Middleware atual (básico):
- Detecta locale
- Redireciona URLs sem locale
- Seta cookie

#### ❌ Poderia ter:
- A/B testing
- Feature flags
- Bot detection (bloquear bots de scraping)
- Rate limiting
- Geolocalização avançada
- Analytics no edge

### 4. Edge Runtime

#### ❌ Tudo roda no Node.js runtime
```tsx
// Nenhum componente usa Edge Runtime
```

#### ✅ Poderiam usar Edge:
- Middleware (já usa por padrão)
- API routes de validação de cupom
- Redirect de sucesso
- Tracking de eventos

### 5. Image Optimization

#### ❌ Imagens sem otimização
```tsx
// Projeto atual usa <img>
<img src={bump.image} alt="..." />
<img src={settings.backgroundImage} alt="..." />
```

#### ✅ Deveria usar:
```tsx
import Image from "next/image";
<Image src={bump.image} alt="..." width={80} height={80} />
```

### 6. Metadata API

#### ❌ Metadata estático
```tsx
// apps/web/src/app/layout.tsx
export const metadata = {
  title: "Checkout",
  description: "...",
};
```

#### ✅ Deveria ser dinâmico:
```tsx
// apps/web/src/app/[locale]/[id]/page.tsx
export async function generateMetadata({ params }) {
  const { id } = await params;
  const checkout = await getCheckoutData(id);
  
  return {
    title: checkout.product.name,
    description: checkout.product.description,
    openGraph: {
      images: [checkout.product.image],
      title: checkout.product.name,
      description: checkout.product.description,
    },
    twitter: {
      card: "summary_large_image",
      images: [checkout.product.image],
    },
  };
}
```

---

## 🎨 Compatibilidade com In-App Browsers

### Problemas Conhecidos

#### TikTok In-App Browser
- User-Agent: `TikTok`
- Problemas:
  - LocalStorage pode estar bloqueado
  - Cookies podem estar bloqueados
  - Apple Pay não funciona
  - Redirecionamentos quebram

#### Instagram In-App Browser
- User-Agent: `Instagram`
- Problemas:
  - Apple Pay não funciona
  - Google Pay não funciona
  - Popup de 3DS pode quebrar

#### Facebook In-App Browser
- User-Agent: `FB_IAB`
- Problemas:
  - Similar ao Instagram
  - Melhor suporte a cookies

### Soluções Necessárias

```typescript
// Detectar in-app browser
export function isInAppBrowser(): boolean {
  if (typeof window === 'undefined') return false;
  
  const ua = navigator.userAgent;
  return /FBAN|FBAV|Instagram|TikTok/i.test(ua);
}

// Detectar plataforma específica
export function getInAppPlatform() {
  if (typeof window === 'undefined') return null;
  
  const ua = navigator.userAgent;
  if (/TikTok/i.test(ua)) return 'tiktok';
  if (/Instagram/i.test(ua)) return 'instagram';
  if (/FBAN|FBAV/i.test(ua)) return 'facebook';
  return null;
}

// Mostrar aviso para abrir no navegador
export function shouldShowOpenInBrowserWarning(): boolean {
  const platform = getInAppPlatform();
  return platform === 'tiktok' || platform === 'instagram';
}
```

#### Implementar:
- Banner no topo: "Para melhor experiência, abra no navegador"
- Botão "Abrir no Safari/Chrome"
- Desabilitar Apple Pay/Google Pay em in-app browsers
- Fallback para métodos que funcionam (PIX, Boleto)

---

## 📋 Plano de Ação - Fase 6

### 6.1 Componentes Essenciais (Prioridade ALTA)

#### 6.1.1 AddressForm ⭐⭐⭐
- Migrar `AddressForm.tsx`
- Adaptar para i18n (labels, placeholders, erros)
- Integrar com CheckoutComponent
- Validar campos com react-hook-form
- Manter integração com `useCepSearch`

#### 6.1.2 BumpItem ⭐⭐⭐
- Migrar `BumpItem.tsx`
- Adicionar i18n (textos, labels)
- Integrar com CheckoutComponent
- Garantir funcionamento com cupons
- Animations com Tailwind

#### 6.1.3 Componentes de Info ⭐⭐
- Migrar `AboutCpf.tsx`
- Migrar `AboutPixSecurityAlert.tsx`
- Criar modals com Radix UI ou native dialog
- i18n completo

#### 6.1.4 Componentes Comuns ⭐⭐
- Migrar `EmailAutoComplete.tsx`
- Migrar `TextFieldPhoneNumber.tsx`
- Migrar `LinkButton.tsx`
- Migrar `ServiceFeeTooltip.tsx`
- Migrar `Toast.tsx`

### 6.2 Sistema de i18n Completo (Prioridade ALTA)

#### 6.2.1 Estrutura de Mensagens
```
apps/web/src/lib/i18n/messages/
├── pt.json (expandir)
├── en.json (expandir)
├── es.json (expandir)
└── types.ts (criar)
```

#### 6.2.2 Categorias Necessárias
```json
{
  "checkout": { ... },
  "common": { ... },
  "errors": { 
    "validation": { ... },
    "network": { ... },
    "payment": { ... },
    "threeds": { ... },
    "system": { ... }
  },
  "form": {
    "labels": { ... },
    "placeholders": { ... },
    "helpers": { ... }
  },
  "payment": {
    "methods": { ... },
    "messages": { ... }
  },
  "address": { ... },
  "bump": { ... }
}
```

#### 6.2.3 Hooks de i18n
```typescript
// Criar: apps/web/src/hooks/useTranslation.ts
export function useTranslation() {
  const { messages, locale } = useIntlContext();
  
  const t = useCallback((key: string, params?: Record<string, any>) => {
    // Implementar get nested key
    // Implementar interpolação
    return message;
  }, [messages]);
  
  return { t, locale };
}
```

### 6.3 Header com Seletor de Locale (Prioridade ALTA)

#### 6.3.1 Criar CheckoutHeader
```tsx
// apps/web/src/components/checkout/checkout-header.tsx
"use client";

export function CheckoutHeader() {
  return (
    <header className="sticky top-0 z-50 bg-background/95 backdrop-blur">
      <div className="container flex h-14 items-center justify-between">
        <Logo />
        <div className="flex items-center gap-4">
          <LocaleSelector />
          <TrustBadges />
        </div>
      </div>
    </header>
  );
}
```

#### 6.3.2 Criar LocaleSelector
```tsx
// apps/web/src/components/checkout/locale-selector.tsx
"use client";

import { usePathname, useRouter } from "next/navigation";
import { useLocale } from "@/contexts/intl-context";

const LOCALES = [
  { code: "pt", label: "🇧🇷 Português", country: "Brasil" },
  { code: "en", label: "🇺🇸 English", country: "United States" },
  { code: "es", label: "🇪🇸 Español", country: "España" },
];

export function LocaleSelector() {
  const locale = useLocale();
  const pathname = usePathname();
  const router = useRouter();
  
  const handleChange = (newLocale: string) => {
    // Trocar locale no pathname
    const segments = pathname.split("/");
    segments[1] = newLocale;
    const newPath = segments.join("/");
    
    // Setar cookie
    document.cookie = `NEXT_LOCALE=${newLocale}; path=/; max-age=31536000`;
    
    // Redirecionar
    router.push(newPath);
  };
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        {LOCALES.find(l => l.code === locale)?.label}
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {LOCALES.map(l => (
          <DropdownMenuItem key={l.code} onClick={() => handleChange(l.code)}>
            {l.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
```

### 6.4 Otimizações SSR/SSG (Prioridade MÉDIA)

#### 6.4.1 Server Components
- Separar componentes estáticos em Server Components
- Mover lógica de dados para o servidor
- Reduzir bundle JavaScript do cliente

#### 6.4.2 Suspense Boundaries
- Adicionar loading states granulares
- Streaming de componentes pesados
- Skeleton loaders

#### 6.4.3 Metadata Dinâmico
- Implementar `generateMetadata` por produto
- Open Graph tags
- Twitter Cards
- SEO otimizado

### 6.5 Compatibilidade In-App Browser (Prioridade MÉDIA)

#### 6.5.1 Detecção
```typescript
// apps/web/src/lib/utils/browser.ts
export function detectBrowser() {
  // Implementar detecção
}
```

#### 6.5.2 Componentes
```tsx
// apps/web/src/components/checkout/in-app-browser-warning.tsx
export function InAppBrowserWarning() {
  // Mostrar aviso se necessário
}
```

#### 6.5.3 Adaptações
- Desabilitar métodos incompatíveis
- Mostrar alternativas
- Melhorar UX

### 6.6 Performance (Prioridade BAIXA)

#### 6.6.1 Code Splitting
```tsx
// Dynamic imports
const PixPayment = dynamic(() => import("@/components/payments/PixPayment"));
const Confetti = dynamic(() => import("react-confetti"));
```

#### 6.6.2 Bundle Analysis
```bash
pnpm --filter web run build --analyze
```

#### 6.6.3 Image Optimization
- Substituir `<img>` por `<Image>`
- Configurar domains permitidos
- Lazy loading

---

## 📊 Métricas de Sucesso

### Antes (Vite)
- ⏱️ Time to Interactive: ~3.5s
- 📦 Bundle size: ~800KB
- 🌍 Sem i18n completo
- 🚫 Problemas em in-app browsers
- ❌ Sem SSR/SSG

### Objetivo (Next.js)
- ⏱️ Time to Interactive: <2s
- 📦 Bundle size inicial: <300KB
- 🌍 i18n completo (pt, es, en)
- ✅ Suporte a in-app browsers
- ✅ SSR/SSG otimizado
- ✅ Lighthouse score: 90+

---

## 🔧 Stack Tecnológico Final

### Core
- ✅ Next.js 16.0.0
- ✅ React 19.2.0
- ✅ TypeScript 5.8+
- ✅ TailwindCSS 4.1
- ✅ Turbopack

### UI
- ✅ Radix UI (para componentes acessíveis)
- ✅ shadcn/ui (base)
- ✅ tailwind-variants
- ✅ Heroicons

### Forms
- ✅ react-hook-form 7.46
- ⏳ Zod (adicionar para validação)

### i18n
- ✅ next-intl (usar ao invés de Context custom)
- ✅ Mensagens JSON
- ✅ Server + Client

### State
- ✅ React Context (checkout)
- ✅ React Query (data fetching)
- ✅ Zustand (se necessário para estado global)

### Payment
- ✅ react-qrcode-logo
- ✅ credit-card-type
- ✅ payment-token-efi
- ⏳ @stripe/stripe-js (se integrar Stripe)

### Analytics
- ✅ posthog-js
- ✅ react-ga4
- ✅ facebook-pixel
- ✅ tiktok-pixel

### Utils
- ✅ moment (ou date-fns)
- ✅ uuid
- ✅ classnames
- ✅ cpf-cnpj-validator

---

## 🎯 Próximos Passos Imediatos

1. ✅ **Análise completa** (este documento)
2. ⏳ **Criar prompt para agente** (próximo)
3. ⏳ **Fase 6.1: Migrar componentes essenciais**
4. ⏳ **Fase 6.2: i18n completo**
5. ⏳ **Fase 6.3: Header + LocaleSelector**
6. ⏳ **Fase 6.4: Otimizações SSR/SSG**
7. ⏳ **Fase 6.5: In-app browser support**
8. ⏳ **Fase 6.6: Performance**
9. ⏳ **Fase 7: Testes e2e**
10. ⏳ **Fase 8: Deploy e monitoramento**

---

**Última atualização:** 10/11/2025 02:00 AM  
**Autor:** Análise automatizada via Cursor AI  
**Status:** 📋 Documento de planejamento

