# Guia de Migração de Componentes: Vite → Next.js

Este documento fornece instruções detalhadas para migrar os componentes do `cakto-checkout` (Vite + React) para `cakto-checkoutv2` (Next.js 16).

## Princípios da Migração

### ✅ O QUE REUTILIZAR
- **Componentes do Builder**: Reutilizar 100% dos componentes do builder sem modificações
- **Lógica de Negócio**: Manter toda a lógica de validação, cálculos e regras de negócio
- **Hooks Customizados**: Adaptar hooks para Next.js, mas manter a lógica
- **Utilitários**: Reutilizar funções utilitárias sem modificações
- **Tipos TypeScript**: Copiar tipos e interfaces diretamente
- **Estilos**: Manter classes Tailwind e estilos CSS existentes

### ⚠️ O QUE ADAPTAR
- **Imports**: Atualizar paths de imports para estrutura Next.js
- **Client Components**: Adicionar `"use client"` onde necessário
- **Server Components**: Usar Server Components para dados iniciais
- **Contextos**: Adaptar para funcionar com SSR
- **Hooks do React Router**: Substituir por hooks do Next.js
- **Variáveis de Ambiente**: Usar `NEXT_PUBLIC_*` ou helpers de `lib/env.ts`

### ❌ O QUE NÃO REUTILIZAR
- **React Router**: Substituir por Next.js App Router
- **Vite-specific code**: Remover código específico do Vite
- **Axios**: Usar `fetch` nativo ou helpers de `lib/api/`

## Estrutura de Pastas

### Origem (cakto-checkout)
```
src/
├── components/
│   ├── checkout/          # Componentes do checkout
│   ├── payment/           # Formulários de pagamento
│   ├── builder/           # Componentes do builder (REUTILIZAR)
│   ├── common/            # Componentes comuns
│   └── pixels/            # Tracking pixels
├── contexts/             # Context providers
├── hooks/                 # Custom hooks
├── services/              # Serviços (API, 3DS, etc.)
├── utils/                 # Utilitários
└── types/                 # TypeScript types
```

### Destino (cakto-checkoutv2)
```
apps/web/src/
├── components/
│   ├── checkout/          # Componentes do checkout
│   ├── payments/          # Formulários de pagamento
│   ├── builder/           # Componentes do builder (COPIAR DIRETO)
│   ├── common/            # Componentes comuns
│   └── pixels/            # Tracking pixels
├── contexts/              # Context providers (adaptar)
├── hooks/                 # Custom hooks (adaptar)
├── lib/
│   ├── api/               # Clientes de API (já criado)
│   └── utils/              # Utilitários
└── types/                  # TypeScript types
```

## Passo a Passo da Migração

### 1. Componentes do Builder (PRIORIDADE ALTA)

**Objetivo**: Copiar componentes do builder sem modificações significativas.

#### Estrutura dos Componentes do Builder

Os componentes do builder estão em:
- `cakto-checkout/src/components/builder/`

#### Passos:

1. **Copiar todos os arquivos do builder**:
   ```bash
   cp -r cakto-checkout/src/components/builder/* cakto-checkoutv2/apps/web/src/components/builder/
   ```

2. **Adicionar `"use client"` no topo de cada componente**:
   - Todos os componentes do builder devem ser Client Components
   - Adicionar `"use client"` como primeira linha

3. **Atualizar imports**:
   ```typescript
   // ANTES (Vite)
   import { SomeUtil } from '@/utils/someUtil';
   import { SomeType } from '@/types/someType';
   
   // DEPOIS (Next.js)
   import { SomeUtil } from '@/lib/utils/someUtil';
   import { SomeType } from '@/types/someType';
   ```

4. **Verificar dependências**:
   - Se o componente usa `useNavigate`, `useLocation` → Remover (não necessário no Next.js)
   - Se usa `window.location` → Manter (funciona no cliente)
   - Se usa `import.meta.env` → Substituir por helpers de `lib/env.ts`

#### Exemplo de Migração de Componente do Builder:

```typescript
// ANTES: cakto-checkout/src/components/builder/CheckoutComponentText.tsx
import { ComponentProps } from '@/types/builder';

export function CheckoutComponentText({ attributes }: ComponentProps) {
  return <div>{attributes.text}</div>;
}

// DEPOIS: cakto-checkoutv2/apps/web/src/components/builder/CheckoutComponentText.tsx
"use client";

import { ComponentProps } from '@/types/builder';

export function CheckoutComponentText({ attributes }: ComponentProps) {
  return <div>{attributes.text}</div>;
}
```

### 2. Componentes de Pagamento

**Objetivo**: Migrar formulários de pagamento mantendo toda a lógica.

#### Componentes a Migrar:
- `CreditCardForm.tsx`
- `PixForm.tsx`
- `PixAutoForm.tsx`
- `BoletoForm.tsx`
- `ApplePayForm.tsx`
- `GooglePayForm.tsx`
- `PicPayForm.tsx`
- `NubankPayForm.tsx`

#### Passos:

1. **Copiar componentes para `components/payments/`**
2. **Adicionar `"use client"`** (todos são client components)
3. **Atualizar imports de API**:
   ```typescript
   // ANTES
   import { startPayment } from '@/services/checkout';
   
   // DEPOIS
   import { startPayment } from '@/lib/api/checkout';
   ```

4. **Adaptar uso de variáveis de ambiente**:
   ```typescript
   // ANTES
   const apiKey = import.meta.env.VITE_HOPYPAY_PUBLIC_KEY;
   
   // DEPOIS
   import { getHopyPayPublicKey } from '@/lib/env';
   const apiKey = getHopyPayPublicKey();
   ```

5. **Manter toda a lógica de validação e formulários**

### 3. Componentes do Checkout

**Objetivo**: Migrar componentes principais do checkout.

#### Componentes Principais:
- `CheckoutForm.tsx` → Adaptar para usar Server Actions
- `ProductPurchaseComponent.tsx` → Manter como Client Component
- `PaymentMethods.tsx` → Manter como Client Component

#### Passos:

1. **CheckoutForm.tsx**:
   - Manter como Client Component
   - Usar React Hook Form (já funciona no cliente)
   - Adaptar submissão para usar Server Actions quando possível
   - Manter toda validação client-side

2. **PaymentMethods.tsx**:
   - Copiar diretamente
   - Adicionar `"use client"`
   - Atualizar imports

### 4. Contextos

**Objetivo**: Adaptar contextos para funcionar com SSR.

#### Contextos a Migrar:
- `CheckoutContext.tsx`
- `CheckoutModeContext.tsx`
- `NotificationContext.tsx`

#### Passos:

1. **Criar Provider como Client Component**:
   ```typescript
   "use client";
   
   import { createContext, useContext, useState } from 'react';
   
   const CheckoutContext = createContext(null);
   
   export function CheckoutProvider({ children, initialData }) {
     // Usar initialData do SSR
     const [data, setData] = useState(initialData);
     
     return (
       <CheckoutContext.Provider value={{ data, setData }}>
         {children}
       </CheckoutContext.Provider>
     );
   }
   ```

2. **Usar no layout ou page**:
   ```typescript
   // app/[locale]/[id]/page.tsx
   import { CheckoutProvider } from '@/contexts/CheckoutContext';
   
   export default async function CheckoutPage({ params, searchParams }) {
     const checkoutData = await getCheckoutData(id);
     
     return (
       <CheckoutProvider initialData={checkoutData}>
         <CheckoutClient />
       </CheckoutProvider>
     );
   }
   ```

### 5. Hooks Customizados

**Objetivo**: Adaptar hooks mantendo a lógica.

#### Hooks a Migrar:
- Todos os hooks de `src/hooks/`

#### Passos:

1. **Copiar hooks para `hooks/`**
2. **Atualizar imports**:
   ```typescript
   // ANTES
   import { useCheckout } from '@/contexts/CheckoutContext';
   
   // DEPOIS
   import { useCheckout } from '@/contexts/CheckoutContext';
   ```

3. **Verificar uso de APIs**:
   - Se usa `axios` → Adaptar para usar `fetch` ou helpers de `lib/api/`
   - Se usa `window` → Manter (só funciona no cliente mesmo)

### 6. Serviços

**Objetivo**: Migrar serviços adaptando para Next.js.

#### Serviços a Migrar:
- `services/checkout/index.ts` → Já migrado para `lib/api/checkout.ts`
- `services/3ds/` → Adaptar para usar helpers de env
- `services/cep.ts` → Manter como está (pode ser client-side)

#### Passos:

1. **Serviços de API**:
   - Já migrados para `lib/api/`
   - Usar `fetch` nativo com `credentials: "include"`

2. **Serviços de 3DS**:
   - Adaptar para usar `get3dsProvider()`, `getCielo3dsScript()`, etc.
   - Manter toda lógica de inicialização

3. **Serviços de CEP**:
   - Copiar diretamente
   - Funciona no cliente

### 7. Utilitários

**Objetivo**: Reutilizar utilitários sem modificações.

#### Passos:

1. **Copiar todos os utilitários**:
   ```bash
   cp -r cakto-checkout/src/utils/* cakto-checkoutv2/apps/web/src/lib/utils/
   ```

2. **Atualizar imports nos componentes**:
   ```typescript
   // ANTES
   import { formatCurrency } from '@/utils/currency';
   
   // DEPOIS
   import { formatCurrency } from '@/lib/utils/currency';
   ```

### 8. Tipos TypeScript

**Objetivo**: Copiar tipos diretamente.

#### Passos:

1. **Copiar todos os tipos**:
   ```bash
   cp -r cakto-checkout/src/types/* cakto-checkoutv2/apps/web/src/types/
   ```

2. **Atualizar imports**:
   - Manter paths relativos ou usar `@/types/`

### 9. Tracking Pixels

**Objetivo**: Migrar pixels mantendo funcionalidade.

#### Passos:

1. **Copiar componentes de pixels**:
   ```bash
   cp -r cakto-checkout/src/components/pixels/* cakto-checkoutv2/apps/web/src/components/pixels/
   ```

2. **Adicionar `"use client"`** em todos
3. **Adaptar variáveis de ambiente**:
   ```typescript
   // Usar helpers de lib/env.ts
   import { getPostHogKey } from '@/lib/env';
   ```

4. **Carregar apenas no cliente**:
   ```typescript
   "use client";
   
   import { useEffect } from 'react';
   
   export function TrackingPixels() {
     useEffect(() => {
       // Inicializar pixels
     }, []);
     
     return null;
   }
   ```

## Checklist de Migração por Componente

Para cada componente migrado, verificar:

- [ ] Adicionado `"use client"` se necessário
- [ ] Imports atualizados para estrutura Next.js
- [ ] Variáveis de ambiente adaptadas para `lib/env.ts`
- [ ] Removido código específico do Vite
- [ ] Removido uso de React Router (se houver)
- [ ] Adaptado uso de APIs para `lib/api/`
- [ ] Testado funcionamento no navegador
- [ ] Verificado SSR (se aplicável)

## Ordem Recomendada de Migração

1. **Tipos TypeScript** (base para tudo)
2. **Utilitários** (usados por muitos componentes)
3. **Componentes do Builder** (mais simples, reutilizar direto)
4. **Contextos** (base para outros componentes)
5. **Hooks Customizados** (usados por componentes)
6. **Componentes de Pagamento** (lógica complexa)
7. **Componentes do Checkout** (integração final)
8. **Tracking Pixels** (último, carrega no cliente)

## Comandos Úteis

### Copiar componentes do builder:
```bash
cd /Users/<USER>/Documents/www/cakto/cakto-checkout
cp -r cakto-checkout/src/components/builder/* cakto-checkoutv2/apps/web/src/components/builder/
```

### Adicionar "use client" em todos os componentes:
```bash
cd cakto-checkoutv2/apps/web/src/components/builder
find . -name "*.tsx" -exec sed -i '' '1i\
"use client";
' {} \;
```

### Buscar uso de import.meta.env:
```bash
grep -r "import.meta.env" cakto-checkoutv2/apps/web/src/
```

### Buscar uso de React Router:
```bash
grep -r "react-router" cakto-checkoutv2/apps/web/src/
```

## Notas Importantes

1. **Não reescrever componentes do builder**: Eles devem funcionar quase idênticos, apenas com `"use client"` e imports atualizados.

2. **Manter compatibilidade**: Os componentes devem funcionar exatamente como no projeto original.

3. **SSR apenas onde necessário**: A maioria dos componentes será Client Component. Apenas páginas e layouts são Server Components.

4. **Testar incrementalmente**: Migrar um componente por vez e testar antes de continuar.

5. **Usar helpers de env**: Sempre usar `lib/env.ts` ao invés de acessar `process.env` diretamente.

## Próximos Passos

Após migrar os componentes:

1. Integrar componentes do builder no `CheckoutClient`
2. Conectar formulários de pagamento
3. Implementar fluxo completo de checkout
4. Adicionar tracking pixels
5. Testar todos os métodos de pagamento
6. Testar em navegadores de redes sociais

