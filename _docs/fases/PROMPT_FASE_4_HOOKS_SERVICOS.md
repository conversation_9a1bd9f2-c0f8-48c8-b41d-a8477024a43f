# Prompt para Fase 4: <PERSON><PERSON><PERSON> de Hooks e Serviços - Checkout V2

## 🎯 Objetivo

Migrar os hooks e serviços do checkout original (`cakto-checkout` - Vite + React) para o novo projeto (`cakto-checkoutv2` - Next.js 16), **completando a implementação dos placeholders criados na Fase 3** e migrando hooks auxiliares necessários.

## 📋 Contexto do Projeto

### Projeto Original (Fonte)
- **Localização**: `/Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2/_docs/cakto-checkout/src`
- **Stack**: Vite + React 18 + TypeScript
- **Roteamento**: React Router DOM
- **HTTP Client**: Axios
- **Estado**: React Query (tanstack-query)

### Projeto Novo (Destino)
- **Localização**: `/Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2/apps/web/src`
- **Stack**: Next.js 16 + React 19 + TypeScript
- **Roteamento**: Next.js App Router
- **HTTP Client**: fetch nativo (server-side) ou Server Actions
- **Estado**: React Query (se necessário) ou useState/useEffect

### Status Atual da Migração

#### ✅ Fase 1: Base (Tipos e Utilitários) - CONCLUÍDA
- ✅ Tipos TypeScript migrados (`types/`)
- ✅ Utilitários migrados (`lib/utils/`)
- ✅ Constantes criadas (`constants/payment.ts`)
- ✅ Helpers de ambiente adaptados (`lib/env.ts`)

#### ✅ Fase 2: Componentes do Builder - CONCLUÍDA
- ✅ Componentes do builder migrados (`components/builder/`)
- ✅ Hook `useImagePreview` migrado
- ✅ Assets copiados (`assets/`)

#### ✅ Fase 3: Contextos - CONCLUÍDA
- ✅ `CheckoutContext` migrado (com placeholders para hooks de pixels)
- ✅ `CheckoutModeContext` migrado
- ✅ `NotificationContext` migrado
- ✅ `useNotifications` hook migrado
- ⚠️ **Hooks de pixels criados como placeholders** (apenas console.log):
  - `useFacebookPixels` → precisa implementação completa
  - `useGoogleAds` → precisa implementação completa
  - `useTikTokPixels` → precisa implementação completa
  - `useKwaiPixels` → precisa implementação completa

#### 🔄 Fase 4: Hooks e Serviços - EM ANDAMENTO
- ⏳ **Hooks de Pixels** - **PRIORIDADE MÁXIMA**
  - ⏳ `useFacebookPixels` - Implementar tracking completo
  - ⏳ `useGoogleAds` - Implementar tracking completo
  - ⏳ `useTikTokPixels` - Implementar tracking completo
  - ⏳ `useKwaiPixels` - Implementar tracking completo
- ⏳ **Hooks Auxiliares** - **PRIORIDADE ALTA**
  - ⏳ `useCepSearch` - Busca de CEP
  - ⏳ `useCouponDiscount` - Cálculo de desconto de cupom
  - ⏳ `usePrice` - Formatação de preços
  - ⏳ `useServiceFee` - Cálculo de taxa de serviço
  - ⏳ `useFetchCalculatedInstallments` - Busca de parcelamento
  - ⏳ `useIsFetchingCalculatedInstallments` - Estado de loading de parcelamento
- ⏳ **Hooks de Tracking** - **PRIORIDADE MÉDIA**
  - ⏳ `useTrackingPixels` - Gerenciamento geral de pixels
  - ⏳ `useDevice` - Detecção de dispositivo
  - ⏳ `useMercadoPagoDeviceId` - Device ID do Mercado Pago
- ⏳ **Hooks de UI/UX** - **PRIORIDADE BAIXA**
  - ⏳ `useDebounce` - Debounce de valores
  - ⏳ `usePageLeave` - Detecção de saída da página
  - ⏳ `useThrowError` - Tratamento de erros
- ⏳ **Ícones Personalizados** - **PRIORIDADE ALTA**
  - ⏳ Migrar ícones de pagamento de `icons/` para `components/icons/` ou `assets/icons/`

## 🎯 Princípios da Migração

### ✅ REUTILIZAR (NÃO REESCREVER)
- **Lógica de Negócio**: Manter toda validação, cálculos e regras
- **Estrutura de Hooks**: Manter estrutura similar, apenas adaptar para Next.js
- **Tracking de Pixels**: Manter toda lógica de tracking, apenas adaptar imports

### ⚠️ ADAPTAR (MUDANÇAS NECESSÁRIAS)
- **Imports**: Atualizar paths para estrutura Next.js (`@/` ao invés de paths relativos)
- **Client Components**: Adicionar `"use client"` no topo
- **React Router**: Substituir `useParams`, `useSearchParams` por props ou `next/navigation`
- **React Query**: Substituir por `useState`/`useEffect` ou adaptar para Next.js
- **APIs**: Usar `lib/api/` ao invés de `services/`
- **Contextos**: Usar contextos migrados na Fase 3 ao invés de React Query

### ❌ REMOVER/SUBSTITUIR
- **React Router**: Remover `useParams`, `useSearchParams`, `useNavigate`, `useLocation`
- **Axios**: Substituir por `fetch` ou helpers de `lib/api/`
- **import.meta.env**: Substituir por helpers de `lib/env.ts`
- **React Query**: Substituir por contextos migrados ou `useState`/`useEffect`
- **Vite-specific code**: Remover qualquer código específico do Vite

## 📁 Estrutura de Migração

### Origem (cakto-checkout)
```
src/
├── hooks/
│   ├── useFacebookPixels.ts          # ⭐ PRIORIDADE MÁXIMA
│   ├── useGoogleAds.ts                # ⭐ PRIORIDADE MÁXIMA
│   ├── useTikTokPixels.ts             # ⭐ PRIORIDADE MÁXIMA
│   ├── useKwaiPixels.ts               # ⭐ PRIORIDADE MÁXIMA
│   ├── useCepSearch.ts                # ⭐ PRIORIDADE ALTA
│   ├── useCouponDiscount.ts           # ⭐ PRIORIDADE ALTA
│   ├── usePrice.ts                    # ⭐ PRIORIDADE ALTA
│   ├── useServiceFee.ts               # ⭐ PRIORIDADE ALTA
│   ├── useFetchCalculatedInstallments.ts  # ⭐ PRIORIDADE ALTA
│   ├── useIsFetchingCalculatedInstallments.ts  # ⭐ PRIORIDADE ALTA
│   ├── useTrackingPixels.ts           # ⭐ PRIORIDADE MÉDIA
│   ├── useDevice.ts                   # ⭐ PRIORIDADE MÉDIA
│   ├── useMercadoPagoDeviceId.ts      # ⭐ PRIORIDADE MÉDIA
│   ├── useDebounce.ts                 # ⭐ PRIORIDADE BAIXA
│   ├── usePageLeave.ts                # ⭐ PRIORIDADE BAIXA
│   └── useThrowError.ts               # ⭐ PRIORIDADE BAIXA
├── icons/                              # ⭐ PRIORIDADE ALTA
│   ├── AlertIcon.tsx
│   ├── ApplePayIcon.tsx
│   ├── BarcodeIcon.tsx
│   ├── GooglePayIcon.tsx
│   ├── NubankIcon.tsx
│   ├── PicPayIcon.tsx
│   ├── PixIcon.tsx
│   └── PixAutoIcon.tsx
├── services/                           # Substituir por lib/api/
│   ├── cepService.ts
│   └── checkout/
└── utils/                              # ✅ JÁ MIGRADO
    └── pixels.ts                       # ✅ JÁ MIGRADO
```

### Destino (cakto-checkoutv2/apps/web/src)
```
src/
├── hooks/
│   ├── useFacebookPixels.ts           # ← IMPLEMENTAR (atualmente placeholder)
│   ├── useGoogleAds.ts                # ← IMPLEMENTAR (atualmente placeholder)
│   ├── useTikTokPixels.ts             # ← IMPLEMENTAR (atualmente placeholder)
│   ├── useKwaiPixels.ts               # ← IMPLEMENTAR (atualmente placeholder)
│   ├── useCepSearch.ts                # ← MIGRAR
│   ├── useCouponDiscount.ts           # ← MIGRAR
│   ├── usePrice.ts                    # ← MIGRAR
│   ├── useServiceFee.ts               # ← MIGRAR
│   ├── useFetchCalculatedInstallments.ts  # ← MIGRAR
│   ├── useIsFetchingCalculatedInstallments.ts  # ← MIGRAR
│   ├── useTrackingPixels.ts           # ← MIGRAR
│   ├── useDevice.ts                   # ← MIGRAR
│   ├── useMercadoPagoDeviceId.ts      # ← MIGRAR
│   ├── useDebounce.ts                 # ← MIGRAR
│   ├── usePageLeave.ts                # ← MIGRAR
│   └── useThrowError.ts               # ← MIGRAR
├── components/
│   └── icons/                          # ← MIGRAR ícones
│       ├── AlertIcon.tsx
│       ├── ApplePayIcon.tsx
│       ├── BarcodeIcon.tsx
│       ├── GooglePayIcon.tsx
│       ├── NubankIcon.tsx
│       ├── PicPayIcon.tsx
│       ├── PixIcon.tsx
│       └── PixAutoIcon.tsx
├── lib/
│   ├── api/                            # ✅ JÁ CRIADO
│   │   └── checkout-client.ts          # ✅ JÁ CRIADO
│   └── utils/                          # ✅ JÁ MIGRADO
│       └── pixels.ts                  # ✅ JÁ MIGRADO (resolveFacebookConfig, etc)
└── contexts/                           # ✅ JÁ MIGRADO (Fase 3)
    └── checkout-context.tsx            # ✅ Usa hooks de pixels
```

## 📚 Referências e Exemplos

### Exemplo de Hook Adaptado para Next.js

Veja o exemplo de `useNotifications` que já foi migrado:

```typescript
"use client";

import { useReducer } from "react";
import { v4 } from "uuid";
import type { CheckoutComponentNotificationType } from "@/types/builder";

export type Notification = {
	id: string;
	message: string;
	exibitionTime: number;
	type: CheckoutComponentNotificationType;
};

const useNotifications = () => {
	const [{ notifications }, dispatch] = useReducer(reducer, initialState);
	
	// ... lógica
	
	return { notifications, notify, dismiss };
};

export default useNotifications;
```

**Padrão a seguir:**
1. Adicionar `"use client"` no topo
2. Atualizar imports para usar `@/` paths
3. Substituir React Router por props ou `useCheckout()` context
4. Substituir React Query por `useCheckout()` context
5. Manter toda lógica de negócio

### Contextos Já Migrados (Fase 3)

Os hooks de pixels devem usar o `CheckoutContext` ao invés de React Query:

```typescript
// ANTES (Vite + React)
const { id } = useParams<{ id: string }>();
const queryClient = useQueryClient();
const offer = queryClient.getQueryData(['checkout', id]) as ProductData;

// DEPOIS (Next.js 16)
import { useCheckout } from "@/contexts";

const { offer } = useCheckout();
```

### Utils de Pixels Já Migrados

O arquivo `lib/utils/pixels.ts` já foi migrado e contém:
- `resolveFacebookConfig()`
- `resolveTiktokConfig()`
- `resolveKwaiConfig()`

Use essas funções nos hooks de pixels!

## 🚀 Plano de Execução - Fase 4

### 1. Hooks de Pixels (PRIORIDADE MÁXIMA)

#### 1.1. useFacebookPixels

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useFacebookPixels.ts`

**Arquivo Destino**: `apps/web/src/hooks/useFacebookPixels.ts` (já existe como placeholder)

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo (já existe)

2. **Substituir React Router e React Query**:
   ```typescript
   // ANTES
   const { id } = useParams<{ id: string }>();
   const queryClient = useQueryClient();
   const offer = queryClient.getQueryData(['checkout', id]) as ProductData;
   
   // DEPOIS
   import { useCheckout } from "@/contexts";
   
   const { offer } = useCheckout();
   ```

3. **Usar utils de pixels migrados**:
   ```typescript
   // ANTES
   import { resolveFacebookConfig } from '@/utils/pixels';
   
   // DEPOIS
   import { resolveFacebookConfig } from '@/lib/utils/pixels';
   ```

4. **Implementar tracking real**:
   ```typescript
   // ANTES (placeholder)
   console.log("[Facebook Pixel] onTicketGenerated");
   
   // DEPOIS
   import ReactFacebookPixel from '@bettercart/react-facebook-pixel';
   
   ReactFacebookPixel.trackCustom('boleto_gerado', {
     content_ids: contentIds,
     value: Number(value),
     currency: 'BRL',
   }, {
     eventID: payment?.id,
   });
   ```

5. **Atualizar imports**:
   ```typescript
   // ANTES
   import { Payment, ProductData } from '../types';
   import { TrackingPixels, FacebookPixel } from '../types/pixels';
   
   // DEPOIS
   import type { Payment, ProductData } from "@/types";
   import type { TrackingPixels, FacebookPixel } from "@/types/pixels";
   ```

**Dependências:**
- `@bettercart/react-facebook-pixel` - Verificar se está instalado
- `lib/utils/pixels.ts` - ✅ Já migrado
- `contexts/checkout-context.tsx` - ✅ Já migrado (Fase 3)

#### 1.2. useGoogleAds

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useGoogleAds.ts`

**Arquivo Destino**: `apps/web/src/hooks/useGoogleAds.ts` (já existe como placeholder)

**Adaptações Necessárias:**

1. **Substituir React Router e React Query**:
   ```typescript
   // ANTES
   const { id } = useParams<{ id: string }>();
   const queryClient = useQueryClient();
   const offer = queryClient.getQueryData(['checkout', id]) as ProductData;
   
   // DEPOIS
   import { useCheckout } from "@/contexts";
   
   const { offer } = useCheckout();
   ```

2. **Implementar tracking real com gtag**:
   ```typescript
   // ANTES (placeholder)
   console.log("[Google Ads] onTicketGenerated");
   
   // DEPOIS
   if (typeof window !== 'undefined' && window.gtag) {
     window.gtag('event', 'conversion', {
       send_to: `${pixel.pixelId}/${pixel.conversionLabel}`,
       value,
       currency: 'BRL',
       transaction_id: payment?.id,
       event_category: 'Boleto',
       event_action: 'Gerado',
       event_label: 'Boleto Gerado',
     });
   }
   ```

3. **Adicionar tipos para window.gtag**:
   ```typescript
   declare global {
     interface Window {
       gtag?: (...args: unknown[]) => void;
     }
   }
   ```

#### 1.3. useTikTokPixels

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useTikTokPixels.ts`

**Arquivo Destino**: `apps/web/src/hooks/useTikTokPixels.ts` (já existe como placeholder)

**Adaptações Necessárias:**

1. **Substituir React Router e React Query** (mesmo padrão dos outros)

2. **Usar utils de pixels migrados**:
   ```typescript
   import { resolveTiktokConfig } from '@/lib/utils/pixels';
   ```

3. **Implementar tracking real com TikTok Pixel**:
   ```typescript
   // Verificar se TikTok Pixel está carregado
   if (typeof window !== 'undefined' && window.ttq) {
     window.ttq.track('CompletePayment', {
       content_type: 'product',
       content_id: payment?.id,
       value: value,
       currency: 'BRL',
     });
   }
   ```

#### 1.4. useKwaiPixels

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useKwaiPixels.ts`

**Arquivo Destino**: `apps/web/src/hooks/useKwaiPixels.ts` (já existe como placeholder)

**Adaptações Necessárias:**

1. **Substituir React Router e React Query** (mesmo padrão dos outros)

2. **Usar utils de pixels migrados**:
   ```typescript
   import { resolveKwaiConfig } from '@/lib/utils/pixels';
   ```

3. **Implementar tracking real com Kwai Pixel**:
   ```typescript
   // Verificar implementação específica do Kwai Pixel
   // Pode usar window.kwaiPixel ou similar
   ```

### 2. Hooks Auxiliares (PRIORIDADE ALTA)

#### 2.1. useCepSearch

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useCepSearch.ts`

**Arquivo Destino**: `apps/web/src/hooks/useCepSearch.ts`

**Adaptações Necessárias:**

1. **Substituir Axios por fetch**:
   ```typescript
   // ANTES
   import axios from 'axios';
   const response = await axios.get(`https://viacep.com.br/ws/${cep}/json/`);
   
   // DEPOIS
   const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
   const data = await response.json();
   ```

2. **Adicionar `"use client"`** no topo

3. **Atualizar imports** para usar `@/` paths

#### 2.2. useCouponDiscount

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useCouponDiscount.ts`

**Arquivo Destino**: `apps/web/src/hooks/useCouponDiscount.ts`

**Adaptações Necessárias:**

1. **Usar `useCheckout()` context** ao invés de React Query
2. **Adicionar `"use client"`** no topo
3. **Atualizar imports** para usar `@/` paths

#### 2.3. usePrice

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/usePrice.ts`

**Arquivo Destino**: `apps/web/src/hooks/usePrice.ts`

**Adaptações Necessárias:**

1. **Usar `useCheckout()` context** ao invés de React Query
2. **Adicionar `"use client"`** no topo
3. **Atualizar imports** para usar `@/` paths

#### 2.4. useServiceFee

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useServiceFee.ts`

**Arquivo Destino**: `apps/web/src/hooks/useServiceFee.ts`

**Adaptações Necessárias:**

1. **Usar `useCheckout()` context** ao invés de React Query
2. **Adicionar `"use client"`** no topo
3. **Atualizar imports** para usar `@/` paths

#### 2.5. useFetchCalculatedInstallments

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useFetchCalculatedInstallments.ts`

**Arquivo Destino**: `apps/web/src/hooks/useFetchCalculatedInstallments.ts`

**Adaptações Necessárias:**

1. **Substituir Axios por fetch** ou usar `lib/api/checkout-client.ts`
2. **Usar `useCheckout()` context** ao invés de React Query
3. **Adicionar `"use client"`** no topo
4. **Atualizar imports** para usar `@/` paths

#### 2.6. useIsFetchingCalculatedInstallments

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useIsFetchingCalculatedInstallments.ts`

**Arquivo Destino**: `apps/web/src/hooks/useIsFetchingCalculatedInstallments.ts`

**Adaptações Necessárias:**

1. **Usar `useCheckout()` context** ao invés de React Query
2. **Adicionar `"use client"`** no topo
3. **Atualizar imports** para usar `@/` paths

### 3. Hooks de Tracking (PRIORIDADE MÉDIA)

#### 3.1. useTrackingPixels

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useTrackingPixels.ts`

**Arquivo Destino**: `apps/web/src/hooks/useTrackingPixels.ts`

**Adaptações Necessárias:**

1. **Usar hooks de pixels migrados** ao invés de criar novos
2. **Adicionar `"use client"`** no topo
3. **Atualizar imports** para usar `@/` paths

#### 3.2. useDevice

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useDevice.ts`

**Arquivo Destino**: `apps/web/src/hooks/useDevice.ts`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Verificar se usa `window`** (adicionar checks SSR)
3. **Atualizar imports** para usar `@/` paths

#### 3.3. useMercadoPagoDeviceId

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useMercadoPagoDeviceId.ts`

**Arquivo Destino**: `apps/web/src/hooks/useMercadoPagoDeviceId.ts`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Verificar se usa `window`** (adicionar checks SSR)
3. **Atualizar imports** para usar `@/` paths

### 4. Hooks de UI/UX (PRIORIDADE BAIXA)

#### 4.1. useDebounce

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useDebounce.ts`

**Arquivo Destino**: `apps/web/src/hooks/useDebounce.ts`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Atualizar imports** para usar `@/` paths
3. **Manter lógica idêntica** (geralmente não precisa de mudanças)

#### 4.2. usePageLeave

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/usePageLeave.ts`

**Arquivo Destino**: `apps/web/src/hooks/usePageLeave.ts`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Verificar se usa `window`** (adicionar checks SSR)
3. **Atualizar imports** para usar `@/` paths

#### 4.3. useThrowError

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useThrowError.ts`

**Arquivo Destino**: `apps/web/src/hooks/useThrowError.ts`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Usar `errorHandler` migrado** (`lib/utils/error-handler.ts`)
3. **Atualizar imports** para usar `@/` paths

### 5. Ícones Personalizados (PRIORIDADE ALTA)

**Arquivo Original**: `_docs/cakto-checkout/src/icons/*.tsx`

**Arquivo Destino**: `apps/web/src/components/icons/*.tsx` ou `apps/web/src/assets/icons/*.tsx`

**Adaptações Necessárias:**

1. **Migrar todos os ícones**:
   - `AlertIcon.tsx`
   - `ApplePayIcon.tsx`
   - `BarcodeIcon.tsx`
   - `GooglePayIcon.tsx`
   - `NubankIcon.tsx`
   - `PicPayIcon.tsx`
   - `PixIcon.tsx`
   - `PixAutoIcon.tsx`

2. **Atualizar imports no CheckoutContext**:
   ```typescript
   // ANTES (placeholder)
   import { CreditCardIcon } from "@heroicons/react/20/solid";
   
   // DEPOIS
   import { PixIcon } from "@/components/icons/PixIcon";
   import { BarcodeIcon } from "@/components/icons/BarcodeIcon";
   // ... etc
   ```

3. **Manter estrutura SVG** dos ícones originais

## 📝 Checklist de Migração por Hook

Para cada hook migrado, verificar:

- [ ] Adicionado `"use client"` no topo
- [ ] Imports atualizados para estrutura Next.js (`@/` paths)
- [ ] Removido uso de React Router (substituir por props ou `useCheckout()` context)
- [ ] Removido uso de React Query (substituir por `useCheckout()` context ou `useState`/`useEffect`)
- [ ] Removido uso de Axios (substituir por `fetch` ou `lib/api/`)
- [ ] Removido código específico do Vite (`import.meta.env`)
- [ ] Mantida toda lógica de negócio
- [ ] Adicionados checks SSR quando necessário (`typeof window !== "undefined"`)
- [ ] Testado funcionamento no navegador

## 🔍 Arquivos de Referência

### Código de Referência
- `apps/web/src/hooks/useNotifications.ts` - Exemplo de hook migrado
- `apps/web/src/contexts/checkout-context.tsx` - Contexto que usa hooks de pixels
- `apps/web/src/lib/utils/pixels.ts` - Utils de pixels já migrados
- `apps/web/src/lib/api/checkout-client.ts` - API client para operações client-side

### Projeto Original
- `_docs/cakto-checkout/src/hooks/useFacebookPixels.ts` - Hook original
- `_docs/cakto-checkout/src/hooks/useGoogleAds.ts` - Hook original
- `_docs/cakto-checkout/src/hooks/useTikTokPixels.ts` - Hook original
- `_docs/cakto-checkout/src/hooks/useKwaiPixels.ts` - Hook original
- `_docs/cakto-checkout/src/icons/` - Ícones originais

## ⚠️ Regras Importantes

### NUNCA
- ❌ Reescrever lógica de negócio - apenas adaptar
- ❌ Usar `import.meta.env` - usar `lib/env.ts`
- ❌ Usar `axios` - usar `fetch` ou `lib/api/`
- ❌ Usar React Router - usar props ou `useCheckout()` context
- ❌ Usar React Query diretamente - usar `useCheckout()` context
- ❌ Criar Server Components com hooks do React

### SEMPRE
- ✅ Adicionar `"use client"` em todos os hooks
- ✅ Usar path aliases (`@/`) para imports
- ✅ Usar `useCheckout()` context ao invés de React Query
- ✅ Adicionar checks SSR quando usar `window` ou APIs do navegador
- ✅ Manter compatibilidade com APIs existentes
- ✅ Testar funcionamento no navegador

## 🎯 Prioridades

1. **MÁXIMA**: Hooks de Pixels (necessários para CheckoutContext funcionar completamente)
   - `useFacebookPixels`
   - `useGoogleAds`
   - `useTikTokPixels`
   - `useKwaiPixels`

2. **ALTA**: Hooks Auxiliares (necessários para funcionalidades do checkout)
   - `useCepSearch`
   - `useCouponDiscount`
   - `usePrice`
   - `useServiceFee`
   - `useFetchCalculatedInstallments`
   - `useIsFetchingCalculatedInstallments`
   - Ícones personalizados

3. **MÉDIA**: Hooks de Tracking
   - `useTrackingPixels`
   - `useDevice`
   - `useMercadoPagoDeviceId`

4. **BAIXA**: Hooks de UI/UX
   - `useDebounce`
   - `usePageLeave`
   - `useThrowError`

## 🚀 Começar Agora

**Inicie pelos hooks de pixels** (prioridade máxima). Para cada hook:

1. Ler o arquivo original completo
2. Identificar todas as dependências (React Router, React Query, Axios, etc)
3. Adaptar para Next.js (substituir por contextos migrados, fetch, etc)
4. Implementar tracking real (remover console.log)
5. Atualizar imports para usar `@/` paths
6. Testar funcionamento no navegador
7. Continuar para o próximo hook

**Lembre-se**: O objetivo é **reutilizar ao máximo**, não reescrever. Os hooks devem funcionar quase idênticos ao original, apenas com adaptações mínimas para Next.js.

## 📌 Notas Importantes

- Os hooks de pixels atualmente têm placeholders com `console.log`. Substitua por implementação real de tracking.
- Use o `useCheckout()` context ao invés de React Query para acessar dados do checkout.
- Use os utils de pixels já migrados (`lib/utils/pixels.ts`) para configuração.
- Adicione checks SSR (`typeof window !== "undefined"`) quando usar APIs do navegador.
- Os ícones devem ser migrados para `components/icons/` ou `assets/icons/` e atualizados no `CheckoutContext`.

## 🔗 Dependências Externas

Verificar se as seguintes bibliotecas estão instaladas:

- `@bettercart/react-facebook-pixel` - Para Facebook Pixel
- `clientjs` - Para fingerprint (já usado em `checkout-client.ts`)
- Outras bibliotecas específicas de cada hook

Se não estiverem instaladas, adicionar ao `package.json` ou criar implementações alternativas.

