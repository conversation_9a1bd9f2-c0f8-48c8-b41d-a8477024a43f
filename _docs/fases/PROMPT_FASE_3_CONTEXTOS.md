# Prompt para Fase 3: Migra<PERSON> de Contextos - Checkout V2

## 🎯 Objetivo

Migrar os contextos do checkout original (`cakto-checkout` - Vite + React) para o novo projeto (`cakto-checkoutv2` - Next.js 16), **adaptando-os para funcionar com SSR (Server-Side Rendering)** e receber dados iniciais do servidor.

## 📋 Contexto do Projeto

### Projeto Original (Fonte)
- **Localização**: `/Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2/_docs/cakto-checkout/src`
- **Stack**: Vite + React 18 + TypeScript
- **Roteamento**: React Router DOM
- **HTTP Client**: Axios
- **Estado**: React Query (tanstack-query)

### Projeto Novo (Destino)
- **Localização**: `/Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2/apps/web/src`
- **Stack**: Next.js 16 + React 19 + TypeScript
- **Roteamento**: Next.js App Router
- **HTTP Client**: fetch nativo (server-side) ou Server Actions
- **Estado**: React Query (se necessário) ou useState/useEffect

### Status Atual da Migração

#### ✅ Fase 1: Base (Tipos e Utilitários) - CONCLUÍDA
- ✅ Tipos TypeScript migrados (`types/`)
- ✅ Utilitários migrados (`lib/utils/`)
- ✅ Constantes criadas (`constants/payment.ts`)
- ✅ Helpers de ambiente adaptados (`lib/env.ts`)

#### ✅ Fase 2: Componentes do Builder - CONCLUÍDA
- ✅ Componentes do builder migrados (`components/builder/`)
- ✅ Hook `useImagePreview` migrado
- ✅ Assets copiados (`assets/`)
- ⚠️ **Alguns componentes dependem de contextos ainda não migrados**:
  - `CheckoutComponentCountdown` → precisa de `CheckoutContext`
  - `CheckoutComponentExitPopup` → precisa de `CheckoutModeContext`
  - `CheckoutComponentNotification` → precisa de `NotificationContext` e `CheckoutContext`
  - `CheckoutComponentRenderer` → precisa de `CheckoutModeContext`

#### 🔄 Fase 3: Contextos - EM ANDAMENTO
- ⏳ `CheckoutContext` - **PRIORIDADE MÁXIMA**
- ⏳ `CheckoutModeContext` - **PRIORIDADE ALTA**
- ⏳ `NotificationContext` - **PRIORIDADE MÉDIA**

## 🎯 Princípios da Migração

### ✅ REUTILIZAR (NÃO REESCREVER)
- **Lógica de Negócio**: Manter toda validação, cálculos e regras
- **Estrutura de Estado**: Manter estrutura similar, apenas adaptar para SSR
- **Hooks Customizados**: Manter lógica, apenas atualizar imports

### ⚠️ ADAPTAR (MUDANÇAS NECESSÁRIAS)
- **SSR Support**: Adaptar para receber `initialData` do SSR
- **Imports**: Atualizar paths para estrutura Next.js (`@/` ao invés de paths relativos)
- **Client Components**: Adicionar `"use client"` no topo
- **React Router**: Substituir `useParams`, `useSearchParams` por Next.js equivalents
- **React Query**: Adaptar ou substituir por useState/useEffect se necessário
- **APIs**: Usar `lib/api/` ao invés de `services/`

### ❌ REMOVER/SUBSTITUIR
- **React Router**: Remover `useParams`, `useSearchParams`, `useNavigate`, `useLocation`
- **Axios**: Substituir por `fetch` ou helpers de `lib/api/`
- **import.meta.env**: Substituir por helpers de `lib/env.ts`
- **Vite-specific code**: Remover qualquer código específico do Vite

## 📁 Estrutura de Migração

### Origem (cakto-checkout)
```
src/
├── contexts/
│   ├── CheckoutContext.tsx        # ⭐ PRIORIDADE MÁXIMA
│   ├── CheckoutModeContext.tsx     # ⭐ PRIORIDADE ALTA
│   └── NotificationContext.tsx    # ⭐ PRIORIDADE MÉDIA
├── hooks/                          # Usados pelos contextos
├── services/                       # Substituir por lib/api/
└── types/                          # ✅ JÁ MIGRADO
```

### Destino (cakto-checkoutv2/apps/web/src)
```
src/
├── contexts/
│   ├── checkout-context.tsx        # ← MIGRAR CheckoutContext
│   ├── checkout-mode-context.tsx   # ← MIGRAR CheckoutModeContext
│   └── notification-context.tsx   # ← MIGRAR NotificationContext
├── hooks/                          # ← MIGRAR hooks necessários
├── lib/
│   ├── api/                        # ✅ JÁ CRIADO
│   └── utils/                      # ✅ JÁ MIGRADO
└── types/                          # ✅ JÁ MIGRADO
```

## 📚 Referências e Exemplos

### Exemplo de Contexto Adaptado para SSR

Veja o exemplo de `intl-context.tsx` que já foi adaptado:

```typescript
"use client";

import { createContext, useContext, type ReactNode, useMemo } from "react";

type IntlContextValue = {
	locale: Locale;
	messages: Messages;
};

const IntlContext = createContext<IntlContextValue | null>(null);

export function IntlProvider({
	locale,
	messages,
	children,
}: {
	locale: Locale;
	messages: Messages;
	children: ReactNode;
}) {
	const value = useMemo(
		() => ({
			locale,
			messages,
		}),
		[locale, messages],
	);

	return <IntlContext.Provider value={value}>{children}</IntlContext.Provider>;
}
```

**Padrão a seguir:**
1. Adicionar `"use client"` no topo
2. Receber `initialData` como prop
3. Usar `useState(initialData)` para inicializar estado
4. Manter toda lógica de negócio

### API Client Já Criado

O projeto já tem um API client em `lib/api/checkout.ts`:

```typescript
import "server-only";

export async function getCheckoutData(
	id: string,
	affiliateShortId?: string,
): Promise<CheckoutData> {
	// ... implementação
}
```

**Para client-side**, você precisará criar funções similares que usem `fetch` diretamente.

## 🚀 Plano de Execução - Fase 3

### 1. CheckoutContext (PRIORIDADE MÁXIMA)

**Arquivo Original**: `_docs/cakto-checkout/src/contexts/CheckoutContext.tsx`

**Arquivo Destino**: `apps/web/src/contexts/checkout-context.tsx`

#### Adaptações Necessárias:

1. **Adicionar `"use client"`** no topo

2. **Adaptar para receber `initialData` do SSR**:
   ```typescript
   export function CheckoutProvider({ 
     children, 
     initialData 
   }: { 
     children: React.ReactNode;
     initialData: ProductData;
   }) {
     const [offer, setOffer] = useState<ProductData | undefined>(initialData);
     // ... resto da lógica
   }
   ```

3. **Substituir React Router**:
   ```typescript
   // ANTES
   const { id } = useParams<{ id: string }>();
   const [searchParams] = useSearchParams();
   
   // DEPOIS
   // Receber id e affiliateShortId como props do SSR
   // Ou usar usePathname/useSearchParams do next/navigation (client-side)
   ```

4. **Substituir React Query**:
   ```typescript
   // ANTES
   const { data: offer, isLoading } = useQuery(...);
   
   // DEPOIS
   // Usar initialData do SSR e useState/useEffect para atualizações
   // Ou manter React Query se necessário (instalar @tanstack/react-query)
   ```

5. **Substituir Axios**:
   ```typescript
   // ANTES
   import { startPayment } from '@/services/checkout';
   
   // DEPOIS
   // Criar funções em lib/api/ que usem fetch
   // Ou usar Server Actions do Next.js
   ```

6. **Atualizar imports**:
   ```typescript
   // ANTES
   import { ProductData } from '@/types';
   import { trackEvent } from '@/utils/analytics';
   
   // DEPOIS
   import { ProductData } from '@/types';
   import { trackEvent } from '@/lib/utils/analytics';
   ```

#### Dependências do CheckoutContext:

- `hooks/useFacebookPixels` - Migrar na Fase 4
- `hooks/useGoogleAds` - Migrar na Fase 4
- `hooks/useTikTokPixels` - Migrar na Fase 4
- `hooks/useKwaiPixels` - Migrar na Fase 4
- `services/checkout` - Substituir por `lib/api/`
- `components/checkout/Payments` - Migrar na Fase 5
- `icons/*` - Verificar se existem ou criar
- `utils/errorHandler` - Migrar se necessário

**Estratégia**: Migrar o contexto primeiro, deixando placeholders para hooks e serviços que serão migrados depois.

### 2. CheckoutModeContext (PRIORIDADE ALTA)

**Arquivo Original**: `_docs/cakto-checkout/src/contexts/CheckoutModeContext.tsx`

**Arquivo Destino**: `apps/web/src/contexts/checkout-mode-context.tsx`

#### Adaptações Necessárias:

1. **Adicionar `"use client"`** no topo

2. **Adaptar para receber `preview` do SSR**:
   ```typescript
   "use client";
   
   import { createContext, useContext, useMemo } from "react";
   
   type CheckoutModeContextValue = {
     preview: boolean;
   };
   
   const CheckoutModeContext = createContext<CheckoutModeContextValue | null>(null);
   
   export function CheckoutModeProvider({
     children,
     preview,
   }: {
     children: React.ReactNode;
     preview: boolean;
   }) {
     const value = useMemo(() => ({ preview }), [preview]);
   
     return (
       <CheckoutModeContext.Provider value={value}>
         {children}
       </CheckoutModeContext.Provider>
     );
   }
   
   export function useCheckoutMode() {
     const context = useContext(CheckoutModeContext);
     if (!context) {
       throw new Error("useCheckoutMode must be used within CheckoutModeProvider");
     }
     return context;
   }
   ```

3. **Atualizar imports** para usar path aliases

**Nota**: Este contexto é simples e já está quase pronto. Precisa apenas de adaptações mínimas.

### 3. NotificationContext (PRIORIDADE MÉDIA)

**Arquivo Original**: `_docs/cakto-checkout/src/contexts/NotificationContext.tsx`

**Arquivo Destino**: `apps/web/src/contexts/notification-context.tsx`

#### Adaptações Necessárias:

1. **Adicionar `"use client"`** no topo

2. **Migrar hook `useNotifications`** primeiro (Fase 4)

3. **Adaptar estrutura**:
   ```typescript
   "use client";
   
   import { createContext, useContext } from "react";
   import { useNotifications } from "@/hooks/useNotifications";
   
   type NotificationContextData = ReturnType<typeof useNotifications>;
   
   const NotificationContext = createContext<NotificationContextData | null>(null);
   
   export function NotificationProvider({ 
     children 
   }: { 
     children: React.ReactNode;
   }) {
     const { notifications, notify, dismiss } = useNotifications();
   
     return (
       <NotificationContext.Provider
         value={{
           notifications,
           notify,
           dismiss,
         }}
       >
         {children}
       </NotificationContext.Provider>
     );
   }
   
   export function useNotificationContext() {
     const context = useContext(NotificationContext);
     if (!context) {
       throw new Error("useNotificationContext must be used within NotificationProvider");
     }
     return context;
   }
   ```

4. **Atualizar imports** para usar path aliases

## 📝 Checklist de Migração por Contexto

Para cada contexto migrado, verificar:

- [ ] Adicionado `"use client"` no topo
- [ ] Imports atualizados para estrutura Next.js (`@/` paths)
- [ ] Adaptado para receber `initialData` do SSR (se aplicável)
- [ ] Removido uso de React Router (substituir por props ou next/navigation)
- [ ] Removido uso de Axios (substituir por `fetch` ou `lib/api/`)
- [ ] Removido uso de React Query (ou adaptado para Next.js)
- [ ] Removido código específico do Vite (`import.meta.env`)
- [ ] Mantida toda lógica de negócio
- [ ] Criado hook customizado para usar o contexto (ex: `useCheckout()`)
- [ ] Adicionado tratamento de erro quando contexto não está disponível
- [ ] Testado funcionamento no navegador

## 🔍 Arquivos de Referência

### Código de Referência
- `apps/web/src/contexts/intl-context.tsx` - Exemplo de contexto adaptado para SSR
- `apps/web/src/lib/api/checkout.ts` - Exemplo de API client
- `apps/web/src/lib/env.ts` - Helpers de variáveis de ambiente
- `apps/web/src/app/[locale]/[id]/page.tsx` - Exemplo de Server Component que pode passar initialData

### Projeto Original
- `_docs/cakto-checkout/src/contexts/CheckoutContext.tsx` - Contexto original
- `_docs/cakto-checkout/src/contexts/CheckoutModeContext.tsx` - Contexto original
- `_docs/cakto-checkout/src/contexts/NotificationContext.tsx` - Contexto original

## ⚠️ Regras Importantes

### NUNCA
- ❌ Reescrever lógica de negócio - apenas adaptar
- ❌ Usar `import.meta.env` - usar `lib/env.ts`
- ❌ Usar `axios` - usar `fetch` ou `lib/api/`
- ❌ Usar React Router - usar Next.js App Router
- ❌ Criar Server Components com hooks do React

### SEMPRE
- ✅ Adicionar `"use client"` em todos os contextos
- ✅ Usar path aliases (`@/`) para imports
- ✅ Adaptar para receber dados iniciais do SSR
- ✅ Criar hooks customizados para usar contextos
- ✅ Adicionar tratamento de erro quando contexto não está disponível
- ✅ Manter compatibilidade com APIs existentes

## 🎯 Prioridades

1. **ALTA**: CheckoutContext (necessário para vários componentes)
2. **ALTA**: CheckoutModeContext (necessário para preview mode)
3. **MÉDIA**: NotificationContext (necessário para notificações)

## 🚀 Começar Agora

**Inicie pelo CheckoutContext** (prioridade máxima). Para cada contexto:

1. Ler o arquivo original completo
2. Identificar todas as dependências (hooks, serviços, componentes)
3. Adaptar para Next.js (SSR, imports, remover React Router)
4. Criar versão adaptada no destino
5. Testar funcionamento básico
6. Continuar para o próximo contexto

**Lembre-se**: O objetivo é **reutilizar ao máximo**, não reescrever. Os contextos devem funcionar quase idênticos ao original, apenas com adaptações mínimas para Next.js e SSR.

## 📌 Notas Importantes

- Alguns hooks e serviços usados pelos contextos ainda não foram migrados. Deixe placeholders ou implementações básicas que serão completadas nas próximas fases.
- O CheckoutContext é complexo e tem muitas dependências. Foque em migrar a estrutura principal primeiro, depois adapte as dependências.
- Use o exemplo de `intl-context.tsx` como referência para o padrão de adaptação para SSR.

