# PROMPT: Fase 6 - i18n Completo + Header + Componentes Essenciais

## 🎯 Contexto

Você é um agente de IA especializado em migração de projetos React/Next.js. Este projeto está migrando do Vite + React 18 para Next.js 16 + React 19.

**Fases anteriores concluídas:**
- ✅ Fase 1-5: Infraestrutura, contexts, hooks, componentes de pagamento

**Status atual:**
- ✅ Sistema de pagamento funcional
- ✅ Checkout básico renderizando
- ⚠️ i18n incompleto (apenas 5 mensagens)
- ⚠️ Textos hardcoded em português
- ❌ Sem header com seletor de idioma
- ❌ Componentes essenciais faltando (AddressForm, BumpItem, etc)

**Documentos importantes:**
- `STATUS_MIGRACAO_FASE_5.md` - Status da fase 5
- `ANALISE_GAPS_E_MELHORIAS.md` - Análise completa de gaps
- `.cursorrules` - Regras do projeto

---

## 📋 Objetivos da Fase 6

### Prioridade 1 (CRÍTICO) ⭐⭐⭐

1. **Sistema de i18n Completo**
   - Expandir mensagens de tradução (pt, es, en)
   - Criar hook `useTranslation` otimizado
   - Traduzir TODOS os componentes
   - Migrar sistema de erros com i18n

2. **Header com Seletor de Locale**
   - Criar `CheckoutHeader` component
   - Criar `LocaleSelector` dropdown
   - Integrar no layout do checkout
   - Persistência em cookie + redirecionamento

3. **Componentes Essenciais**
   - Migrar `AddressForm` (formulário de endereço)
   - Migrar `BumpItem` (order bumps)
   - Migrar `AboutCpf` (modal informativo)
   - Migrar componentes comuns (EmailAutoComplete, etc)

### Prioridade 2 (IMPORTANTE) ⭐⭐

4. **Otimizações SSR/SSG**
   - Separar Server Components
   - Adicionar Suspense boundaries
   - Metadata dinâmico por produto

5. **In-App Browser Support**
   - Detectar TikTok/Instagram/Facebook browsers
   - Mostrar warning quando necessário
   - Adaptar métodos de pagamento

### Prioridade 3 (DESEJÁVEL) ⭐

6. **Performance**
   - Code splitting de formulários
   - Lazy load de QR Code/Confetti
   - Image optimization

---

## 🏗️ Estrutura de Arquivos

### Novos Arquivos a Criar

```
apps/web/src/
├── components/
│   ├── checkout/
│   │   ├── checkout-header.tsx           # NOVO - Header do checkout
│   │   ├── locale-selector.tsx           # NOVO - Seletor de idioma
│   │   ├── in-app-browser-warning.tsx    # NOVO - Aviso in-app
│   │   ├── address-form.tsx              # NOVO - Formulário de endereço
│   │   └── bump-item.tsx                 # NOVO - Order bump
│   │
│   ├── info/
│   │   ├── about-cpf.tsx                 # NOVO - Modal CPF
│   │   └── about-pix-security.tsx        # NOVO - Alerta PIX
│   │
│   └── common/
│       ├── email-autocomplete.tsx        # NOVO
│       ├── text-field-phone-number.tsx   # NOVO
│       ├── link-button.tsx               # NOVO
│       ├── service-fee-tooltip.tsx       # NOVO
│       └── toast.tsx                     # NOVO
│
├── hooks/
│   ├── useTranslation.ts                 # NOVO - Hook de tradução
│   ├── useCouponDiscount.ts              # NOVO - Desconto cupom
│   ├── useInAppBrowser.ts                # NOVO - Detecção browser
│   └── useDebounce.ts                    # NOVO - Debounce
│
├── lib/
│   ├── i18n/
│   │   ├── messages/
│   │   │   ├── pt.json                   # EXPANDIR
│   │   │   ├── en.json                   # EXPANDIR
│   │   │   └── es.json                   # EXPANDIR
│   │   ├── types.ts                      # NOVO - Types de mensagens
│   │   └── utils.ts                      # NOVO - Helpers i18n
│   │
│   └── utils/
│       ├── browser.ts                    # NOVO - Browser detection
│       └── interpolate.ts                # NOVO - Interpolação strings
│
└── types/
    └── i18n.ts                           # NOVO - Types i18n
```

---

## 📝 Tarefas Detalhadas

### Tarefa 1: Expandir Sistema de i18n

#### 1.1 Criar Types de Mensagens

```typescript
// apps/web/src/types/i18n.ts
export type Messages = {
  checkout: {
    title: string;
    loading: string;
    subtitle: string;
    // ... adicionar todas
  };
  common: {
    continue: string;
    retry: string;
    save: string;
    cancel: string;
    close: string;
    yes: string;
    no: string;
    loading: string;
    // ... adicionar todas
  };
  errors: {
    validation: {
      required: string;
      invalid_email: string;
      invalid_phone: string;
      invalid_cpf: string;
      invalid_cnpj: string;
      invalid_card_number: string;
      invalid_cvv: string;
      invalid_expiry: string;
      min_length: string;
      max_length: string;
      generic: string;
    };
    network: {
      connection_failed: string;
      timeout: string;
      server_error: string;
      not_found: string;
      unauthorized: string;
      forbidden: string;
      generic: string;
    };
    payment: {
      card_declined: string;
      insufficient_funds: string;
      invalid_card: string;
      expired_card: string;
      processing_error: string;
      provider_error: string;
      generic: string;
    };
    threeds: {
      authentication_failed: string;
      challenge_failed: string;
      not_enrolled: string;
      unsupported_card: string;
      generic: string;
    };
    system: {
      unexpected_error: string;
      service_unavailable: string;
      maintenance: string;
      generic: string;
    };
    generic: {
      something_went_wrong: string;
      try_again: string;
      contact_support: string;
    };
  };
  form: {
    labels: {
      name: string;
      email: string;
      phone: string;
      cpf: string;
      cnpj: string;
      cpf_cnpj: string;
      card_number: string;
      card_expiry: string;
      card_cvv: string;
      card_holder_name: string;
      installments: string;
      coupon: string;
      zipcode: string;
      street: string;
      number: string;
      complement: string;
      neighborhood: string;
      city: string;
      state: string;
      // ... adicionar todas
    };
    placeholders: {
      name: string;
      email: string;
      phone: string;
      cpf_cnpj: string;
      card_number: string;
      card_expiry: string;
      card_cvv: string;
      card_holder_name: string;
      coupon: string;
      zipcode: string;
      street: string;
      number: string;
      complement: string;
      neighborhood: string;
      city: string;
      // ... adicionar todas
    };
    helpers: {
      cpf_required: string;
      phone_format: string;
      card_number_hint: string;
      // ... adicionar todas
    };
  };
  payment: {
    methods: {
      credit_card: string;
      pix: string;
      pix_auto: string;
      boleto: string;
      picpay: string;
      applepay: string;
      googlepay: string;
      openfinance_nubank: string;
    };
    messages: {
      processing: string;
      waiting: string;
      success: string;
      failed: string;
      expired: string;
      // ... adicionar todas
    };
    pix: {
      title: string;
      subtitle: string;
      qr_code_label: string;
      copy_code: string;
      code_copied: string;
      expires_in: string;
      waiting_payment: string;
      // ... adicionar todas
    };
    boleto: {
      title: string;
      subtitle: string;
      barcode_label: string;
      copy_code: string;
      code_copied: string;
      due_date: string;
      // ... adicionar todas
    };
    credit_card: {
      title: string;
      subtitle: string;
      save_card: string;
      installments_label: string;
      // ... adicionar todas
    };
  };
  address: {
    title: string;
    zipcode_label: string;
    zipcode_not_found: string;
    zipcode_searching: string;
    zipcode_found: string;
    street_label: string;
    number_label: string;
    complement_label: string;
    neighborhood_label: string;
    city_label: string;
    state_label: string;
    // ... adicionar todas
  };
  bump: {
    add_product: string;
    selected: string;
    discount_label: string;
    // ... adicionar todas
  };
  success: {
    title: string;
    subtitle: string;
    approved: string;
    rejected: string;
    pending: string;
    // ... adicionar todas
  };
  about: {
    cpf_title: string;
    cpf_description: string;
    pix_security_title: string;
    pix_security_description: string;
    // ... adicionar todas
  };
  in_app_browser: {
    warning_title: string;
    warning_message: string;
    open_in_browser: string;
    // ... adicionar todas
  };
};
```

#### 1.2 Expandir Mensagens JSON

**Referência:** `_docs/cakto-checkout/src/i18n/errorMessages.ts`

```json
// apps/web/src/lib/i18n/messages/pt.json
{
  "checkout": {
    "title": "Finalize sua compra",
    "subtitle": "Preencha os dados abaixo",
    "loading": "Carregando informações do checkout..."
  },
  "common": {
    "continue": "Continuar",
    "retry": "Tentar novamente",
    "save": "Salvar",
    "cancel": "Cancelar",
    "close": "Fechar",
    "yes": "Sim",
    "no": "Não",
    "loading": "Carregando...",
    "checkoutTitle": "Checkout",
    "selectCheckout": "Selecione um checkout válido para continuar."
  },
  "errors": {
    "validation": {
      "required": "Este campo é obrigatório",
      "invalid_email": "Por favor, insira um e-mail válido",
      "invalid_phone": "Por favor, insira um telefone válido",
      "invalid_cpf": "Por favor, insira um CPF válido",
      "invalid_cnpj": "Por favor, insira um CNPJ válido",
      "invalid_card_number": "Número do cartão inválido",
      "invalid_cvv": "CVV inválido",
      "invalid_expiry": "Data de validade inválida",
      "min_length": "Este campo deve ter pelo menos {min} caracteres",
      "max_length": "Este campo deve ter no máximo {max} caracteres",
      "generic": "Por favor, verifique os dados informados"
    },
    "network": {
      "connection_failed": "Falha na conexão. Verifique sua internet",
      "timeout": "A operação demorou mais que o esperado",
      "server_error": "Erro interno do servidor",
      "not_found": "Recurso não encontrado",
      "unauthorized": "Acesso não autorizado",
      "forbidden": "Operação não permitida",
      "generic": "Erro de comunicação com o servidor"
    },
    "payment": {
      "card_declined": "Cartão recusado. Tente outro cartão",
      "insufficient_funds": "Saldo insuficiente",
      "invalid_card": "Dados do cartão inválidos",
      "expired_card": "Cartão vencido",
      "processing_error": "Erro no processamento do pagamento",
      "provider_error": "Erro na operadora do cartão",
      "generic": "Não foi possível processar o pagamento"
    },
    "threeds": {
      "authentication_failed": "Falha na autenticação 3D Secure",
      "challenge_failed": "Desafio 3D Secure não concluído",
      "not_enrolled": "Cartão não habilitado para 3D Secure",
      "unsupported_card": "Cartão não suportado para 3D Secure",
      "generic": "Erro na autenticação 3D Secure"
    },
    "system": {
      "unexpected_error": "Erro inesperado do sistema",
      "service_unavailable": "Serviço temporariamente indisponível",
      "maintenance": "Sistema em manutenção",
      "generic": "Erro interno do sistema"
    },
    "generic": {
      "something_went_wrong": "Algo deu errado",
      "try_again": "Tente novamente",
      "contact_support": "Entre em contato com o suporte"
    }
  },
  "form": {
    "labels": {
      "name": "Nome completo",
      "email": "E-mail",
      "phone": "Telefone",
      "cpf": "CPF",
      "cnpj": "CNPJ",
      "cpf_cnpj": "CPF/CNPJ",
      "card_number": "Número do cartão",
      "card_expiry": "Validade",
      "card_cvv": "CVV",
      "card_holder_name": "Nome no cartão",
      "installments": "Parcelas",
      "coupon": "Cupom de desconto",
      "zipcode": "CEP",
      "street": "Logradouro",
      "number": "Número",
      "complement": "Complemento",
      "neighborhood": "Bairro",
      "city": "Cidade",
      "state": "Estado"
    },
    "placeholders": {
      "name": "Digite seu nome completo",
      "email": "<EMAIL>",
      "phone": "(00) 00000-0000",
      "cpf_cnpj": "000.000.000-00",
      "card_number": "0000 0000 0000 0000",
      "card_expiry": "MM/AA",
      "card_cvv": "000",
      "card_holder_name": "Nome como está no cartão",
      "coupon": "Digite o código do cupom",
      "zipcode": "00000-000",
      "street": "Rua, Avenida, etc.",
      "number": "123",
      "complement": "Apartamento, bloco, etc.",
      "neighborhood": "Nome do bairro",
      "city": "Nome da cidade"
    },
    "helpers": {
      "cpf_required": "Por que pedimos esse dado?",
      "phone_format": "Formato: (DD) 00000-0000",
      "card_number_hint": "Digite os números sem espaço"
    }
  },
  "payment": {
    "methods": {
      "credit_card": "Cartão de Crédito",
      "pix": "PIX",
      "pix_auto": "PIX Automático",
      "boleto": "Boleto",
      "picpay": "PicPay",
      "applepay": "Apple Pay",
      "googlepay": "Google Pay",
      "openfinance_nubank": "Nubank"
    },
    "messages": {
      "processing": "Processando pagamento...",
      "waiting": "Aguardando pagamento",
      "success": "Pagamento confirmado!",
      "failed": "Pagamento recusado",
      "expired": "Pagamento expirado"
    },
    "pix": {
      "title": "PIX",
      "subtitle": "Escaneie o QR Code ou copie o código",
      "qr_code_label": "QR Code PIX",
      "copy_code": "Copiar código PIX",
      "code_copied": "Código copiado!",
      "expires_in": "Expira em",
      "waiting_payment": "Aguardando confirmação do pagamento..."
    },
    "boleto": {
      "title": "Boleto Bancário",
      "subtitle": "Copie o código de barras ou baixe o boleto",
      "barcode_label": "Código de barras",
      "copy_code": "Copiar código",
      "code_copied": "Código copiado!",
      "due_date": "Vencimento",
      "download": "Baixar boleto"
    },
    "credit_card": {
      "title": "Cartão de Crédito",
      "subtitle": "Preencha os dados do seu cartão",
      "save_card": "Salvar cartão para futuras compras",
      "installments_label": "Número de parcelas"
    }
  },
  "address": {
    "title": "Endereço",
    "delivery_title": "Endereço de entrega",
    "zipcode_label": "CEP",
    "zipcode_not_found": "CEP não encontrado. Preencha os campos manualmente.",
    "zipcode_searching": "Buscando endereço...",
    "zipcode_found": "Endereço encontrado!",
    "street_label": "Logradouro",
    "number_label": "Número",
    "complement_label": "Complemento (opcional)",
    "neighborhood_label": "Bairro",
    "city_label": "Cidade",
    "state_label": "Estado",
    "shipping_method": "Método de Envio",
    "shipping_options": "Opções de envio"
  },
  "bump": {
    "add_product": "Adicionar Produto",
    "selected": "Selecionado",
    "discount_label": "OFF"
  },
  "success": {
    "title": "Pagamento aprovado!",
    "subtitle": "Seu pedido foi confirmado",
    "order_number": "Número do pedido",
    "approved": "Aprovado",
    "rejected": "Recusado",
    "pending": "Pendente",
    "redirecting": "Redirecionando..."
  },
  "about": {
    "cpf_title": "Sobre CPF/CNPJ",
    "cpf_description": "Esse dado é necessário para garantir a segurança da sua compra e cadastro em nossa plataforma, essa informação também pode ser usada para emissão de Nota Fiscal.",
    "pix_security_title": "Segurança PIX",
    "pix_security_description": "O PIX é um método de pagamento seguro e instantâneo do Banco Central do Brasil."
  },
  "in_app_browser": {
    "warning_title": "Atenção",
    "warning_message": "Para melhor experiência e segurança, recomendamos abrir esta página no seu navegador.",
    "open_in_browser": "Abrir no navegador"
  }
}
```

**IMPORTANTE:** Replicar TODAS as mensagens para `en.json` e `es.json` traduzidas.

#### 1.3 Criar Hook useTranslation

```typescript
// apps/web/src/hooks/useTranslation.ts
"use client";

import { useCallback } from "react";
import { useIntlContext } from "@/contexts/intl-context";
import type { Messages } from "@/types/i18n";

type MessageKey = string; // Ex: "form.labels.name" or "errors.validation.required"

export function useTranslation() {
  const { messages, locale } = useIntlContext();

  const t = useCallback(
    (key: MessageKey, params?: Record<string, string | number>): string => {
      // Split key by dots: "form.labels.name" => ["form", "labels", "name"]
      const keys = key.split(".");
      
      // Navigate nested object
      let value: any = messages;
      for (const k of keys) {
        if (value && typeof value === "object" && k in value) {
          value = value[k];
        } else {
          console.warn(`Translation key not found: ${key}`);
          return key; // Fallback to key itself
        }
      }

      // If value is not string, return key
      if (typeof value !== "string") {
        console.warn(`Translation value is not a string: ${key}`);
        return key;
      }

      // Interpolate params: "Hello {name}" with {name: "John"} => "Hello John"
      if (params) {
        return Object.entries(params).reduce((str, [paramKey, paramValue]) => {
          return str.replace(new RegExp(`\\{${paramKey}\\}`, "g"), String(paramValue));
        }, value);
      }

      return value;
    },
    [messages]
  );

  return { t, locale };
}
```

#### 1.4 Criar Helpers de Interpolação

```typescript
// apps/web/src/lib/utils/interpolate.ts
export function interpolate(
  template: string,
  params: Record<string, string | number>
): string {
  return Object.entries(params).reduce((str, [key, value]) => {
    return str.replace(new RegExp(`\\{${key}\\}`, "g"), String(value));
  }, template);
}
```

---

### Tarefa 2: Header com Seletor de Locale

#### 2.1 Criar CheckoutHeader

```tsx
// apps/web/src/components/checkout/checkout-header.tsx
"use client";

import { LocaleSelector } from "./locale-selector";
import { SecurityIcon } from "@/components/icons";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";

export function CheckoutHeader() {
  const settings = useSettings();
  const { t } = useTranslation();

  return (
    <header
      className="sticky top-0 z-50 backdrop-blur-sm border-b"
      style={{
        backgroundColor: `${settings.backgroundColor}ee`,
        borderColor: settings.text.color.secondary + "40",
      }}
    >
      <div className="container mx-auto px-4">
        <div className="flex h-14 items-center justify-between">
          {/* Logo / Title */}
          <div className="flex items-center gap-2">
            <SecurityIcon className="w-5 h-5" style={{ color: settings.text.color.primary }} />
            <span
              className="font-semibold text-sm hidden sm:inline"
              style={{ color: settings.text.color.primary }}
            >
              {t("common.checkoutTitle")}
            </span>
          </div>

          {/* Locale Selector */}
          <LocaleSelector />
        </div>
      </div>
    </header>
  );
}
```

#### 2.2 Criar LocaleSelector

```tsx
// apps/web/src/components/checkout/locale-selector.tsx
"use client";

import { useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { useLocale } from "@/contexts/intl-context";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDownIcon } from "@heroicons/react/20/solid";

const LOCALES = [
  { code: "pt", label: "Português", flag: "🇧🇷", country: "Brasil" },
  { code: "en", label: "English", flag: "🇺🇸", country: "United States" },
  { code: "es", label: "Español", flag: "🇪🇸", country: "España" },
] as const;

export function LocaleSelector() {
  const locale = useLocale();
  const pathname = usePathname();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);

  const currentLocale = LOCALES.find((l) => l.code === locale) || LOCALES[0];

  const handleChange = (newLocale: string) => {
    // Replace locale in pathname: /pt/7rohg3i => /en/7rohg3i
    const segments = pathname.split("/");
    segments[1] = newLocale;
    const newPath = segments.join("/");

    // Set cookie (client-side)
    document.cookie = `NEXT_LOCALE=${newLocale}; path=/; max-age=31536000; SameSite=Lax`;

    // Redirect
    router.push(newPath);
    router.refresh();
    setIsOpen(false);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger className="flex items-center gap-2 px-3 py-1.5 rounded-md hover:bg-gray-100/10 transition-colors">
        <span className="text-xl">{currentLocale.flag}</span>
        <span className="text-sm font-medium hidden sm:inline">{currentLocale.label}</span>
        <ChevronDownIcon className="w-4 h-4 opacity-50" />
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end" className="w-48">
        {LOCALES.map((l) => (
          <DropdownMenuItem
            key={l.code}
            onClick={() => handleChange(l.code)}
            className="flex items-center gap-3 cursor-pointer"
          >
            <span className="text-xl">{l.flag}</span>
            <div className="flex flex-col">
              <span className="font-medium">{l.label}</span>
              <span className="text-xs opacity-60">{l.country}</span>
            </div>
            {l.code === locale && (
              <span className="ml-auto text-green-500">✓</span>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
```

#### 2.3 Integrar no Layout

```tsx
// apps/web/src/app/[locale]/[id]/layout.tsx (CRIAR NOVO)
import { CheckoutHeader } from "@/components/checkout/checkout-header";

export default function CheckoutLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen flex flex-col">
      <CheckoutHeader />
      <main className="flex-1">{children}</main>
    </div>
  );
}
```

---

### Tarefa 3: Migrar Componentes Essenciais

#### 3.1 Migrar AddressForm

**Referência:** `_docs/cakto-checkout/src/components/checkout/AddressForm.tsx`

```tsx
// apps/web/src/components/checkout/address-form.tsx
"use client";

import { useEffect, useState } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { MapPinIcon, CheckIcon, ExclamationTriangleIcon } from "@heroicons/react/20/solid";
import { useCepSearch } from "@/hooks/useCepSearch";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";
import TextField from "@/components/ui/text-field";
import Select from "@/components/ui/select";
import { Spinner } from "@/components/ui/spinner"; // CRIAR se não existir
import { getBranding } from "@/lib/env";

const BRAZILIAN_STATES = [
  { value: "AC", label: "Acre" },
  { value: "AL", label: "Alagoas" },
  // ... todos os estados
] as const;

export type AddressFormValues = {
  zipCode: string;
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
};

interface AddressFormProps {
  className?: string;
}

export function AddressForm({ className = "" }: AddressFormProps) {
  const settings = useSettings();
  const { t } = useTranslation();
  const { searchCep, isLoadingCep, handleManualChange, resetManualChanges } = useCepSearch();
  const { setValue } = useFormContext();
  
  const zipCode = useWatch({ name: "zipCode" });
  const city = useWatch({ name: "city" });
  const state = useWatch({ name: "state" });
  
  const [cepFound, setCepFound] = useState(false);
  const [cepError, setCepError] = useState(false);
  const [lastSearchedCep, setLastSearchedCep] = useState("");

  const isNommi = getBranding() === "nommi";

  // Effect to handle CEP search
  useEffect(() => {
    if (zipCode && zipCode.replace(/\D/g, "").length === 8) {
      const normalizedCep = zipCode.replace(/\D/g, "");
      if (normalizedCep !== lastSearchedCep) {
        setLastSearchedCep(normalizedCep);
        setCepError(false);
        setCepFound(false);
        resetManualChanges();
        searchCep(zipCode);
      }
    } else if (zipCode && zipCode.replace(/\D/g, "").length < 8) {
      setCepFound(false);
      setCepError(false);
      setLastSearchedCep("");
    }
  }, [zipCode, searchCep, resetManualChanges, lastSearchedCep]);

  // Effect to detect when CEP was found or failed
  useEffect(() => {
    if (!isLoadingCep && lastSearchedCep && zipCode?.replace(/\D/g, "") === lastSearchedCep) {
      if (city && state) {
        setCepFound(true);
        setCepError(false);
      } else if (zipCode?.replace(/\D/g, "").length === 8) {
        setCepError(true);
        setCepFound(false);
      }
    }
  }, [isLoadingCep, city, state, lastSearchedCep, zipCode]);

  const hasValidCep = zipCode && zipCode.replace(/\D/g, "").length === 8;
  const showAddressFields = hasValidCep || zipCode?.length > 0;

  return (
    <div className={`w-full ${className}`}>
      {/* Title */}
      <div className="flex items-center mb-4 font-bold">
        {!isNommi && (
          <MapPinIcon
            style={{ color: settings.text?.color.primary }}
            width={24}
            height={24}
            className="mr-2"
          />
        )}
        <span style={{ color: settings.text?.color.primary }} className="text-lg">
          {isNommi ? t("address.delivery_title") : t("address.title")}
        </span>
      </div>

      <div className="flex flex-col gap-4">
        {/* CEP */}
        <div className="relative">
          <TextField
            name="zipCode"
            size="sm"
            label={t("address.zipcode_label")}
            placeholder={t("form.placeholders.zipcode")}
            mask="99999-999"
            inputMode="numeric"
          />
          <div className="absolute right-3 top-8 flex items-center">
            {isLoadingCep && <Spinner className="w-4 h-4" />}
            {cepFound && !isLoadingCep && (
              <CheckIcon className="w-4 h-4 text-green-500" title={t("address.zipcode_found")} />
            )}
            {cepError && !isLoadingCep && hasValidCep && (
              <ExclamationTriangleIcon className="w-4 h-4 text-yellow-500" />
            )}
          </div>
        </div>

        {/* Address Fields */}
        {showAddressFields && (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="sm:col-span-2">
                <TextField
                  name="street"
                  size="sm"
                  label={t("address.street_label")}
                  placeholder={t("form.placeholders.street")}
                  normalize
                  onChange={() => handleManualChange("street")}
                />
              </div>
              <div>
                <TextField
                  name="number"
                  size="sm"
                  label={t("address.number_label")}
                  placeholder={t("form.placeholders.number")}
                  normalize
                />
              </div>
            </div>

            <TextField
              name="complement"
              size="sm"
              label={t("address.complement_label")}
              placeholder={t("form.placeholders.complement")}
              normalize
            />

            <TextField
              name="neighborhood"
              size="sm"
              label={t("address.neighborhood_label")}
              placeholder={t("form.placeholders.neighborhood")}
              normalize
              onChange={() => handleManualChange("neighborhood")}
            />

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <TextField
                name="city"
                size="sm"
                label={t("address.city_label")}
                placeholder={t("form.placeholders.city")}
                normalize
                onChange={() => handleManualChange("city")}
              />
              <Select
                name="state"
                size="sm"
                label={t("address.state_label")}
                placeholder={t("form.placeholders.state")}
                options={BRAZILIAN_STATES}
                onChange={(e) => {
                  setValue("state", e.target.value);
                  handleManualChange("state");
                }}
              />
            </div>

            {cepError && hasValidCep && (
              <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <ExclamationTriangleIcon className="w-4 h-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">
                  {t("address.zipcode_not_found")}
                </span>
              </div>
            )}
          </>
        )}
      </div>

      {/* Nommi Shipping */}
      {isNommi && (
        <>
          <div className="flex items-center font-bold mt-6">
            <span style={{ color: settings.text?.color.primary }} className="text-lg">
              {t("address.shipping_method")}
            </span>
          </div>
          <div className="flex flex-col gap-4">
            <Select
              name="freight"
              size="sm"
              label={t("address.shipping_options")}
              placeholder={t("form.placeholders.shipping")}
              options={[
                {
                  value: "15",
                  label: "Frete disponível: SEDEX – R$ 15,00 – entrega em até 7 dias úteis",
                },
                {
                  value: "10",
                  label: "Frete disponível: Melhor Envio – R$ 10,00 – entrega em até 5 dias úteis",
                },
              ]}
              onChange={(e) => {
                setValue("freight", e.target.value);
              }}
            />
          </div>
        </>
      )}
    </div>
  );
}
```

#### 3.2 Migrar BumpItem

**Referência:** `_docs/cakto-checkout/src/components/checkout/Bumps/BumpItem.tsx`

```tsx
// apps/web/src/components/checkout/bump-item.tsx
"use client";

import { useEffect, useMemo, useState } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { useCheckout } from "@/contexts";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";
import { useCouponDiscount } from "@/hooks/useCouponDiscount"; // MIGRAR
import { formatPrice } from "@/lib/utils/format";
import { getRecurrencePeriodLabel } from "@/lib/utils/payment";
import { CircleCheckIcon } from "@/components/icons"; // CRIAR ou importar
import { InstallmentsSelector } from "@/components/payments/InstallmentsSelector";

type Props = {
  index: number;
  defaultChecked?: boolean;
};

export function BumpItem({ index, defaultChecked = false }: Props) {
  const [isChecked, setIsChecked] = useState(defaultChecked);
  const { setValue } = useFormContext();
  const settings = useSettings();
  const { t } = useTranslation();
  const { canBePaidInInstallments, hasAnySubscription } = useCheckout();
  const { applyCoupon } = useCouponDiscount();
  const form = useFormContext();

  const bump = useWatch({
    control: form.control,
    name: `bumps.${index}`,
  });

  const originalPrice = bump.offer.price;
  const finalPrice = applyCoupon(originalPrice, true);

  const discountPercentage = useMemo(() => {
    if (bump.referencePrice) {
      return ((bump.referencePrice - originalPrice) / bump.referencePrice) * 100;
    }
    return 0;
  }, [bump.referencePrice, originalPrice]);

  const handleCheckedBump = () => {
    const newCheckedValue = !isChecked;
    setIsChecked(newCheckedValue);
    setValue(`bumps.${index}.checked`, newCheckedValue);
  };

  useEffect(() => {
    if (!canBePaidInInstallments) {
      setValue(`bumps.${index}.installments`, 1);
    }
  }, [canBePaidInInstallments, index, setValue]);

  const getFormattedFinalPrice = () => {
    if (bump.offer.type === "subscription") {
      return `${formatPrice(finalPrice)} / ${getRecurrencePeriodLabel(
        bump.offer.recurrence_period || 30
      )}`;
    }
    return formatPrice(finalPrice);
  };

  return (
    <div
      style={{
        backgroundColor: isChecked
          ? settings.box.selected.background.color
          : settings.box.unselected.background.color,
        border: `2px ${isChecked ? "solid" : "dashed"} ${
          isChecked
            ? settings.box.selected.header.background.color
            : settings.box.unselected.header.background.color
        }`,
      }}
      className="cursor-pointer ease-in-out duration-150 my-1 overflow-hidden rounded-md shadow-md transition w-full"
      onClick={handleCheckedBump}
    >
      {/* Header */}
      <div
        style={{
          backgroundColor: isChecked
            ? (settings.box.selected.header.background.color as string)
            : (settings.box.unselected.header.background.color as string),
        }}
        className="ease-in-out flex justify-between py-2 px-4 transition"
      >
        <div className="flex-grow">
          <h1
            style={{
              color: isChecked
                ? settings.box.selected.header.text.color.primary
                : settings.box.unselected.header.text.color.primary,
            }}
            className="text-lg xs:text-sm text-white font-semibold line-clamp-3 overflow-hidden text-ellipsis"
          >
            {bump.cta.trim()}
          </h1>
        </div>
        <div className="flex items-center shrink-0 basis-auto">
          {isChecked && (
            <span
              style={{
                color: isChecked
                  ? settings.box.selected.header.text.color.primary
                  : settings.box.unselected.header.text.color.primary,
              }}
              className="text-white mr-1 xs:hidden"
            >
              {t("bump.selected")}
            </span>
          )}
          <CircleCheckIcon
            className={`h-4 w-4 ${isChecked ? "text-[#38C4AF]" : "text-slate-500"}`}
          />
        </div>
      </div>

      {/* Body */}
      <div className="flex p-4 xs:p-3">
        <div className="flex flex-row gap-2 items-center w-full xs:flex-col">
          <div className="flex gap-2 w-full">
            {/* Image */}
            {bump.image && bump.showImage && (
              <div className="w-[80px] h-[80px] xs:w-[80px] xs:h-[80px] flex-shrink-0">
                <img
                  src={bump.image}
                  alt="Imagem do produto"
                  className="w-full h-full rounded-md object-cover"
                />
              </div>
            )}

            {/* Content */}
            <div className="flex flex-col justify-center w-full gap-1">
              <div className="flex justify-between">
                <div className="flex-1">
                  <p
                    style={{
                      color: isChecked
                        ? settings.box.selected.text.color.primary
                        : settings.box.unselected.text.color.primary,
                    }}
                    className="font-bold text-white text-sm line-clamp-3 overflow-hidden text-ellipsis"
                  >
                    {bump.title.trim() || "Nome do seu produto"}
                  </p>
                </div>
                <div className="min-w-[100px] flex justify-center items-center gap-2 xs:flex-col-reverse">
                  {bump.referencePrice !== null && (
                    <>
                      <span
                        style={{
                          color: isChecked
                            ? settings.box.selected.text.color.secondary
                            : settings.box.unselected.text.color.secondary,
                        }}
                        className="text-gray-400 line-through mr-1 xs:text-xs"
                      >
                        {formatPrice(bump.referencePrice)}
                      </span>
                      <span
                        style={{
                          color: settings.box.selected.header.text.color.primary,
                        }}
                        className="bg-[#0F7864] text-white text-xs font-bold px-2 py-1 rounded-full xs:px-1 xs:font-semibold"
                      >
                        {Math.round(discountPercentage)}% {t("bump.discount_label")}
                      </span>
                    </>
                  )}
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
                <div className="flex-1">
                  <p
                    style={{
                      color: isChecked
                        ? settings.box.selected.text.color.secondary
                        : settings.box.unselected.text.color.secondary,
                    }}
                    className="text-white text-sm overflow-hidden"
                  >
                    {bump.description.trim() || "Nome do seu produto"}
                  </p>
                </div>
                <div className="min-w-[100px] relative sm:text-right">
                  {hasAnySubscription && canBePaidInInstallments ? (
                    <InstallmentsSelector
                      itsNew={false}
                      offerPaid={false}
                      hideLabel
                      fieldName={`bumps.${index}.installments`}
                      calculatedInstallmentsFieldName={`bumps.${index}.calculatedInstallments`}
                      offerId={bump.offer.id}
                      offerType={bump.offer.type}
                      price={finalPrice}
                      recurrencePeriod={bump.offer.recurrence_period || 30}
                      autoCalc={false}
                    />
                  ) : (
                    <span
                      style={{
                        color: settings.text.color.active,
                      }}
                      className="text-base font-bold px-2 py-1 rounded-full xs:px-0 xs:font-semibold"
                    >
                      {getFormattedFinalPrice()}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div
        style={{
          width: "100%",
          height: 30,
          background: isChecked
            ? (settings.box.selected.header.background.color as string)
            : (settings.box.unselected.header.background.color as string),
          display: "flex",
          alignItems: "center",
          padding: "0 10px",
          cursor: "pointer",
        }}
        onClick={(e) => {
          e.stopPropagation();
          setIsChecked(!isChecked);
        }}
      >
        <input
          type="checkbox"
          checked={isChecked}
          onChange={() => setIsChecked(!isChecked)}
          onClick={(e) => e.stopPropagation()}
          style={{
            marginRight: "8px",
            cursor: "pointer",
            accentColor: isChecked ? "#38C4AF" : "auto",
          }}
        />
        <span
          style={{
            userSelect: "none",
            fontSize: "14px",
            color: isChecked
              ? settings.box.selected.header.text.color.primary
              : settings.box.unselected.header.text.color.primary,
            fontWeight: "bold",
          }}
        >
          {t("bump.add_product")}
        </span>
      </div>
    </div>
  );
}
```

#### 3.3 Migrar AboutCpf

**Referência:** `_docs/cakto-checkout/src/components/info/AboutCpf.tsx`

```tsx
// apps/web/src/components/info/about-cpf.tsx
"use client";

import { useState } from "react";
import { createPortal } from "react-dom";
import useSettings from "@/hooks/useSettings";
import { useTranslation } from "@/hooks/useTranslation";
import { getOppositeColor, applyTransparency } from "@/lib/utils/colors";
import { getBranding } from "@/lib/env";
import { LinkButton } from "@/components/common/link-button"; // MIGRAR

export function AboutCpf() {
  const [showModal, setShowModal] = useState(false);
  const { t } = useTranslation();

  return (
    <>
      <LinkButton
        onOpen={() => setShowModal(true)}
        spanClassName="-mt-1.5 -mb-3 text-xs leading-4 LinkButton text-[#36B37E]"
      >
        {t("form.helpers.cpf_required")}
      </LinkButton>

      {showModal &&
        typeof window !== "undefined" &&
        createPortal(<ModalContent onClose={() => setShowModal(false)} />, document.body)}
    </>
  );
}

function ModalContent({ onClose }: { onClose: () => void }) {
  const settings = useSettings();
  const { t } = useTranslation();
  const isNommi = getBranding() === "nommi";

  return (
    <div
      style={{
        backgroundColor: applyTransparency(getOppositeColor(settings.backgroundColor), 0.5),
      }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-opacity-50"
      onClick={onClose}
    >
      <div
        style={{
          backgroundColor: settings.form.background.color,
        }}
        className="bg-gray-800 rounded-lg shadow-lg p-6 max-w-md mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex flex-col">
          <h2
            style={{
              color: settings.text.color.primary,
            }}
            className="text-lg font-semibold text-white mb-4"
          >
            {t("about.cpf_title")}
          </h2>
          <p
            style={{
              color: settings.text.color.secondary,
            }}
            className="text-sm text-gray-400 mb-6"
          >
            {t("about.cpf_description")}
          </p>
          <button
            onClick={onClose}
            style={{
              backgroundColor: isNommi ? "#2886B9" : settings.payButton.color,
              color: settings.payButton.text.color,
              opacity: 1,
            }}
            className="px-6 py-2 bg-[#0F7864] text-white rounded-md hover:bg-[#0b6856] hover:shadow-button-hover transition-colors w-full"
          >
            {t("common.close")}
          </button>
        </div>
      </div>
    </div>
  );
}
```

#### 3.4 Outros Componentes Comuns

**Criar:**
- `LinkButton` - Botão tipo link
- `EmailAutoComplete` - Autocomplete de email
- `TextFieldPhoneNumber` - Campo de telefone
- `ServiceFeeTooltip` - Tooltip de taxa
- `Toast` - Sistema de notificações

**Instruções:** Seguir o mesmo padrão de migração, sempre usando:
- `"use client"` no topo
- `useTranslation()` para textos
- `useSettings()` para estilos
- Path aliases `@/`

---

### Tarefa 4: In-App Browser Support

#### 4.1 Criar Detector de Browser

```typescript
// apps/web/src/lib/utils/browser.ts
export type InAppBrowserType = "tiktok" | "instagram" | "facebook" | null;

export function getInAppBrowser(): InAppBrowserType {
  if (typeof window === "undefined") return null;

  const ua = navigator.userAgent || "";

  if (/TikTok/i.test(ua)) return "tiktok";
  if (/Instagram/i.test(ua)) return "instagram";
  if (/FBAN|FBAV/i.test(ua)) return "facebook";

  return null;
}

export function isInAppBrowser(): boolean {
  return getInAppBrowser() !== null;
}

export function shouldShowOpenInBrowserWarning(): boolean {
  const browser = getInAppBrowser();
  // TikTok e Instagram têm mais problemas
  return browser === "tiktok" || browser === "instagram";
}

export function getOpenInBrowserUrl(currentUrl: string): string {
  const browser = getInAppBrowser();

  if (browser === "tiktok") {
    // TikTok: usar intent do Android ou fallback
    return currentUrl;
  }

  if (browser === "instagram") {
    // Instagram: usar intent do Android ou fallback
    return currentUrl;
  }

  return currentUrl;
}
```

#### 4.2 Criar Hook useInAppBrowser

```typescript
// apps/web/src/hooks/useInAppBrowser.ts
"use client";

import { useEffect, useState } from "react";
import { getInAppBrowser, shouldShowOpenInBrowserWarning, type InAppBrowserType } from "@/lib/utils/browser";

export function useInAppBrowser() {
  const [browser, setBrowser] = useState<InAppBrowserType>(null);
  const [showWarning, setShowWarning] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const detected = getInAppBrowser();
      setBrowser(detected);
      setShowWarning(shouldShowOpenInBrowserWarning());
    }
  }, []);

  return {
    browser,
    isInAppBrowser: browser !== null,
    showWarning,
    dismissWarning: () => setShowWarning(false),
  };
}
```

#### 4.3 Criar Componente InAppBrowserWarning

```tsx
// apps/web/src/components/checkout/in-app-browser-warning.tsx
"use client";

import { XMarkIcon, ArrowTopRightOnSquareIcon } from "@heroicons/react/20/solid";
import { useInAppBrowser } from "@/hooks/useInAppBrowser";
import { useTranslation } from "@/hooks/useTranslation";

export function InAppBrowserWarning() {
  const { showWarning, dismissWarning } = useInAppBrowser();
  const { t } = useTranslation();

  if (!showWarning) return null;

  const handleOpenInBrowser = () => {
    if (typeof window === "undefined") return;
    
    // Tentar abrir no navegador externo
    // Para iOS Safari:
    window.location.href = window.location.href.replace(/^https?:/, "x-safari-https:");
    
    // Fallback: copiar URL
    setTimeout(() => {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(window.location.href);
        alert("URL copiada! Cole no navegador para continuar.");
      }
    }, 1000);
  };

  return (
    <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-3">
      <div className="flex items-start gap-3 max-w-screen-lg mx-auto">
        <div className="flex-shrink-0 text-yellow-600">
          <ArrowTopRightOnSquareIcon className="w-5 h-5" />
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-yellow-800">
            {t("in_app_browser.warning_title")}
          </p>
          <p className="text-sm text-yellow-700 mt-1">
            {t("in_app_browser.warning_message")}
          </p>
          <button
            onClick={handleOpenInBrowser}
            className="mt-2 text-sm font-medium text-yellow-800 underline hover:no-underline"
          >
            {t("in_app_browser.open_in_browser")} →
          </button>
        </div>
        <button
          onClick={dismissWarning}
          className="flex-shrink-0 text-yellow-600 hover:text-yellow-800"
        >
          <XMarkIcon className="w-5 h-5" />
        </button>
      </div>
    </div>
  );
}
```

#### 4.4 Integrar no Checkout

```tsx
// apps/web/src/components/checkout/checkout-form.tsx
import { InAppBrowserWarning } from "./in-app-browser-warning";

export function CheckoutForm() {
  // ... código existente

  return (
    <>
      <InAppBrowserWarning />
      {/* resto do checkout */}
    </>
  );
}
```

---

### Tarefa 5: Otimizações SSR/SSG

#### 5.1 Metadata Dinâmico

```tsx
// apps/web/src/app/[locale]/[id]/page.tsx
import { getCheckoutData } from "@/lib/api/checkout";
import type { Metadata } from "next";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ id: string; locale: string }>;
}): Promise<Metadata> {
  const { id } = await params;

  try {
    const checkoutData = await getCheckoutData(id);
    const product = checkoutData.offer?.product;

    if (!product) {
      return {
        title: "Checkout",
        description: "Complete your purchase",
      };
    }

    return {
      title: `${product.name} - Checkout`,
      description: product.description || "Complete your purchase securely",
      openGraph: {
        title: product.name,
        description: product.description || "",
        images: product.image ? [{ url: product.image }] : [],
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title: product.name,
        description: product.description || "",
        images: product.image ? [product.image] : [],
      },
      robots: {
        index: false, // Não indexar páginas de checkout
        follow: false,
      },
    };
  } catch (error) {
    return {
      title: "Checkout",
      description: "Complete your purchase",
    };
  }
}
```

#### 5.2 Server Components

```tsx
// apps/web/src/components/checkout/product-section.tsx
// REMOVER "use client" - será Server Component

import Image from "next/image";
import { formatPrice } from "@/lib/utils/format";
import type { ProductData } from "@/types";

export function ProductSection({ product }: { product: ProductData }) {
  return (
    <div className="mb-6">
      {product.image && (
        <Image
          src={product.image}
          alt={product.name}
          width={400}
          height={300}
          className="rounded-lg"
          priority
        />
      )}
      <h1 className="text-2xl font-bold mt-4">{product.name}</h1>
      <p className="text-gray-600 mt-2">{product.description}</p>
      <p className="text-3xl font-bold mt-4">{formatPrice(product.price)}</p>
    </div>
  );
}
```

#### 5.3 Suspense Boundaries

```tsx
// apps/web/src/app/[locale]/[id]/page.tsx
import { Suspense } from "react";
import { ProductSection } from "@/components/checkout/product-section";
import { ProductSkeleton } from "@/components/checkout/product-skeleton"; // CRIAR

export default async function CheckoutPage({ params }: CheckoutPageProps) {
  const { id } = await params;
  const checkoutData = await getCheckoutData(id);

  return (
    <div>
      <Suspense fallback={<ProductSkeleton />}>
        <ProductSection product={checkoutData.offer.product} />
      </Suspense>

      <Suspense fallback={<FormSkeleton />}>
        <CheckoutClient initialData={checkoutData} checkoutId={id} />
      </Suspense>
    </div>
  );
}
```

---

### Tarefa 6: Code Splitting e Performance

#### 6.1 Dynamic Imports

```tsx
// apps/web/src/components/payments/WaitingPayment.tsx
import dynamic from "next/dynamic";
import { Spinner } from "@/components/ui/spinner";

// Lazy load QR Code components
const PixPayment = dynamic(() => import("./PixPayment"), {
  loading: () => <Spinner />,
  ssr: false,
});

const BoletoPayment = dynamic(() => import("./BoletoPayment"), {
  loading: () => <Spinner />,
  ssr: false,
});

const PicPayPayment = dynamic(() => import("./PicPayPayment"), {
  loading: () => <Spinner />,
  ssr: false,
});
```

#### 6.2 Lazy Load Confetti

```tsx
// apps/web/src/components/payments/SuccessPayment.tsx
import dynamic from "next/dynamic";

const Confetti = dynamic(() => import("react-confetti"), {
  ssr: false,
});

export function SuccessPayment() {
  // ... código

  return (
    <>
      {showConfetti && (
        <Confetti
          width={width}
          height={height}
          recycle={false}
          numberOfPieces={500}
        />
      )}
      {/* resto */}
    </>
  );
}
```

#### 6.3 Image Optimization

```tsx
// Substituir todos os <img> por <Image>
import Image from "next/image";

// ANTES
<img src={bump.image} alt="Produto" className="w-20 h-20" />

// DEPOIS
<Image
  src={bump.image}
  alt="Produto"
  width={80}
  height={80}
  className="rounded-md"
/>
```

**Configurar domains no next.config.ts:**
```typescript
// apps/web/next.config.ts
const config: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**.cakto.com.br",
      },
      {
        protocol: "https",
        hostname: "**.nommi.com.br",
      },
      // Adicionar outros domínios conforme necessário
    ],
  },
};
```

---

## 🔍 Checklist de Validação

Após completar as tarefas, validar:

### i18n
- [ ] Todas as mensagens em pt.json, en.json, es.json
- [ ] Hook `useTranslation()` funcionando
- [ ] TODOS os componentes usando `t()` ao invés de texto hardcoded
- [ ] Interpolação de variáveis funcionando (`t("key", { param: value })`)
- [ ] Fallback para chave se tradução não existir

### Header e Locale
- [ ] Header aparecendo no checkout
- [ ] Dropdown de locale funcionando
- [ ] Troca de idioma persistindo no cookie
- [ ] Redirecionamento correto (/pt/id → /en/id)
- [ ] Visual responsivo (mobile + desktop)

### Componentes
- [ ] AddressForm renderizando
- [ ] Busca de CEP funcionando
- [ ] BumpItem renderizando
- [ ] Checkbox de bump funcionando
- [ ] AboutCpf modal funcionando
- [ ] Todos os componentes com i18n

### In-App Browser
- [ ] Detecção funcionando (console.log)
- [ ] Warning aparecendo no TikTok/Instagram
- [ ] Botão "Abrir no navegador" funcionando
- [ ] Dismiss do warning funcionando

### Performance
- [ ] Bundle size inicial < 500KB
- [ ] QR Code carrega apenas quando necessário
- [ ] Confetti carrega apenas no sucesso
- [ ] Imagens otimizadas com Next/Image
- [ ] Lighthouse score > 85

### SSR/SSG
- [ ] Metadata dinâmico por produto
- [ ] Open Graph tags corretas
- [ ] Twitter Card funcionando
- [ ] Componentes estáticos como Server Components
- [ ] Suspense boundaries implementados

---

## 🚀 Como Executar

```bash
# Instalar dependências (se necessário)
pnpm install

# Rodar em desenvolvimento
pnpm dev:web

# Testar checkout
http://localhost:3001/pt/7rohg3i
http://localhost:3001/en/7rohg3i
http://localhost:3001/es/7rohg3i

# Build para produção
pnpm --filter web run build

# Analisar bundle
pnpm --filter web run build --analyze
```

---

## 📚 Referências

### Documentação
- [Next.js i18n](https://nextjs.org/docs/app/building-your-application/routing/internationalization)
- [Next.js Metadata](https://nextjs.org/docs/app/building-your-application/optimizing/metadata)
- [Next.js Image Optimization](https://nextjs.org/docs/app/building-your-application/optimizing/images)
- [React 19 Migration](https://react.dev/blog/2024/04/25/react-19)

### Arquivos Importantes
- `.cursorrules` - Regras do projeto
- `STATUS_MIGRACAO_FASE_5.md` - Status anterior
- `ANALISE_GAPS_E_MELHORIAS.md` - Análise completa
- `_docs/cakto-checkout/` - Projeto original (referência)

---

## ⚠️ Regras Importantes

### SEMPRE FAZER:
1. ✅ Usar `"use client"` em componentes interativos
2. ✅ Usar `useTranslation()` para TODOS os textos
3. ✅ Usar path aliases `@/` ao invés de imports relativos
4. ✅ Verificar SSR com `typeof window !== "undefined"`
5. ✅ Usar `useCheckout()` hook (NUNCA `useContext` direto)
6. ✅ Usar helpers de `lib/env.ts` (NUNCA `process.env` direto)
7. ✅ Manter padrões de código do projeto original
8. ✅ Testar em pt, en, es
9. ✅ Validar no navegador antes de finalizar

### NUNCA FAZER:
1. ❌ Texto hardcoded em português (usar i18n)
2. ❌ `import.meta.env` (não existe no Next.js)
3. ❌ `useContext(CheckoutContext)` direto (usar `useCheckout()`)
4. ❌ Imports relativos longos (`../../..`)
5. ❌ Libs incompatíveis com React 19 (`react-input-mask`, `@headlessui/react` v1.7)
6. ❌ `window` ou `navigator` sem verificação SSR
7. ❌ Esquecer `"use client"` em componentes com hooks/state
8. ❌ Criar componentes novos se já existirem no projeto

---

## 🎯 Objetivo Final

Ao concluir esta fase, o checkout deve:

1. ✅ Funcionar perfeitamente em **3 idiomas** (pt, es, en)
2. ✅ Ter **header com seletor de locale** funcionando
3. ✅ Todos os **componentes essenciais migrados**
4. ✅ Funcionar bem em **in-app browsers** (TikTok, Instagram)
5. ✅ Ter **performance otimizada** (bundle, images, code splitting)
6. ✅ Aproveitar **SSR/SSG** do Next.js
7. ✅ Ter **metadata dinâmico** por produto
8. ✅ **Zero texto hardcoded** em português
9. ✅ **Lighthouse score > 85**
10. ✅ **100% compatível com React 19**

---

**BOA SORTE! 🚀**

Se tiver dúvidas, consulte:
- `.cursorrules` (regras do projeto)
- `ANALISE_GAPS_E_MELHORIAS.md` (análise detalhada)
- `STATUS_MIGRACAO_FASE_5.md` (status anterior)
- Projeto original em `_docs/cakto-checkout/`

**Lembre-se:** Você está fazendo um trabalho incrível migrando um projeto complexo para a stack mais moderna. Siga as instruções, teste bastante, e o resultado será excelente! 💪

