# 🚀 Fase 5 - Continuação: Componentes de Status e Auxiliares de Pagamento

## 📋 Contexto

A migração dos componentes de pagamento está **quase completa**. Os formulários principais foram migrados e o `CheckoutContext` foi atualizado. Agora é necessário migrar os **componentes de status** e os **componentes auxiliares de pagamento** que são usados durante o fluxo de pagamento.

## ✅ Status Atual

### Concluído

1. ✅ Hook `useSettings` migrado
2. ✅ Componentes de UI criados:
   - `TextField` (com máscaras e validação)
   - `Select` (com react-hook-form)
   - `Checkbox` adaptado (com react-hook-form)
   - `Card` adaptado (com props do original)
   - `Alert`
   - `Divider`
   - `Button` adaptado (`button-form.tsx`)
3. ✅ Formulários de pagamento migrados:
   - `CreditCardForm`
   - `PixForm`
   - `PixAutoForm`
   - `BoletoForm`
   - `PicPayForm`
   - `NubankPayForm`
   - `ApplePayForm`
   - `GooglePayForm`
4. ✅ Componentes auxiliares migrados:
   - `InstallmentsSelector`
   - `OrderSummary`
   - `OrderItemResume`
   - `CouponFormNew`
   - `LoadingPaymentForm`
   - `NotFoundPaymentForm`
   - `PixAutoSuccessPayment`
5. ✅ `CheckoutContext.mapForms` atualizado com componentes reais

### Pendente

1. ⏳ **Componentes Auxiliares de Pagamento** (PRIORIDADE MÁXIMA):
   - `PixPayment` - Componente para exibir QR Code PIX
   - `PixAutoPayment` - Componente para exibir QR Code PIX Automático
   - `BoletoPayment` - Componente para exibir boleto
   - `PicPayPayment` - Componente para exibir QR Code PicPay

2. ⏳ **Componentes de Status** (PRIORIDADE MÁXIMA):
   - `WaitingPayment` - Tela de aguardando pagamento
   - `SuccessPayment` - Tela de sucesso

## 📁 Estrutura de Migração

### Origem (cakto-checkout)
```
_docs/cakto-checkout/src/components/payment/
├── WaitingPayment.tsx          # ← MIGRAR
├── SuccessPayment.tsx           # ← MIGRAR
├── PixAutoSuccessPayment.tsx   # ✅ JÁ MIGRADO
├── PixPayment.tsx               # ← MIGRAR
├── PixAutoPayment.tsx           # ← MIGRAR
├── BoletoPayment.tsx            # ← MIGRAR
└── PicPayPayment.tsx            # ← MIGRAR
```

### Destino (cakto-checkoutv2/apps/web/src)
```
apps/web/src/components/payments/
├── WaitingPayment.tsx          # ← CRIAR
├── SuccessPayment.tsx           # ← CRIAR
├── PixAutoSuccessPayment.tsx   # ✅ JÁ CRIADO
├── PixPayment.tsx               # ← CRIAR
├── PixAutoPayment.tsx           # ← CRIAR
├── BoletoPayment.tsx            # ← CRIAR
└── PicPayPayment.tsx            # ← CRIAR
```

## 🎯 Tarefas

### 1. Componentes Auxiliares de Pagamento (PRIORIDADE MÁXIMA)

#### 1.1. PixPayment

**Arquivo Original**: `_docs/cakto-checkout/src/components/payment/PixPayment.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/PixPayment.tsx`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Substituir imports**:
   ```typescript
   // ANTES
   import Button from '@/components/common/Button';
   import useSettings from '@/hooks/useSettings';
   import { BRANDING } from '@/utils/brand';
   
   // DEPOIS
   import Button from '@/components/ui/button-form';
   import useSettings from '@/hooks/useSettings';
   import { BRANDING } from '@/lib/utils/brand';
   ```
3. **Manter toda lógica de QR Code** (usar `react-qrcode-logo`)
4. **Manter lógica de countdown** (15 minutos)
5. **Manter lógica de copiar código**
6. **Manter lógica de verificar pagamento**

**Dependências:**
- `react-qrcode-logo` - Verificar se está instalado
- `@/components/ui/button-form` - ✅ Já criado
- `@/hooks/useSettings` - ✅ Já migrado
- `@/lib/utils/brand` - ✅ Já existe

#### 1.2. PixAutoPayment

**Arquivo Original**: `_docs/cakto-checkout/src/components/payment/PixAutoPayment.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/PixAutoPayment.tsx`

**Adaptações Necessárias:**

1. **Mesmas adaptações do PixPayment**
2. **Manter lógica de countdown** (10 minutos)
3. **Manter lógica de progress bar**
4. **Manter lógica de autorização PIX Automático**

#### 1.3. BoletoPayment

**Arquivo Original**: `_docs/cakto-checkout/src/components/payment/BoletoPayment.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/BoletoPayment.tsx`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Substituir imports**:
   ```typescript
   // ANTES
   import Button from '@/components/common/Button';
   import useSettings from '@/hooks/useSettings';
   import { formatPrice } from '@/utils/format';
   import moment from 'moment';
   
   // DEPOIS
   import Button from '@/components/ui/button-form';
   import useSettings from '@/hooks/useSettings';
   import { formatPrice } from '@/lib/utils/format';
   import moment from 'moment';
   ```
3. **Manter toda lógica de boleto** (código de barras, vencimento, etc)
4. **Manter lógica de copiar código**
5. **Manter lógica de imprimir boleto**

**Dependências:**
- `moment` - Verificar se está instalado
- `@/components/ui/button-form` - ✅ Já criado
- `@/hooks/useSettings` - ✅ Já migrado
- `@/lib/utils/format` - ✅ Já existe

#### 1.4. PicPayPayment

**Arquivo Original**: `_docs/cakto-checkout/src/components/payment/PicPayPayment.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/PicPayPayment.tsx`

**Adaptações Necessárias:**

1. **Mesmas adaptações do PixPayment**
2. **Manter lógica de countdown** (15 minutos)
3. **Manter lógica de QR Code PicPay**

### 2. Componentes de Status (PRIORIDADE MÁXIMA)

#### 2.1. WaitingPayment

**Arquivo Original**: `_docs/cakto-checkout/src/components/payment/WaitingPayment.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/WaitingPayment.tsx`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Substituir imports**:
   ```typescript
   // ANTES
   import Card from '@/components/common/Card';
   import { CheckoutContext } from '@/contexts/CheckoutContext';
   import useDevice from '@/hooks/useDevice';
   import useSettings from '@/hooks/useSettings';
   import { useContext, useEffect } from 'react';
   import Confetti from 'react-confetti';
   import BoletoPayment from './BoletoPayment';
   import PicPayPayment from './PicPayPayment';
   import PixPayment from './PixPayment';
   import PixAutoPayment from './PixAutoPayment';
   
   // DEPOIS
   import Card from '@/components/ui/card-form';
   import { useCheckout } from '@/contexts';
   import useDevice from '@/hooks/useDevice';
   import useSettings from '@/hooks/useSettings';
   import { useEffect } from 'react';
   import Confetti from 'react-confetti';
   import BoletoPayment from './BoletoPayment';
   import PicPayPayment from './PicPayPayment';
   import PixPayment from './PixPayment';
   import PixAutoPayment from './PixAutoPayment';
   ```
3. **Substituir `useContext(CheckoutContext)` por `useCheckout()`**:
   ```typescript
   // ANTES
   const { firstPayment, checkPayment, checkingPayment } = useContext(CheckoutContext);
   
   // DEPOIS
   const { firstPayment, checkPayment, checkingPayment } = useCheckout();
   ```
4. **Substituir `import.meta.env` por helpers de `lib/env.ts`**:
   ```typescript
   // ANTES
   const intervalTime = Number(import.meta.env.VITE_PIX_CHECK_INTERVAL || 5000);
   const totalDuration = Number(import.meta.env.VITE_PIX_CHECK_DURATION || 300000);
   
   // DEPOIS
   import { getPixCheckInterval, getPixCheckDuration } from '@/lib/env';
   const intervalTime = getPixCheckInterval();
   const totalDuration = getPixCheckDuration();
   ```
5. **Manter toda lógica de polling de status**
6. **Manter lógica de redirecionamento mobile (PicPay)**
7. **Manter lógica de confetti**

**Dependências:**
- `react-confetti` - Verificar se está instalado
- `@/components/ui/card-form` - ✅ Já criado
- `@/contexts` - ✅ Já migrado
- `@/hooks/useDevice` - ✅ Já migrado
- `@/hooks/useSettings` - ✅ Já migrado
- `@/lib/env` - ✅ Já existe (adicionar `getPixCheckInterval` e `getPixCheckDuration` se não existirem)

#### 2.2. SuccessPayment

**Arquivo Original**: `_docs/cakto-checkout/src/components/payment/SuccessPayment.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/SuccessPayment.tsx`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Substituir imports**:
   ```typescript
   // ANTES
   import { useContext, useEffect, useMemo } from 'react';
   import { CheckoutContext } from '@/contexts/CheckoutContext';
   import useSettings from '@/hooks/useSettings';
   import { adjustColor } from '@/utils/colors';
   import Button from '../common/Button';
   import Card from '../common/Card';
   import { getQueryParams } from '@/utils/url';
   import posthog from 'posthog-js';
   import { trackEvent, trackPageView } from '@/utils/analytics';
   import PixAutoSuccessPayment from './PixAutoSuccessPayment';
   
   const cacktoUrl = import.meta.env.VITE_CAKTO_URL as string;
   
   // DEPOIS
   import { useEffect, useMemo } from 'react';
   import { useCheckout } from '@/contexts';
   import useSettings from '@/hooks/useSettings';
   import { adjustColor } from '@/lib/utils/colors';
   import Button from '@/components/ui/button-form';
   import Card from '@/components/ui/card-form';
   import { getQueryParams } from '@/lib/utils/url';
   import posthog from 'posthog-js';
   import { trackEvent, trackPageView } from '@/lib/utils/analytics';
   import PixAutoSuccessPayment from './PixAutoSuccessPayment';
   import { getCaktoUrl } from '@/lib/env';
   ```
3. **Substituir `useContext(CheckoutContext)` por `useCheckout()`**:
   ```typescript
   // ANTES
   const { offer, firstPayment, setFirstPayment } = useContext(CheckoutContext);
   
   // DEPOIS
   const { offer, firstPayment, setFirstPayment } = useCheckout();
   ```
4. **Substituir `import.meta.env` por helpers de `lib/env.ts`**:
   ```typescript
   // ANTES
   const cacktoUrl = import.meta.env.VITE_CAKTO_URL as string;
   
   // DEPOIS
   const cacktoUrl = getCaktoUrl() || '';
   ```
5. **Manter toda lógica de tracking** (PostHog, analytics)
6. **Manter lógica de redirecionamento** (produto, upsell)
7. **Manter lógica de tabela de produtos** (sucesso e erros)
8. **Manter lógica de botões** (acessar produto, continuar, pagar itens recusados)

**Dependências:**
- `posthog-js` - Verificar se está instalado
- `@/components/ui/button-form` - ✅ Já criado
- `@/components/ui/card-form` - ✅ Já criado
- `@/contexts` - ✅ Já migrado
- `@/hooks/useSettings` - ✅ Já migrado
- `@/lib/utils/colors` - ✅ Já existe
- `@/lib/utils/url` - ✅ Já existe
- `@/lib/utils/analytics` - ✅ Já existe (verificar se `trackPageView` e `trackEvent` existem)
- `@/lib/env` - ✅ Já existe (verificar se `getCaktoUrl` existe)

## 📝 Checklist de Migração

Para cada componente migrado, verificar:

- [ ] Adicionado `"use client"` no topo
- [ ] Imports atualizados para estrutura Next.js (`@/` paths)
- [ ] Removido uso de `useContext(CheckoutContext)` (substituir por `useCheckout()`)
- [ ] Removido uso de `import.meta.env` (substituir por helpers de `lib/env.ts`)
- [ ] Imports de componentes atualizados (`@/components/ui/button-form`, `@/components/ui/card-form`)
- [ ] Imports de utils atualizados (`@/lib/utils/format`, `@/lib/utils/colors`, etc)
- [ ] Mantida toda lógica de negócio
- [ ] Mantidas validações
- [ ] Mantidas integrações (PostHog, analytics, etc)
- [ ] Adicionados checks SSR quando necessário (`typeof window !== "undefined"`)
- [ ] Testado funcionamento no navegador

## 🔍 Arquivos de Referência

### Código de Referência
- `apps/web/src/components/payments/PixAutoSuccessPayment.tsx` - Exemplo de componente de status migrado
- `apps/web/src/components/ui/button-form.tsx` - Componente Button adaptado
- `apps/web/src/components/ui/card-form.tsx` - Componente Card adaptado
- `apps/web/src/contexts/checkout-context.tsx` - Contexto com `useCheckout()` hook
- `apps/web/src/hooks/useSettings.ts` - Hook de settings migrado
- `apps/web/src/lib/env.ts` - Helpers de variáveis de ambiente

### Projeto Original
- `_docs/cakto-checkout/src/components/payment/WaitingPayment.tsx` - Componente original
- `_docs/cakto-checkout/src/components/payment/SuccessPayment.tsx` - Componente original
- `_docs/cakto-checkout/src/components/payment/PixPayment.tsx` - Componente original
- `_docs/cakto-checkout/src/components/payment/PixAutoPayment.tsx` - Componente original
- `_docs/cakto-checkout/src/components/payment/BoletoPayment.tsx` - Componente original
- `_docs/cakto-checkout/src/components/payment/PicPayPayment.tsx` - Componente original

## ⚠️ Regras Importantes

### NUNCA
- ❌ Usar `import.meta.env` - usar `lib/env.ts`
- ❌ Usar `useContext(CheckoutContext)` - usar `useCheckout()`
- ❌ Usar imports relativos longos - usar `@/` paths
- ❌ Reescrever lógica de negócio - apenas adaptar

### SEMPRE
- ✅ Adicionar `"use client"` em todos os componentes
- ✅ Usar path aliases (`@/`) para imports
- ✅ Usar `useCheckout()` hook ao invés de `useContext(CheckoutContext)`
- ✅ Usar helpers de `lib/env.ts` ao invés de `import.meta.env`
- ✅ Manter compatibilidade com APIs existentes
- ✅ Testar funcionamento no navegador

## 🚀 Ordem de Execução Recomendada

1. **Primeiro**: Migrar componentes auxiliares de pagamento (PixPayment, PixAutoPayment, BoletoPayment, PicPayPayment)
2. **Segundo**: Migrar componentes de status (WaitingPayment, SuccessPayment)
3. **Terceiro**: Verificar se todas as dependências estão instaladas
4. **Quarto**: Testar fluxo completo de pagamento

## 📌 Notas Importantes

- Os componentes auxiliares de pagamento são **necessários** para o `WaitingPayment` funcionar
- O `SuccessPayment` usa `PixAutoSuccessPayment` que já foi migrado
- Verificar se `react-qrcode-logo` está instalado (necessário para QR Codes)
- Verificar se `react-confetti` está instalado (necessário para WaitingPayment)
- Verificar se `moment` está instalado (necessário para BoletoPayment)
- Verificar se `posthog-js` está instalado (necessário para SuccessPayment)
- Adicionar helpers `getPixCheckInterval` e `getPixCheckDuration` em `lib/env.ts` se não existirem
- Verificar se `trackPageView` e `trackEvent` existem em `lib/utils/analytics.ts`

## 🎯 Objetivo Final

Após completar esta fase, todos os componentes de pagamento estarão migrados e funcionais no novo projeto Next.js 16, mantendo toda a funcionalidade do projeto original.

