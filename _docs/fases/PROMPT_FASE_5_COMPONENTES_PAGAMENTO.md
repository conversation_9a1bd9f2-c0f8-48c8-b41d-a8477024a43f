# Prompt para Fase 5: Migração de Componentes de Pagamento - Checkout V2

## 🎯 Objetivo

Migrar os componentes de pagamento do checkout original (`cakto-checkout` - Vite + React) para o novo projeto (`cakto-checkoutv2` - Next.js 16), **completando a implementação dos placeholders criados na Fase 4** e migrando todos os formulários de pagamento necessários.

## 📋 Contexto do Projeto

### Projeto Original (Fonte)
- **Localização**: `/Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2/_docs/cakto-checkout/src`
- **Stack**: Vite + React 18 + TypeScript
- **Roteamento**: React Router DOM
- **HTTP Client**: Axios
- **Formulários**: React Hook Form + Yup
- **Estado**: React Query (tanstack-query)

### Projeto Novo (Destino)
- **Localização**: `/Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2/apps/web/src`
- **Stack**: Next.js 16 + React 19 + TypeScript
- **Roteamento**: Next.js App Router
- **HTTP Client**: fetch nativo (server-side) ou Server Actions
- **Formulários**: React Hook Form (manter)
- **Estado**: React Query (se necessário) ou useState/useEffect

### Status Atual da Migração

#### ✅ Fase 1: Base (Tipos e Utilitários) - CONCLUÍDA
- ✅ Tipos TypeScript migrados (`types/`)
- ✅ Utilitários migrados (`lib/utils/`)
- ✅ Constantes criadas (`constants/payment.ts`)
- ✅ Helpers de ambiente adaptados (`lib/env.ts`)

#### ✅ Fase 2: Componentes do Builder - CONCLUÍDA
- ✅ Componentes do builder migrados (`components/builder/`)
- ✅ Hook `useImagePreview` migrado
- ✅ Assets copiados (`assets/`)

#### ✅ Fase 3: Contextos - CONCLUÍDA
- ✅ `CheckoutContext` migrado
- ✅ `CheckoutModeContext` migrado
- ✅ `NotificationContext` migrado
- ✅ `useNotifications` hook migrado

#### ✅ Fase 4: Hooks e Serviços - CONCLUÍDA
- ✅ Hooks de pixels migrados e implementados
- ✅ Hooks auxiliares migrados
- ✅ Hooks de tracking migrados
- ✅ Hooks de UI/UX migrados
- ✅ Ícones personalizados migrados
- ✅ Serviço de CEP criado

#### 🔄 Fase 5: Componentes de Pagamento - EM ANDAMENTO
- ⏳ **Formulários de Pagamento** - **PRIORIDADE MÁXIMA**
  - ⏳ `CreditCardForm` - Migrar formulário de cartão de crédito
  - ⏳ `PixForm` - Migrar formulário de PIX
  - ⏳ `PixAutoForm` - Migrar formulário de PIX Automático
  - ⏳ `BoletoForm` - Migrar formulário de Boleto
  - ⏳ `PicPayForm` - Migrar formulário de PicPay
  - ⏳ `ApplePayForm` - Migrar formulário de Apple Pay
  - ⏳ `GooglePayForm` - Migrar formulário de Google Pay
  - ⏳ `NubankPayForm` - Migrar formulário de Nubank (Open Finance)
- ⏳ **Componentes Auxiliares** - **PRIORIDADE ALTA**
  - ⏳ `InstallmentsSelector` - Seletor de parcelas
  - ⏳ `OrderSummary` - Resumo do pedido
  - ⏳ `LoadingPaymentForm` - Formulário de loading
  - ⏳ `NotFoundPaymentForm` - Formulário não encontrado
- ⏳ **Componentes de Status** - **PRIORIDADE MÉDIA**
  - ⏳ `WaitingPayment` - Tela de aguardando pagamento
  - ⏳ `SuccessPayment` - Tela de sucesso
  - ⏳ `PixAutoSuccessPayment` - Tela de sucesso PIX Auto
- ⏳ **Atualizar CheckoutContext** - **PRIORIDADE MÁXIMA**
  - ⏳ Substituir placeholders de formulários por componentes reais

## 🎯 Princípios da Migração

### ✅ REUTILIZAR (NÃO REESCREVER)
- **Lógica de Negócio**: Manter toda validação, cálculos e regras
- **Estrutura de Componentes**: Manter estrutura similar, apenas adaptar para Next.js
- **Validações**: Manter toda lógica de validação (Yup → Zod se necessário)
- **Integrações**: Manter integrações com 3DS, Nethone, etc

### ⚠️ ADAPTAR (MUDANÇAS NECESSÁRIAS)
- **Imports**: Atualizar paths para estrutura Next.js (`@/` ao invés de paths relativos)
- **Client Components**: Adicionar `"use client"` no topo
- **React Router**: Substituir `useParams`, `useSearchParams` por props ou `next/navigation`
- **React Query**: Substituir por `useState`/`useEffect` ou adaptar para Next.js
- **APIs**: Usar `lib/api/` ao invés de `services/`
- **Contextos**: Usar contextos migrados na Fase 3 ao invés de React Query
- **Variáveis de Ambiente**: Usar `lib/env.ts` ao invés de `import.meta.env`

### ❌ REMOVER/SUBSTITUIR
- **React Router**: Remover `useParams`, `useSearchParams`, `useNavigate`, `useLocation`
- **Axios**: Substituir por `fetch` ou helpers de `lib/api/`
- **import.meta.env**: Substituir por helpers de `lib/env.ts`
- **React Query**: Substituir por contextos migrados ou `useState`/`useEffect`
- **Vite-specific code**: Remover qualquer código específico do Vite

## 📁 Estrutura de Migração

### Origem (cakto-checkout)
```
src/
├── components/
│   ├── checkout/
│   │   ├── Payments/                    # ⭐ PRIORIDADE MÁXIMA
│   │   │   ├── CreditCardForm.tsx
│   │   │   ├── PixForm.tsx
│   │   │   ├── PixAutoForm.tsx
│   │   │   ├── BoletoForm.tsx
│   │   │   ├── PicPayForm.tsx
│   │   │   ├── ApplePayForm.tsx
│   │   │   ├── GooglePayForm.tsx
│   │   │   ├── NubankPayForm.tsx
│   │   │   ├── InstallmentsSelector.tsx  # ⭐ PRIORIDADE ALTA
│   │   │   ├── OrderSummary.tsx          # ⭐ PRIORIDADE ALTA
│   │   │   ├── LoadingPaymentForm.tsx    # ⭐ PRIORIDADE ALTA
│   │   │   └── NotFoundPaymentForm.tsx   # ⭐ PRIORIDADE ALTA
│   │   └── PaymentMethods.tsx
│   └── payment/                          # ⭐ PRIORIDADE MÉDIA
│       ├── WaitingPayment.tsx
│       ├── SuccessPayment.tsx
│       ├── PixAutoSuccessPayment.tsx
│       ├── BoletoPayment.tsx
│       ├── PicPayPayment.tsx
│       ├── PixPayment.tsx
│       └── PixAutoPayment.tsx
├── services/
│   └── 3ds/                              # Integração 3DS
│       └── threeDSService.ts
└── hooks/
    └── useNethone.ts                     # Antifraude
```

### Destino (cakto-checkoutv2/apps/web/src)
```
src/
├── components/
│   ├── payments/                         # ← MIGRAR (renomear Payments → payments)
│   │   ├── CreditCardForm.tsx            # ← MIGRAR
│   │   ├── PixForm.tsx                   # ← MIGRAR
│   │   ├── PixAutoForm.tsx               # ← MIGRAR
│   │   ├── BoletoForm.tsx                # ← MIGRAR
│   │   ├── PicPayForm.tsx                # ← MIGRAR
│   │   ├── ApplePayForm.tsx              # ← MIGRAR
│   │   ├── GooglePayForm.tsx             # ← MIGRAR
│   │   ├── NubankPayForm.tsx             # ← MIGRAR
│   │   ├── InstallmentsSelector.tsx      # ← MIGRAR
│   │   ├── OrderSummary.tsx              # ← MIGRAR
│   │   ├── LoadingPaymentForm.tsx        # ← MIGRAR
│   │   └── NotFoundPaymentForm.tsx      # ← MIGRAR
│   └── checkout/
│       └── PaymentMethods.tsx            # ← MIGRAR (se necessário)
├── lib/
│   ├── api/                              # ✅ JÁ CRIADO
│   │   └── checkout-client.ts            # ✅ JÁ CRIADO
│   └── services/                         # ← CRIAR se necessário
│       └── 3ds/                          # ← MIGRAR integração 3DS
│           └── threeDSService.ts
└── contexts/                             # ✅ JÁ MIGRADO (Fase 3)
    └── checkout-context.tsx              # ← ATUALIZAR mapForms
```

## 📚 Referências e Exemplos

### Exemplo de Componente Adaptado para Next.js

Veja o exemplo de componentes já migrados:

```typescript
"use client";

import { useFormContext } from "react-hook-form";
import { useCheckout } from "@/contexts";
import type { PaymentMethod } from "@/types";

export const CreditCardForm = () => {
  const { register, watch } = useFormContext();
  const { offer, paying } = useCheckout();
  
  // ... lógica do componente
  
  return (
    <div>
      {/* Formulário */}
    </div>
  );
};
```

**Padrão a seguir:**
1. Adicionar `"use client"` no topo
2. Atualizar imports para usar `@/` paths
3. Substituir React Router por props ou `useCheckout()` context
4. Substituir React Query por `useCheckout()` context
5. Substituir `import.meta.env` por `lib/env.ts`
6. Substituir Axios por `fetch` ou `lib/api/`
7. Manter toda lógica de negócio

### Contextos Já Migrados (Fase 3)

Os componentes devem usar o `CheckoutContext` ao invés de React Query:

```typescript
// ANTES (Vite + React)
const { id } = useParams<{ id: string }>();
const queryClient = useQueryClient();
const offer = queryClient.getQueryData(['checkout', id]) as ProductData;

// DEPOIS (Next.js 16)
import { useCheckout } from "@/contexts";

const { offer, pay, paying } = useCheckout();
```

### Hooks Já Migrados (Fase 4)

Use os hooks migrados na Fase 4:
- `useCepSearch` - Busca de CEP
- `useCouponDiscount` - Cálculo de desconto
- `usePrice` - Formatação de preços
- `useServiceFee` - Cálculo de taxa de serviço
- `useFetchCalculatedInstallments` - Busca de parcelamento
- `useDevice` - Detecção de dispositivo
- `useMercadoPagoDeviceId` - Device ID do Mercado Pago

## 🚀 Plano de Execução - Fase 5

### 1. Formulários de Pagamento (PRIORIDADE MÁXIMA)

#### 1.1. CreditCardForm

**Arquivo Original**: `_docs/cakto-checkout/src/components/checkout/Payments/CreditCardForm.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/CreditCardForm.tsx`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo

2. **Substituir React Router e React Query**:
   ```typescript
   // ANTES
   const { id } = useParams<{ id: string }>();
   const queryClient = useQueryClient();
   const offer = queryClient.getQueryData(['checkout', id]) as ProductData;
   
   // DEPOIS
   import { useCheckout } from "@/contexts";
   
   const { offer, pay, paying } = useCheckout();
   ```

3. **Substituir variáveis de ambiente**:
   ```typescript
   // ANTES
   const publicKey = import.meta.env.VITE_HOPYPAY_PUBLIC_KEY;
   
   // DEPOIS
   import { getHopyPayPublicKey } from "@/lib/env";
   const publicKey = getHopyPayPublicKey();
   ```

4. **Substituir Axios por fetch**:
   ```typescript
   // ANTES
   import axios from 'axios';
   const response = await axios.post('/api/endpoint', data);
   
   // DEPOIS
   import { startPayment } from "@/lib/api/checkout-client";
   const response = await startPayment(productId, payload);
   ```

5. **Adaptar integração 3DS**:
   - Migrar `threeDSService.ts` para `lib/services/3ds/threeDSService.ts`
   - Adaptar para usar `fetch` ao invés de Axios
   - Manter toda lógica de 3DS

6. **Adaptar integração Nethone**:
   - Verificar se `useNethone` hook precisa ser migrado
   - Adaptar para usar `fetch` ao invés de Axios

7. **Atualizar imports**:
   ```typescript
   // ANTES
   import { PaymentMethod } from '../types';
   import { useCheckout } from '@/contexts/CheckoutContext';
   
   // DEPOIS
   import type { PaymentMethod } from "@/types";
   import { useCheckout } from "@/contexts";
   ```

**Dependências:**
- `lib/api/checkout-client.ts` - ✅ Já criado
- `lib/services/3ds/threeDSService.ts` - ⏳ Precisa migrar
- `hooks/useNethone.ts` - ⏳ Verificar se precisa migrar
- `contexts/checkout-context.tsx` - ✅ Já migrado (Fase 3)

#### 1.2. PixForm

**Arquivo Original**: `_docs/cakto-checkout/src/components/checkout/Payments/PixForm.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/PixForm.tsx`

**Adaptações Necessárias:**

1. **Mesmas adaptações do CreditCardForm** (React Router, React Query, env vars, Axios)
2. **Manter lógica de geração de PIX**
3. **Adaptar para usar `useCheckout()` context**

#### 1.3. PixAutoForm

**Arquivo Original**: `_docs/cakto-checkout/src/components/checkout/Payments/PixAutoForm.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/PixAutoForm.tsx`

**Adaptações Necessárias:**

1. **Mesmas adaptações do CreditCardForm**
2. **Manter lógica de PIX Automático**
3. **Adaptar para usar `useCheckout()` context**

#### 1.4. BoletoForm

**Arquivo Original**: `_docs/cakto-checkout/src/components/checkout/Payments/BoletoForm.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/BoletoForm.tsx`

**Adaptações Necessárias:**

1. **Mesmas adaptações do CreditCardForm**
2. **Manter lógica de geração de boleto**
3. **Adaptar para usar `useCheckout()` context**

#### 1.5. PicPayForm

**Arquivo Original**: `_docs/cakto-checkout/src/components/checkout/Payments/PicPayForm.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/PicPayForm.tsx`

**Adaptações Necessárias:**

1. **Mesmas adaptações do CreditCardForm**
2. **Manter lógica de integração PicPay**
3. **Adaptar para usar `useCheckout()` context**

#### 1.6. ApplePayForm

**Arquivo Original**: `_docs/cakto-checkout/src/components/checkout/Payments/ApplePayForm.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/ApplePayForm.tsx`

**Adaptações Necessárias:**

1. **Mesmas adaptações do CreditCardForm**
2. **Manter lógica de integração Apple Pay**
3. **Adaptar para usar `useCheckout()` context**
4. **Verificar se precisa de checks SSR** (Apple Pay é client-side)

#### 1.7. GooglePayForm

**Arquivo Original**: `_docs/cakto-checkout/src/components/checkout/Payments/GooglePayForm.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/GooglePayForm.tsx`

**Adaptações Necessárias:**

1. **Mesmas adaptações do CreditCardForm**
2. **Manter lógica de integração Google Pay**
3. **Adaptar para usar `useCheckout()` context**
4. **Verificar se precisa de checks SSR** (Google Pay é client-side)

#### 1.8. NubankPayForm

**Arquivo Original**: `_docs/cakto-checkout/src/components/checkout/Payments/NubankPayForm.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/NubankPayForm.tsx`

**Adaptações Necessárias:**

1. **Mesmas adaptações do CreditCardForm**
2. **Manter lógica de integração Nubank (Open Finance)**
3. **Adaptar para usar `useCheckout()` context**

### 2. Componentes Auxiliares (PRIORIDADE ALTA)

#### 2.1. InstallmentsSelector

**Arquivo Original**: `_docs/cakto-checkout/src/components/checkout/Payments/InstallmentsSelector.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/InstallmentsSelector.tsx`

**Adaptações Necessárias:**

1. **Usar `useFetchCalculatedInstallments` hook** migrado na Fase 4
2. **Adicionar `"use client"`** no topo
3. **Atualizar imports** para usar `@/` paths
4. **Manter toda lógica de seleção de parcelas**

#### 2.2. OrderSummary

**Arquivo Original**: `_docs/cakto-checkout/src/components/checkout/Payments/OrderSummary.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/OrderSummary.tsx`

**Adaptações Necessárias:**

1. **Usar `usePrice` hook** migrado na Fase 4
2. **Usar `useCouponDiscount` hook** migrado na Fase 4
3. **Usar `useServiceFee` hook** migrado na Fase 4
4. **Adicionar `"use client"`** no topo
5. **Atualizar imports** para usar `@/` paths
6. **Manter toda lógica de cálculo de preços**

#### 2.3. LoadingPaymentForm

**Arquivo Original**: `_docs/cakto-checkout/src/components/checkout/Payments/LoadingPaymentForm.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/LoadingPaymentForm.tsx`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Atualizar imports** para usar `@/` paths
3. **Manter estrutura de loading**

#### 2.4. NotFoundPaymentForm

**Arquivo Original**: `_docs/cakto-checkout/src/components/checkout/Payments/NotFoundPaymentForm.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/NotFoundPaymentForm.tsx`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Atualizar imports** para usar `@/` paths
3. **Manter estrutura de erro**

### 3. Componentes de Status (PRIORIDADE MÉDIA)

#### 3.1. WaitingPayment

**Arquivo Original**: `_docs/cakto-checkout/src/components/payment/WaitingPayment.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/WaitingPayment.tsx`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Atualizar imports** para usar `@/` paths
3. **Adaptar para usar `useCheckout()` context**
4. **Manter lógica de polling de status**

#### 3.2. SuccessPayment

**Arquivo Original**: `_docs/cakto-checkout/src/components/payment/SuccessPayment.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/SuccessPayment.tsx`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Atualizar imports** para usar `@/` paths
3. **Adaptar para usar `useCheckout()` context**
4. **Manter estrutura de sucesso**

#### 3.3. PixAutoSuccessPayment

**Arquivo Original**: `_docs/cakto-checkout/src/components/payment/PixAutoSuccessPayment.tsx`

**Arquivo Destino**: `apps/web/src/components/payments/PixAutoSuccessPayment.tsx`

**Adaptações Necessárias:**

1. **Adicionar `"use client"`** no topo
2. **Atualizar imports** para usar `@/` paths
3. **Adaptar para usar `useCheckout()` context**
4. **Manter estrutura de sucesso PIX Auto**

### 4. Atualizar CheckoutContext (PRIORIDADE MÁXIMA)

**Arquivo**: `apps/web/src/contexts/checkout-context.tsx`

**Atualização Necessária:**

Substituir os placeholders de formulários por componentes reais:

```typescript
// ANTES (placeholders)
const mapForms: Record<string, React.ComponentType<unknown>> = {
  credit_card: (() => <div>Credit Card Form</div>) as React.ComponentType,
  boleto: (() => <div>Boleto Form</div>) as React.ComponentType,
  pix: (() => <div>Pix Form</div>) as React.ComponentType,
  // ... etc
};

// DEPOIS (componentes reais)
import { CreditCardForm } from "@/components/payments/CreditCardForm";
import { BoletoForm } from "@/components/payments/BoletoForm";
import { PixForm } from "@/components/payments/PixForm";
import { PixAutoForm } from "@/components/payments/PixAutoForm";
import { PicPayForm } from "@/components/payments/PicPayForm";
import { ApplePayForm } from "@/components/payments/ApplePayForm";
import { GooglePayForm } from "@/components/payments/GooglePayForm";
import { NubankPayForm } from "@/components/payments/NubankPayForm";

const mapForms: Record<string, React.ComponentType<unknown>> = {
  credit_card: CreditCardForm,
  boleto: BoletoForm,
  pix: PixForm,
  pix_auto: PixAutoForm,
  picpay: PicPayForm,
  applepay: ApplePayForm,
  googlepay: GooglePayForm,
  openfinance_nubank: NubankPayForm,
};
```

### 5. Integrações Especiais

#### 5.1. Integração 3DS

**Arquivo Original**: `_docs/cakto-checkout/src/services/3ds/threeDSService.ts`

**Arquivo Destino**: `apps/web/src/lib/services/3ds/threeDSService.ts`

**Adaptações Necessárias:**

1. **Substituir Axios por fetch**
2. **Atualizar imports** para usar `@/` paths
3. **Manter toda lógica de 3DS** (Cielo e Pagar.me)
4. **Adaptar para Next.js** (verificar se precisa de checks SSR)

#### 5.2. Integração Nethone (Antifraude)

**Arquivo Original**: `_docs/cakto-checkout/src/hooks/useNethone.ts`

**Arquivo Destino**: `apps/web/src/hooks/useNethone.ts` (se necessário)

**Adaptações Necessárias:**

1. **Verificar se já existe** ou precisa ser migrado
2. **Substituir Axios por fetch**
3. **Adicionar `"use client"`** no topo
4. **Atualizar imports** para usar `@/` paths
5. **Manter toda lógica de antifraude**

## 📝 Checklist de Migração por Componente

Para cada componente migrado, verificar:

- [ ] Adicionado `"use client"` no topo
- [ ] Imports atualizados para estrutura Next.js (`@/` paths)
- [ ] Removido uso de React Router (substituir por props ou `useCheckout()` context)
- [ ] Removido uso de React Query (substituir por `useCheckout()` context ou `useState`/`useEffect`)
- [ ] Removido uso de Axios (substituir por `fetch` ou `lib/api/`)
- [ ] Removido código específico do Vite (`import.meta.env`)
- [ ] Substituído `import.meta.env` por helpers de `lib/env.ts`
- [ ] Mantida toda lógica de negócio
- [ ] Mantidas validações (Yup ou adaptar para Zod)
- [ ] Mantidas integrações (3DS, Nethone, etc)
- [ ] Adicionados checks SSR quando necessário (`typeof window !== "undefined"`)
- [ ] Testado funcionamento no navegador

## 🔍 Arquivos de Referência

### Código de Referência
- `apps/web/src/contexts/checkout-context.tsx` - Contexto que usa formulários
- `apps/web/src/hooks/usePrice.ts` - Hook de preços (Fase 4)
- `apps/web/src/hooks/useCouponDiscount.ts` - Hook de cupom (Fase 4)
- `apps/web/src/hooks/useServiceFee.ts` - Hook de taxa (Fase 4)
- `apps/web/src/lib/api/checkout-client.ts` - API client para operações client-side
- `apps/web/src/lib/env.ts` - Helpers de variáveis de ambiente

### Projeto Original
- `_docs/cakto-checkout/src/components/checkout/Payments/CreditCardForm.tsx` - Formulário original
- `_docs/cakto-checkout/src/components/checkout/Payments/PixForm.tsx` - Formulário original
- `_docs/cakto-checkout/src/services/3ds/threeDSService.ts` - Serviço 3DS original
- `_docs/cakto-checkout/src/hooks/useNethone.ts` - Hook Nethone original

## ⚠️ Regras Importantes

### NUNCA
- ❌ Reescrever lógica de negócio - apenas adaptar
- ❌ Usar `import.meta.env` - usar `lib/env.ts`
- ❌ Usar `axios` - usar `fetch` ou `lib/api/`
- ❌ Usar React Router - usar props ou `useCheckout()` context
- ❌ Usar React Query diretamente - usar `useCheckout()` context
- ❌ Criar Server Components com hooks do React
- ❌ Modificar validações sem necessidade

### SEMPRE
- ✅ Adicionar `"use client"` em todos os componentes
- ✅ Usar path aliases (`@/`) para imports
- ✅ Usar `useCheckout()` context ao invés de React Query
- ✅ Usar hooks migrados na Fase 4 (usePrice, useCouponDiscount, etc)
- ✅ Adicionar checks SSR quando usar APIs do navegador
- ✅ Manter compatibilidade com APIs existentes
- ✅ Testar funcionamento no navegador
- ✅ Manter integrações (3DS, Nethone, etc)

## 🎯 Prioridades

1. **MÁXIMA**: Formulários de Pagamento (necessários para CheckoutContext funcionar completamente)
   - `CreditCardForm`
   - `PixForm`
   - `PixAutoForm`
   - `BoletoForm`
   - `PicPayForm`
   - `ApplePayForm`
   - `GooglePayForm`
   - `NubankPayForm`
   - Atualizar `CheckoutContext.mapForms`

2. **ALTA**: Componentes Auxiliares (necessários para funcionalidades do checkout)
   - `InstallmentsSelector`
   - `OrderSummary`
   - `LoadingPaymentForm`
   - `NotFoundPaymentForm`

3. **MÉDIA**: Componentes de Status
   - `WaitingPayment`
   - `SuccessPayment`
   - `PixAutoSuccessPayment`

4. **ALTA**: Integrações Especiais
   - Integração 3DS (`threeDSService.ts`)
   - Integração Nethone (`useNethone.ts` - se necessário)

## 🚀 Começar Agora

**Inicie pelos formulários de pagamento** (prioridade máxima). Para cada componente:

1. Ler o arquivo original completo
2. Identificar todas as dependências (React Router, React Query, Axios, etc)
3. Adaptar para Next.js (substituir por contextos migrados, fetch, etc)
4. Migrar integrações especiais (3DS, Nethone) se necessário
5. Atualizar imports para usar `@/` paths
6. Atualizar `CheckoutContext.mapForms` com o componente migrado
7. Testar funcionamento no navegador
8. Continuar para o próximo componente

**Lembre-se**: O objetivo é **reutilizar ao máximo**, não reescrever. Os componentes devem funcionar quase idênticos ao original, apenas com adaptações mínimas para Next.js.

## 📌 Notas Importantes

- Os formulários de pagamento atualmente têm placeholders no `CheckoutContext`. Substitua por componentes reais.
- Use o `useCheckout()` context ao invés de React Query para acessar dados do checkout.
- Use os hooks migrados na Fase 4 (usePrice, useCouponDiscount, useServiceFee, etc).
- Mantenha todas as integrações (3DS, Nethone, Apple Pay, Google Pay, etc).
- Adicione checks SSR (`typeof window !== "undefined"`) quando usar APIs do navegador.
- Os componentes devem ser migrados para `components/payments/` (renomear `Payments` → `payments`).

## 🔗 Dependências Externas

Verificar se as seguintes bibliotecas estão instaladas:

- `react-hook-form` - Para formulários (já usado)
- `yup` ou `zod` - Para validação (verificar qual está sendo usado)
- Bibliotecas específicas de cada método de pagamento:
  - Apple Pay SDK
  - Google Pay SDK
  - PicPay SDK
  - Nubank SDK
  - Etc

Se não estiverem instaladas, adicionar ao `package.json` ou criar implementações alternativas.

## 📋 Estrutura Final Esperada

Após a migração, a estrutura deve ser:

```
apps/web/src/
├── components/
│   └── payments/
│       ├── CreditCardForm.tsx
│       ├── PixForm.tsx
│       ├── PixAutoForm.tsx
│       ├── BoletoForm.tsx
│       ├── PicPayForm.tsx
│       ├── ApplePayForm.tsx
│       ├── GooglePayForm.tsx
│       ├── NubankPayForm.tsx
│       ├── InstallmentsSelector.tsx
│       ├── OrderSummary.tsx
│       ├── LoadingPaymentForm.tsx
│       ├── NotFoundPaymentForm.tsx
│       ├── WaitingPayment.tsx
│       ├── SuccessPayment.tsx
│       └── PixAutoSuccessPayment.tsx
├── lib/
│   └── services/
│       └── 3ds/
│           └── threeDSService.ts
└── contexts/
    └── checkout-context.tsx  # ← Atualizado com mapForms real
```

