# PROMPT - Fase 7: i18n Completo + UX Faltante + Componentes Finais

## 🎯 Objetivo Principal

**Completar a migração do checkout original para Next.js 16, removendo TODOS os textos hardcoded e implementando os componentes/features que ainda faltam para atingir paridade completa com o projeto original.**

---

## 📋 Contexto

### Status Atual (Fase 6 Concluída)
- ✅ Sistema de i18n completo com tipagem forte
- ✅ Header do checkout com seletor de idioma
- ✅ 3 componentes essenciais migrados (AddressForm, BumpItem, AboutCpf)
- ✅ 200+ mensagens traduzidas em pt/en/es
- ✅ Hooks e utilitários para suporte in-app browser

### Problemas Identificados
- ❌ **Muitos componentes ainda têm texto hardcoded em português**
- ❌ **Faltam componentes do projeto original**
- ❌ **UX incompleta em alguns fluxos**
- ❌ **Checkout parece "mock" em vez de funcional**

---

## 🎯 Objetivos da Fase 7

### 1. PRIORIDADE MÁXIMA: i18n Completo ⭐⭐⭐⭐⭐

**Remover TODOS os textos hardcoded e implementar traduções em:**

#### Componentes de Checkout
- [ ] `CheckoutComponent.tsx` - Formulário principal
  - "Informações de Contato" → `t("form.contact_info_title")`
  - "Nome completo" → `t("form.full_name")`
  - "Preencha seu nome" → `t("form.full_name_placeholder")`
  - "Email" → `t("form.email")`
  - "Preencha seu email" → `t("form.email_placeholder")`
  - "CPF/CNPJ" → `t("form.cpf_cnpj")`
  - "Preencha seu CPF/CNPJ" → `t("form.cpf_cnpj_placeholder")`
  - "Forma de Pagamento" → `t("payment.payment_method_title")`
  - "Compra 100% segura" → `t("checkout.secure_purchase")`
  - "Este site é protegido pelo reCAPTCHA do Google" → `t("checkout.recaptcha_notice")`

#### Componentes de Pagamento
- [ ] `CreditCardForm.tsx`
  - "Número do cartão" → `t("payment.credit_card.card_number")`
  - "0000 0000 0000 0000" → `t("payment.credit_card.card_number_placeholder")`
  - "Nome no cartão" → `t("payment.credit_card.cardholder_name")`
  - "Validade" → `t("payment.credit_card.expiration_date")`
  - "MM/AA" → `t("payment.credit_card.expiration_placeholder")`
  - "CVV" → `t("payment.credit_card.cvv")`
  - "Digite uma data válida" → `t("errors.invalid_date")`
  - "Data inválida" → `t("errors.invalid_expiration")`
  - "Salvar cartão" → `t("payment.credit_card.save_card")`
  - "Parcelas" → `t("payment.installments")`

- [ ] `PixForm.tsx`
  - "Liberação imediata" → `t("payment.pix.instant_release")`
  - "É simples, só usar o aplicativo de seu banco para pagar Pix" → `t("payment.pix.easy_to_use")`

- [ ] `PixAutoForm.tsx`
  - Traduzir vantagens do PIX Auto

- [ ] `BoletoForm.tsx`
  - "CPF/CNPJ do Pagador" → `t("payment.boleto.payer_document")`
  - Outras mensagens relacionadas a boleto

- [ ] `PicPayForm.tsx`
  - Mensagens sobre PicPay

- [ ] `OrderSummary.tsx`
  - "Resumo do pedido" → `t("checkout.order_summary")`
  - "Cupom aplicado" → `t("checkout.coupon_applied")`
  - "Taxa de serviço" → `t("checkout.service_fee")`
  - "Taxa de serviço cobrada pela plataforma" → `t("checkout.service_fee_tooltip")`
  - "Total" → `t("checkout.total")`

- [ ] `OrderItemResume.tsx`
  - "à vista" → `t("payment.one_time")`
  - "de" → `t("common.from")`
  - "por" → `t("common.per")`
  - "mês" → `t("common.month")`
  - "ano" → `t("common.year")`
  - "semana" → `t("common.week")`
  - "dia" → `t("common.day")`

- [ ] `CouponFormNew.tsx`
  - "Cupom de desconto" → `t("checkout.coupon_code")`
  - "Digite seu cupom" → `t("checkout.coupon_placeholder")`
  - "Aplicar" → `t("common.apply")`
  - "Remover" → `t("common.remove")`
  - "Cupom inválido" → `t("errors.invalid_coupon")`
  - "Cupom aplicado com sucesso" → `t("checkout.coupon_success")`

- [ ] `InstallmentsSelector.tsx`
  - "Parcelas" → `t("payment.installments")`
  - "à vista" → `t("payment.one_time")`

#### Componentes de Status de Pagamento
- [ ] `WaitingPayment.tsx`
  - "Aguardando pagamento" → `t("payment.waiting_payment")`
  - "Escaneie o QR Code" → `t("payment.pix.scan_qrcode")`
  - "Ou copie o código" → `t("payment.pix.or_copy_code")`
  - "Verificando pagamento..." → `t("payment.checking_payment")`
  - "Verificar pagamento" → `t("payment.check_payment")`

- [ ] `PixPayment.tsx`
  - "QR Code PIX" → `t("payment.pix.qrcode_title")`
  - "Copiar código" → `t("payment.pix.copy_code")`
  - "Código copiado!" → `t("payment.pix.code_copied")`
  - "Escanear QR Code" → `t("payment.pix.scan_instruction")`
  - "Abra o app do seu banco" → `t("payment.pix.open_bank_app")`
  - "Escaneie o código" → `t("payment.pix.scan_code")`
  - "Confirme o pagamento" → `t("payment.pix.confirm_payment")`

- [ ] `PixAutoPayment.tsx`
  - Mensagens do PIX Auto

- [ ] `PixAutoSuccessPayment.tsx`
  - Mensagens de sucesso do PIX Auto

- [ ] `BoletoPayment.tsx`
  - "Código de barras do boleto" → `t("payment.boleto.barcode_title")`
  - "Copiar código" → `t("payment.boleto.copy_code")`
  - "Código copiado!" → `t("payment.boleto.code_copied")`
  - "Imprimir boleto" → `t("payment.boleto.print")`
  - "Pagar no banco" → `t("payment.boleto.pay_at_bank")`
  - "Data de vencimento" → `t("payment.boleto.due_date")`

- [ ] `PicPayPayment.tsx`
  - Mensagens do PicPay

- [ ] `SuccessPayment.tsx`
  - "Pagamento confirmado!" → `t("success.title")`
  - "Seu pagamento foi aprovado com sucesso" → `t("success.subtitle")`
  - "Você receberá um email com os detalhes" → `t("success.email_sent")`
  - "Acessar produto" → `t("success.access_product")`
  - "Número do pedido" → `t("success.order_number")`

#### Componentes de Builder
- [ ] `CheckoutComponentText.tsx`
- [ ] `CheckoutComponentHeader.tsx`
- [ ] `CheckoutComponentTestimonial.tsx`
- [ ] `CheckoutComponentList.tsx`
- [ ] `CheckoutComponentAdvantage.tsx`
- [ ] `CheckoutComponentSeal.tsx`
- [ ] `CheckoutComponentCountdown.tsx`

#### Componentes de Erro
- [ ] Criar `ErrorBoundary.tsx` com i18n
- [ ] Atualizar mensagens de erro para usar i18n

---

### 2. Expandir Mensagens i18n ⭐⭐⭐⭐

**Adicionar novas categorias aos arquivos de tradução:**

```json
{
  "form": {
    "contact_info_title": "...",
    "full_name": "...",
    "full_name_placeholder": "...",
    "email": "...",
    "email_placeholder": "...",
    "cpf_cnpj": "...",
    "cpf_cnpj_placeholder": "...",
    "phone": "...",
    "phone_placeholder": "..."
  },
  "payment": {
    "payment_method_title": "...",
    "installments": "...",
    "one_time": "...",
    "waiting_payment": "...",
    "checking_payment": "...",
    "check_payment": "...",
    "credit_card": {
      "card_number": "...",
      "card_number_placeholder": "...",
      "cardholder_name": "...",
      "cardholder_name_placeholder": "...",
      "expiration_date": "...",
      "expiration_placeholder": "...",
      "cvv": "...",
      "cvv_placeholder": "...",
      "save_card": "..."
    },
    "pix": {
      "instant_release": "...",
      "easy_to_use": "...",
      "qrcode_title": "...",
      "scan_qrcode": "...",
      "or_copy_code": "...",
      "copy_code": "...",
      "code_copied": "...",
      "scan_instruction": "...",
      "open_bank_app": "...",
      "scan_code": "...",
      "confirm_payment": "..."
    },
    "pix_auto": {
      "title": "...",
      "description": "...",
      "advantages": ["..."]
    },
    "boleto": {
      "payer_document": "...",
      "barcode_title": "...",
      "copy_code": "...",
      "code_copied": "...",
      "print": "...",
      "pay_at_bank": "...",
      "due_date": "..."
    },
    "picpay": {
      "title": "...",
      "scan_qrcode": "..."
    }
  },
  "checkout": {
    "order_summary": "...",
    "coupon_code": "...",
    "coupon_placeholder": "...",
    "coupon_applied": "...",
    "coupon_success": "...",
    "service_fee": "...",
    "service_fee_tooltip": "...",
    "total": "...",
    "secure_purchase": "...",
    "recaptcha_notice": "..."
  },
  "success": {
    "title": "...",
    "subtitle": "...",
    "email_sent": "...",
    "access_product": "...",
    "order_number": "..."
  },
  "common": {
    "from": "...",
    "per": "...",
    "month": "...",
    "year": "...",
    "week": "...",
    "day": "...",
    "apply": "...",
    "remove": "..."
  },
  "errors": {
    "invalid_date": "...",
    "invalid_expiration": "...",
    "invalid_coupon": "..."
  }
}
```

**Traduzir para os 3 idiomas:**
- `pt.json` - Português (Brasil)
- `en.json` - English (US)
- `es.json` - Español (España/LATAM)

---

### 3. Componentes Faltantes do Projeto Original ⭐⭐⭐

**Comparação: Original vs Migrado**

#### ✅ JÁ MIGRADOS
- CheckoutComponent
- CheckoutForm
- PaymentMethods
- CreditCardForm
- PixForm
- PixAutoForm
- BoletoForm
- PicPayForm
- ApplePayForm
- GooglePayForm
- NubankPayForm
- OrderSummary
- OrderItemResume
- CouponFormNew
- InstallmentsSelector
- PixPayment
- PixAutoPayment
- BoletoPayment
- PicPayPayment
- SuccessPayment
- WaitingPayment
- AddressForm
- BumpItem
- AboutCpf

#### ❌ FALTAM MIGRAR

**Checkout:**
- [ ] `CheckoutTitles.tsx` - Títulos do checkout
- [ ] `ProductComponent.tsx` - Componente de produto
- [ ] `ProductPurchaseComponent.tsx` - Componente de compra

**Componentes Comuns:**
- [ ] `EmailAutoComplete.tsx` - Autocomplete de email
- [ ] `ErrorModal.tsx` - Modal de erro
- [ ] `ErrorTopAlert.tsx` - Alerta de erro no topo
- [ ] `Toast.tsx` - Notificações toast
- [ ] `ServiceFeeTooltip.tsx` - Tooltip de taxa de serviço (migrar versão melhorada)
- [ ] `TextFieldPhoneNumber.tsx` - Campo de telefone com máscara

**Info:**
- [ ] `AboutPixSecurityAlert.tsx` - Alerta de segurança PIX

**Chats:** (Opcional - Prioridade Baixa)
- [ ] `Crisp.tsx`
- [ ] `CustomChatCard.tsx`
- [ ] `Facebook.tsx`
- [ ] `FreshChat.tsx`
- [ ] `Intercom.tsx`
- [ ] `JivoChat.tsx`
- [ ] `ManyChat.tsx`
- [ ] `Tawk.tsx`
- [ ] `Whatsapp.tsx`
- [ ] `Zendesk.tsx`

---

### 4. UX e Features Faltantes ⭐⭐⭐⭐

#### A. Loading States
- [ ] Loading skeleton nos componentes
- [ ] Loading ao buscar endereço
- [ ] Loading ao aplicar cupom
- [ ] Loading ao processar pagamento
- [ ] Shimmer effect nos cards

#### B. Error Handling
- [ ] Modal de erro global
- [ ] Toast notifications
- [ ] Alertas contextuais
- [ ] Retry logic

#### C. Validações de Formulário
- [ ] Validação de email
- [ ] Validação de CPF/CNPJ
- [ ] Validação de telefone
- [ ] Validação de cartão de crédito
- [ ] Validação de data de validade
- [ ] Validação de CVV
- [ ] Mensagens de erro traduzidas

#### D. Feedback Visual
- [ ] Animações de transição
- [ ] Hover states
- [ ] Focus states
- [ ] Success states
- [ ] Error states

#### E. Autocomplete e Mascaras
- [ ] Autocomplete de email (@gmail.com, @hotmail.com, etc)
- [ ] Máscara de telefone adaptativa (8/9 dígitos)
- [ ] Máscara de CPF/CNPJ adaptativa
- [ ] Máscara de cartão de crédito por bandeira
- [ ] Máscara de CVV (3/4 dígitos)

#### F. Acessibilidade
- [ ] Labels corretos em todos os campos
- [ ] ARIA labels
- [ ] Tab navigation
- [ ] Screen reader support
- [ ] Focus visible

---

### 5. Otimizações e Melhorias ⭐⭐⭐

#### A. Performance
- [ ] Code splitting dos formulários de pagamento
- [ ] Lazy load do QR Code
- [ ] Lazy load do Confetti
- [ ] Image optimization (Next/Image)
- [ ] Bundle analysis

#### B. SEO e Meta Tags
- [ ] Metadata dinâmico por produto
- [ ] Open Graph tags
- [ ] Twitter Card
- [ ] Canonical URLs
- [ ] Structured data (JSON-LD)

#### C. SSR/SSG
- [ ] Separar Server Components
- [ ] Adicionar Suspense boundaries
- [ ] Streaming SSR onde apropriado
- [ ] Static generation de páginas comuns

#### D. Analytics e Tracking
- [ ] Eventos de tracking completos
- [ ] Error tracking (Sentry/PostHog)
- [ ] Performance monitoring
- [ ] Conversion tracking

---

## 📝 Instruções de Implementação

### Passo 1: Expandir Mensagens i18n

```bash
# Editar os arquivos:
apps/web/src/lib/i18n/messages/pt.json
apps/web/src/lib/i18n/messages/en.json
apps/web/src/lib/i18n/messages/es.json
```

**Adicionar TODAS as mensagens necessárias** seguindo a estrutura proposta acima.

**Exemplo:**

```json
// pt.json
{
  "payment": {
    "credit_card": {
      "card_number": "Número do cartão",
      "card_number_placeholder": "0000 0000 0000 0000",
      "cardholder_name": "Nome no cartão",
      "expiration_date": "Validade",
      "cvv": "CVV"
    }
  }
}

// en.json
{
  "payment": {
    "credit_card": {
      "card_number": "Card number",
      "card_number_placeholder": "0000 0000 0000 0000",
      "cardholder_name": "Cardholder name",
      "expiration_date": "Expiration date",
      "cvv": "CVV"
    }
  }
}

// es.json
{
  "payment": {
    "credit_card": {
      "card_number": "Número de tarjeta",
      "card_number_placeholder": "0000 0000 0000 0000",
      "cardholder_name": "Nombre en la tarjeta",
      "expiration_date": "Fecha de vencimiento",
      "cvv": "CVV"
    }
  }
}
```

### Passo 2: Atualizar Types i18n

```typescript
// apps/web/src/types/i18n.ts
export type Messages = {
  checkout: {
    // ... existente
    order_summary: string;
    coupon_code: string;
    coupon_placeholder: string;
    // ... adicionar novas
  };
  payment: {
    payment_method_title: string;
    installments: string;
    one_time: string;
    credit_card: {
      card_number: string;
      card_number_placeholder: string;
      cardholder_name: string;
      // ... adicionar novas
    };
    pix: {
      instant_release: string;
      easy_to_use: string;
      // ... adicionar novas
    };
    // ... adicionar novas categorias
  };
  // ... adicionar novas categorias
};
```

### Passo 3: Atualizar Componentes para Usar i18n

**Exemplo: CheckoutComponent.tsx**

```typescript
"use client";

import { useTranslation } from "@/hooks/useTranslation";
// ... outros imports

export default function CheckoutComponent() {
  const { t } = useTranslation();
  const { offer, paying, creditCardError } = useCheckout();
  // ...

  return (
    <Card>
      <form onSubmit={handleSubmit}>
        {/* Título de Contato */}
        <h2 style={{ color: settings.text?.color.primary }}>
          {t("form.contact_info_title")}
        </h2>

        {/* Nome */}
        <TextField
          name="name"
          label={t("form.full_name")}
          placeholder={t("form.full_name_placeholder")}
        />

        {/* Email */}
        <TextField
          name="email"
          label={t("form.email")}
          placeholder={t("form.email_placeholder")}
          type="email"
        />

        {/* CPF */}
        <TextField
          name="cpf"
          label={t("form.cpf_cnpj")}
          placeholder={t("form.cpf_cnpj_placeholder")}
          cpfCnpj
        />

        {/* ... resto do código */}
      </form>
    </Card>
  );
}
```

**Exemplo: CreditCardForm.tsx**

```typescript
"use client";

import { useTranslation } from "@/hooks/useTranslation";
// ... outros imports

export const CreditCardForm = () => {
  const { t } = useTranslation();
  // ...

  return (
    <>
      <TextField
        name="cardNumber"
        label={t("payment.credit_card.card_number")}
        placeholder={t("payment.credit_card.card_number_placeholder")}
        // ...
      />

      <TextField
        name="cardholderName"
        label={t("payment.credit_card.cardholder_name")}
        placeholder={t("payment.credit_card.cardholder_name_placeholder")}
        // ...
      />

      <TextField
        name="cardExpiration"
        label={t("payment.credit_card.expiration_date")}
        placeholder={t("payment.credit_card.expiration_placeholder")}
        // ...
      />

      <TextField
        name="cardCvv"
        label={t("payment.credit_card.cvv")}
        placeholder={t("payment.credit_card.cvv_placeholder")}
        // ...
      />

      {/* ... resto do código */}
    </>
  );
};
```

### Passo 4: Migrar Componentes Faltantes

**Para cada componente faltante:**

1. Abrir o arquivo original em `_docs/cakto-checkout/src/components/`
2. Criar novo arquivo em `apps/web/src/components/`
3. Adicionar `"use client"` se necessário
4. Atualizar imports para usar path aliases (`@/`)
5. Substituir `import.meta.env` por helpers de `@/lib/env`
6. Adicionar SSR checks onde necessário
7. **SUBSTITUIR TODO TEXTO HARDCODED POR `t()`**
8. Testar funcionamento

**Exemplo: EmailAutoComplete.tsx**

```typescript
"use client";

import { useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import TextField from "@/components/ui/text-field";

const EMAIL_DOMAINS = [
  "@gmail.com",
  "@hotmail.com",
  "@outlook.com",
  "@yahoo.com",
];

export function EmailAutoComplete({ name = "email" }) {
  const { t } = useTranslation();
  const [suggestions, setSuggestions] = useState<string[]>([]);

  const handleChange = (value: string) => {
    if (value.includes("@")) {
      setSuggestions([]);
      return;
    }

    setSuggestions(EMAIL_DOMAINS.map(domain => value + domain));
  };

  return (
    <div className="relative">
      <TextField
        name={name}
        label={t("form.email")}
        placeholder={t("form.email_placeholder")}
        type="email"
        onChange={(e) => handleChange(e.target.value)}
      />
      
      {suggestions.length > 0 && (
        <div className="absolute z-10 w-full bg-white border rounded-md shadow-lg mt-1">
          {suggestions.map((suggestion) => (
            <div
              key={suggestion}
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => {
                // Implementar seleção
              }}
            >
              {suggestion}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
```

### Passo 5: Implementar Validações

**Criar validação helpers em `apps/web/src/lib/utils/validation.ts`:**

```typescript
import { useTranslation } from "@/hooks/useTranslation";

export function useValidation() {
  const { t } = useTranslation();

  return {
    email: (value: string) => {
      if (!value) return t("errors.required_field");
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) return t("errors.invalid_email");
      return true;
    },

    cpf: (value: string) => {
      if (!value) return t("errors.required_field");
      // Implementar validação de CPF
      return true;
    },

    phone: (value: string) => {
      if (!value) return t("errors.required_field");
      const phoneRegex = /^\(\d{2}\) \d{4,5}-\d{4}$/;
      if (!phoneRegex.test(value)) return t("errors.invalid_phone");
      return true;
    },

    cardNumber: (value: string) => {
      if (!value) return t("errors.required_field");
      const cleaned = value.replace(/\s/g, "");
      if (cleaned.length < 13) return t("errors.invalid_card_number");
      return true;
    },

    cardExpiration: (value: string) => {
      if (!value) return t("errors.required_field");
      const match = value.match(/^(0[1-9]|1[0-2])\/(\d{2})$/);
      if (!match) return t("errors.invalid_date");
      
      const month = parseInt(match[1], 10);
      const year = 2000 + parseInt(match[2], 10);
      const now = new Date();
      const y = now.getFullYear();
      const m = now.getMonth() + 1;
      
      if (year < y || (year === y && month < m)) {
        return t("errors.invalid_expiration");
      }
      
      return true;
    },

    cvv: (value: string, cardType: string) => {
      if (!value) return t("errors.required_field");
      const expectedLength = cardType === "amex" ? 4 : 3;
      if (value.length !== expectedLength) {
        return t("errors.invalid_cvv");
      }
      return true;
    },
  };
}
```

**Usar validações nos formulários:**

```typescript
import { useValidation } from "@/lib/utils/validation";

export function MyForm() {
  const validation = useValidation();
  
  const form = useForm({
    defaultValues: { email: "" },
  });

  return (
    <FormProvider {...form}>
      <TextField
        name="email"
        validate={validation.email}
        // ...
      />
    </FormProvider>
  );
}
```

### Passo 6: Implementar Error Handling

**Criar ErrorBoundary:**

```typescript
// apps/web/src/components/common/error-boundary.tsx
"use client";

import { Component, ReactNode } from "react";
import { useTranslation } from "@/hooks/useTranslation";

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error("ErrorBoundary caught an error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }

    return this.props.children;
  }
}

function ErrorFallback({ error }: { error?: Error }) {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <h1 className="text-2xl font-bold mb-4">{t("errors.generic_error")}</h1>
      <p className="text-gray-600 mb-4">{error?.message}</p>
      <button
        onClick={() => window.location.reload()}
        className="px-4 py-2 bg-blue-500 text-white rounded-md"
      >
        {t("common.retry")}
      </button>
    </div>
  );
}
```

**Criar Toast:**

```typescript
// apps/web/src/components/common/toast.tsx
"use client";

import { useEffect, useState } from "react";
import { createPortal } from "react-dom";

export function Toast({ message, type = "success", duration = 3000 }) {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setVisible(false), duration);
    return () => clearTimeout(timer);
  }, [duration]);

  if (!visible || typeof window === "undefined") return null;

  return createPortal(
    <div className={`fixed top-4 right-4 px-4 py-2 rounded-md shadow-lg ${
      type === "success" ? "bg-green-500" : "bg-red-500"
    } text-white`}>
      {message}
    </div>,
    document.body
  );
}
```

### Passo 7: Implementar Loading States

**Criar Skeleton:**

```typescript
// apps/web/src/components/ui/skeleton.tsx
export function Skeleton({ className = "" }) {
  return (
    <div className={`animate-pulse bg-gray-200 rounded ${className}`} />
  );
}
```

**Usar nos componentes:**

```typescript
import { Skeleton } from "@/components/ui/skeleton";

export function OrderSummary() {
  const { offer, isFetching } = useCheckout();

  if (isFetching) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-8 w-full" />
      </div>
    );
  }

  return (
    // ... conteúdo normal
  );
}
```

---

## 🧪 Testes

### Checklist de Validação

#### i18n Completo
- [ ] NENHUM texto hardcoded em português
- [ ] Todas as mensagens em pt.json, en.json, es.json
- [ ] Trocar idioma no header funciona corretamente
- [ ] Todos os textos mudam de idioma
- [ ] Placeholders traduzidos
- [ ] Labels traduzidos
- [ ] Mensagens de erro traduzidas
- [ ] Botões traduzidos

#### Componentes
- [ ] Todos os componentes faltantes migrados
- [ ] Todos os componentes usando `useTranslation()`
- [ ] Todos os componentes com "use client" quando necessário
- [ ] Todos os imports usando @/
- [ ] SSR checks em lugares apropriados

#### UX
- [ ] Loading states funcionando
- [ ] Error handling funcionando
- [ ] Toast notifications funcionando
- [ ] Validações de formulário funcionando
- [ ] Mensagens de validação traduzidas
- [ ] Autocomplete de email funcionando
- [ ] Máscaras de input funcionando

#### Linter
- [ ] Zero erros de linter
- [ ] Zero erros de TypeScript
- [ ] Zero warnings (se possível)

### Testar Fluxo Completo

1. **Testar em 3 idiomas:**
   ```bash
   # Português
   http://localhost:3001/pt/7rohg3i
   
   # Inglês
   http://localhost:3001/en/7rohg3i
   
   # Espanhol
   http://localhost:3001/es/7rohg3i
   ```

2. **Verificar troca de idioma:**
   - Abrir em qualquer idioma
   - Clicar no seletor de idioma
   - Selecionar outro idioma
   - Verificar que TODOS os textos mudaram
   - Recarregar página e verificar persistência

3. **Testar formulários:**
   - Preencher todos os campos
   - Validar erros de validação
   - Verificar que erros estão traduzidos
   - Testar autocomplete de email
   - Testar máscaras de input

4. **Testar pagamento:**
   - Selecionar cada método de pagamento
   - Verificar que formulário está traduzido
   - Testar envio de pagamento
   - Verificar loading states
   - Verificar mensagens de sucesso/erro

5. **Testar componentes de builder:**
   - Abrir checkout com builder configurado
   - Verificar que todos os componentes aparecem
   - Verificar que textos estão traduzidos

---

## 📊 Métricas de Sucesso

### Obrigatório
- [ ] **100% dos textos traduzidos** (0 textos hardcoded)
- [ ] **100% dos componentes migrados**
- [ ] **0 erros de linter**
- [ ] **0 erros de TypeScript**
- [ ] **3 idiomas funcionando** (pt, en, es)

### Desejável
- [ ] **Lighthouse score > 85**
- [ ] **Bundle size < 500KB (gzipped)**
- [ ] **FCP < 1.5s**
- [ ] **TTI < 3s**
- [ ] **0 console warnings**

---

## 🚀 Entrega

### O que deve estar 100% funcional:

1. ✅ **Sistema de i18n completo**
   - Todas as mensagens traduzidas
   - Nenhum texto hardcoded
   - Troca de idioma funcionando perfeitamente

2. ✅ **Todos os componentes migrados**
   - Paridade completa com projeto original
   - Todos os componentes usando i18n
   - UX idêntica ao original

3. ✅ **Validações e Error Handling**
   - Validações de formulário
   - Mensagens de erro traduzidas
   - Error boundary
   - Toast notifications

4. ✅ **Loading States**
   - Skeletons
   - Loading indicators
   - Feedback visual

5. ✅ **Qualidade do código**
   - Zero erros de linter
   - Zero erros de TypeScript
   - Todos os imports usando @/
   - SSR-safe

---

## 📚 Referências

### Arquivos Importantes

**Projeto Original (Referência):**
- `_docs/cakto-checkout/src/components/` - Todos os componentes
- `_docs/cakto-checkout/src/utils/` - Utilitários
- `_docs/cakto-checkout/src/services/` - Serviços

**Projeto Novo (Next.js):**
- `apps/web/src/components/` - Componentes migrados
- `apps/web/src/lib/` - Utilitários e serviços
- `apps/web/src/hooks/` - Hooks customizados
- `apps/web/src/types/` - Types TypeScript

**Documentação:**
- `STATUS_FASE_6_COMPLETO.md` - Status anterior
- `.cursorrules` - Regras do projeto
- `ANALISE_GAPS_E_MELHORIAS.md` - Análise completa

---

## ⚡ Começar Agora

### Ordem de Implementação Sugerida:

1. **Expandir mensagens i18n** (30 min)
   - Adicionar todas as mensagens aos arquivos pt/en/es.json
   - Atualizar types i18n

2. **Atualizar componentes principais** (2h)
   - CheckoutComponent
   - CreditCardForm
   - OrderSummary
   - PixForm, BoletoForm, etc.

3. **Migrar componentes faltantes** (3h)
   - EmailAutoComplete
   - ErrorModal
   - Toast
   - Outros componentes comuns

4. **Implementar validações** (1h)
   - Criar helpers de validação
   - Adicionar validações aos formulários

5. **Implementar error handling** (1h)
   - ErrorBoundary
   - Toast notifications
   - Error messages

6. **Implementar loading states** (1h)
   - Skeletons
   - Loading indicators

7. **Testar tudo** (1h)
   - Testar em 3 idiomas
   - Testar todos os fluxos
   - Verificar linter

**Tempo total estimado:** 9-10 horas

---

## ✅ Critérios de Aceitação

### Para considerar a Fase 7 CONCLUÍDA:

1. ✅ **ZERO textos hardcoded em português**
2. ✅ **TODAS as mensagens traduzidas em pt/en/es**
3. ✅ **TODOS os componentes migrados**
4. ✅ **ZERO erros de linter/TypeScript**
5. ✅ **Troca de idioma funciona perfeitamente**
6. ✅ **UX idêntica ao projeto original**
7. ✅ **Validações de formulário funcionando**
8. ✅ **Error handling implementado**
9. ✅ **Loading states implementados**
10. ✅ **Testes manuais passando em todos os fluxos**

---

**Última atualização:** 10/11/2025  
**Versão:** 1.0.0  
**Prioridade:** CRÍTICA ⭐⭐⭐⭐⭐  
**Status:** PRONTO PARA EXECUÇÃO


