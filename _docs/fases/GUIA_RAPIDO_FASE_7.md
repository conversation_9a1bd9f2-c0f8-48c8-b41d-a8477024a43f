# Guia Rápido - Fase 7: i18n Completo + UX

## 🚀 Quick Start

```bash
# 1. Ir para o diretório do projeto
cd /Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2

# 2. Instalar dependências (se necessário)
pnpm install

# 3. Rodar em desenvolvimento
pnpm dev:web

# 4. Acessar checkout de teste
# Português: http://localhost:3001/pt/7rohg3i
# Inglês:    http://localhost:3001/en/7rohg3i
# Espanhol:  http://localhost:3001/es/7rohg3i
```

---

## 📍 Arquivos Principais

### Mensagens i18n
```
apps/web/src/lib/i18n/messages/
├── pt.json  ← Adicionar novas mensagens aqui
├── en.json  ← Adicionar traduções em inglês
└── es.json  ← Adicionar traduções em espanhol
```

### Types i18n
```
apps/web/src/types/i18n.ts  ← Adicionar novos tipos aqui
```

### Componentes a atualizar
```
apps/web/src/components/
├── checkout/
│   └── CheckoutComponent.tsx         ← Muitos textos hardcoded
├── payments/
│   ├── CreditCardForm.tsx            ← Muitos textos hardcoded
│   ├── PixForm.tsx                   ← Textos hardcoded
│   ├── PixAutoForm.tsx
│   ├── BoletoForm.tsx
│   ├── OrderSummary.tsx              ← Muitos textos hardcoded
│   ├── OrderItemResume.tsx
│   ├── CouponFormNew.tsx
│   ├── WaitingPayment.tsx
│   ├── PixPayment.tsx                ← Textos hardcoded
│   ├── BoletoPayment.tsx
│   └── SuccessPayment.tsx
└── builder/
    └── (vários componentes)
```

---

## 🔍 Como Encontrar Textos Hardcoded

### Buscar textos em português
```bash
# Buscar strings com letras maiúsculas (possíveis textos hardcoded)
grep -r '"[A-Z][a-zàáãâéêíóôõúç]' apps/web/src/components/

# Buscar palavras específicas comuns
grep -r '"Informações' apps/web/src/components/
grep -r '"Nome' apps/web/src/components/
grep -r '"Email' apps/web/src/components/
grep -r '"CPF' apps/web/src/components/
grep -r '"Cartão' apps/web/src/components/
grep -r '"Pagamento' apps/web/src/components/

# Buscar por placeholders
grep -r 'placeholder="' apps/web/src/components/

# Buscar por labels
grep -r 'label="' apps/web/src/components/
```

---

## ✏️ Como Adicionar Nova Tradução

### 1. Adicionar mensagem aos arquivos JSON

**pt.json:**
```json
{
  "payment": {
    "credit_card": {
      "card_number": "Número do cartão",
      "card_number_placeholder": "0000 0000 0000 0000"
    }
  }
}
```

**en.json:**
```json
{
  "payment": {
    "credit_card": {
      "card_number": "Card number",
      "card_number_placeholder": "0000 0000 0000 0000"
    }
  }
}
```

**es.json:**
```json
{
  "payment": {
    "credit_card": {
      "card_number": "Número de tarjeta",
      "card_number_placeholder": "0000 0000 0000 0000"
    }
  }
}
```

### 2. Adicionar tipo em i18n.ts

```typescript
// apps/web/src/types/i18n.ts
export type Messages = {
  payment: {
    credit_card: {
      card_number: string;
      card_number_placeholder: string;
    };
  };
};
```

### 3. Usar no componente

```typescript
// apps/web/src/components/payments/CreditCardForm.tsx
"use client";

import { useTranslation } from "@/hooks/useTranslation";
import TextField from "@/components/ui/text-field";

export const CreditCardForm = () => {
  const { t } = useTranslation();

  return (
    <TextField
      name="cardNumber"
      label={t("payment.credit_card.card_number")}
      placeholder={t("payment.credit_card.card_number_placeholder")}
    />
  );
};
```

---

## 🧪 Como Testar

### Testar troca de idioma
```bash
# 1. Abrir checkout em português
http://localhost:3001/pt/7rohg3i

# 2. Abrir DevTools Console (F12)

# 3. Clicar no seletor de idioma no header (canto superior direito)

# 4. Selecionar "🇺🇸 English"

# 5. Verificar que TODOS os textos mudaram para inglês

# 6. Verificar que URL mudou para /en/7rohg3i

# 7. Recarregar página (F5)

# 8. Verificar que idioma persistiu (ainda em inglês)
```

### Testar componente específico
```bash
# 1. Abrir componente no código
code apps/web/src/components/payments/CreditCardForm.tsx

# 2. Buscar por textos hardcoded (entre aspas)
# Exemplo: "Número do cartão"

# 3. Substituir por t("payment.credit_card.card_number")

# 4. Verificar no navegador

# 5. Trocar idioma e verificar tradução
```

### Buscar por componentes não traduzidos
```bash
# Buscar por textos em português que não estão usando t()
grep -r '"[A-ZÀÁÃÂÉÊÍÓÔÕÚÇ]' apps/web/src/components/ | grep -v "t("

# Buscar por placeholders hardcoded
grep -r 'placeholder="[A-Z]' apps/web/src/components/
```

---

## 🔧 Comandos Úteis

### Desenvolvimento
```bash
# Rodar servidor de desenvolvimento
pnpm dev:web

# Rodar linter
pnpm --filter web run lint

# Rodar linter e corrigir automaticamente
pnpm --filter web run lint --fix

# Build para verificar erros
pnpm --filter web run build
```

### Verificar Erros
```bash
# Ver erros de TypeScript
pnpm --filter web run build 2>&1 | grep "error TS"

# Ver erros de linter
pnpm --filter web run lint 2>&1 | grep "error"

# Ver warnings
pnpm --filter web run lint 2>&1 | grep "warning"
```

### Buscar e Substituir
```bash
# Buscar arquivo específico
find apps/web/src -name "CreditCardForm.tsx"

# Buscar por texto em arquivos
grep -r "Número do cartão" apps/web/src/

# Contar quantos textos hardcoded ainda existem
grep -r '"[A-ZÀÁÃÂÉÊÍÓÔÕÚÇ]' apps/web/src/components/ | wc -l
```

---

## 📊 Progresso Rápido

### Contar mensagens traduzidas
```bash
# Contar linhas em cada arquivo
wc -l apps/web/src/lib/i18n/messages/pt.json
wc -l apps/web/src/lib/i18n/messages/en.json
wc -l apps/web/src/lib/i18n/messages/es.json

# Contar chaves no JSON
cat apps/web/src/lib/i18n/messages/pt.json | grep -o '"[a-z_]*":' | wc -l
```

### Verificar componentes sem i18n
```bash
# Listar componentes que ainda têm texto hardcoded
grep -l '"[A-ZÀÁÃÂÉÊÍÓÔÕÚÇ]' apps/web/src/components/**/*.tsx
```

---

## ⚠️ Problemas Comuns

### 1. "t is not defined"
**Erro:** `ReferenceError: t is not defined`

**Solução:**
```typescript
// Adicionar import
import { useTranslation } from "@/hooks/useTranslation";

// Usar hook
const { t } = useTranslation();
```

### 2. "Cannot find module '@/hooks/useTranslation'"
**Erro:** `Module not found: Can't resolve '@/hooks/useTranslation'`

**Solução:**
```bash
# Verificar se arquivo existe
ls apps/web/src/hooks/useTranslation.ts

# Se não existir, criar
```

### 3. "Property 'X' does not exist on type 'Messages'"
**Erro:** TypeScript reclama que a chave não existe

**Solução:**
```typescript
// Adicionar tipo em apps/web/src/types/i18n.ts
export type Messages = {
  payment: {
    credit_card: {
      card_number: string; // ← Adicionar aqui
    };
  };
};
```

### 4. Tradução não aparece
**Problema:** `t("payment.card_number")` retorna `"payment.card_number"` em vez do texto

**Solução:**
1. Verificar que mensagem existe em pt.json/en.json/es.json
2. Verificar que caminho está correto (com ponto)
3. Verificar que não há typo no caminho
4. Recarregar página

### 5. "window is not defined"
**Erro:** SSR error: `window is not defined`

**Solução:**
```typescript
// Adicionar check
if (typeof window === 'undefined') return null;

// Ou adicionar "use client" no topo
"use client";
```

---

## 🎯 Ordem de Execução Recomendada

### Fase 1: Preparação (30 min)
1. Expandir pt.json com todas as mensagens
2. Expandir en.json com todas as traduções
3. Expandir es.json com todas as traduções
4. Atualizar types i18n

### Fase 2: Componentes Principais (2h)
1. CheckoutComponent.tsx
2. CreditCardForm.tsx
3. OrderSummary.tsx
4. PixForm.tsx
5. BoletoForm.tsx

### Fase 3: Componentes de Status (1h)
1. WaitingPayment.tsx
2. PixPayment.tsx
3. BoletoPayment.tsx
4. SuccessPayment.tsx

### Fase 4: Componentes Restantes (2h)
1. OrderItemResume.tsx
2. CouponFormNew.tsx
3. InstallmentsSelector.tsx
4. Outros componentes de payments/

### Fase 5: Componentes de Builder (1h)
1. CheckoutComponentText.tsx
2. CheckoutComponentHeader.tsx
3. Outros componentes de builder/

### Fase 6: Componentes Faltantes (2h)
1. EmailAutoComplete.tsx
2. ErrorModal.tsx
3. Toast.tsx
4. Outros componentes comuns

### Fase 7: Validações e UX (1h)
1. Criar useValidation hook
2. Adicionar validações
3. Adicionar loading states
4. Adicionar error handling

### Fase 8: Testes (1h)
1. Testar em 3 idiomas
2. Testar todos os fluxos
3. Verificar linter
4. Verificar build

---

## 📝 Template de Componente

### Componente básico com i18n

```typescript
"use client";

import { useTranslation } from "@/hooks/useTranslation";
import { useCheckout } from "@/contexts";
import useSettings from "@/hooks/useSettings";
import TextField from "@/components/ui/text-field";
import Button from "@/components/ui/button-form";

export function MeuComponente() {
  const { t } = useTranslation();
  const { offer, paying } = useCheckout();
  const settings = useSettings();

  return (
    <div>
      <h2 style={{ color: settings.text?.color.primary }}>
        {t("my_component.title")}
      </h2>

      <TextField
        name="email"
        label={t("form.email")}
        placeholder={t("form.email_placeholder")}
        type="email"
      />

      <Button loading={paying}>
        {t("common.continue")}
      </Button>
    </div>
  );
}
```

---

## 🎉 Quando Considerar Concluído

### Checklist Final
- [ ] Zero textos hardcoded em português
- [ ] Zero textos hardcoded em inglês (fora do i18n)
- [ ] Todas as mensagens em pt/en/es.json
- [ ] Todos os componentes usando `t()`
- [ ] Troca de idioma funciona perfeitamente
- [ ] Zero erros de linter
- [ ] Zero erros de TypeScript
- [ ] Build funcionando
- [ ] Testes manuais OK em 3 idiomas

### Comandos de Validação Final
```bash
# 1. Verificar textos hardcoded
grep -r '"[A-ZÀÁÃÂÉÊÍÓÔÕÚÇ]' apps/web/src/components/ | grep -v "t(" | wc -l
# Resultado esperado: 0

# 2. Verificar linter
pnpm --filter web run lint
# Resultado esperado: 0 errors

# 3. Verificar build
pnpm --filter web run build
# Resultado esperado: Success

# 4. Testar em 3 idiomas
# Abrir /pt/7rohg3i, /en/7rohg3i, /es/7rohg3i
# Resultado esperado: Tudo traduzido corretamente
```

---

## 🆘 Precisa de Ajuda?

### Documentos de Referência
- `PROMPT_FASE_7_I18N_COMPLETO_UX.md` - Prompt completo
- `STATUS_FASE_7_CHECKLIST.md` - Checklist detalhado
- `STATUS_FASE_6_COMPLETO.md` - Status anterior
- `.cursorrules` - Regras do projeto

### Comandos de Debug
```bash
# Ver logs do servidor
pnpm dev:web

# Ver erros no console do navegador
# Abrir DevTools (F12) → Console

# Ver requisições de rede
# Abrir DevTools (F12) → Network

# Ver estado do React
# Instalar React DevTools extension
```

---

**Última atualização:** 10/11/2025  
**Versão:** 1.0.0  
**Próximo passo:** Expandir mensagens i18n


