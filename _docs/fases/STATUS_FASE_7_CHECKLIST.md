# Status da Fase 7: Checklist de Execução

## 🎯 Objetivo
Remover TODOS os textos hardcoded e completar a migração do checkout com UX idêntica ao original.

---

## 📋 Checklist de Execução

### 1. Expans<PERSON> das Mensagens i18n ⭐⭐⭐⭐⭐

#### pt.json, en.json, es.json
- [ ] `form.contact_info_title`
- [ ] `form.full_name`
- [ ] `form.full_name_placeholder`
- [ ] `form.email`
- [ ] `form.email_placeholder`
- [ ] `form.cpf_cnpj`
- [ ] `form.cpf_cnpj_placeholder`
- [ ] `form.phone`
- [ ] `form.phone_placeholder`

#### payment.credit_card.*
- [ ] `card_number`
- [ ] `card_number_placeholder`
- [ ] `cardholder_name`
- [ ] `cardholder_name_placeholder`
- [ ] `expiration_date`
- [ ] `expiration_placeholder`
- [ ] `cvv`
- [ ] `cvv_placeholder`
- [ ] `save_card`

#### payment.pix.*
- [ ] `instant_release`
- [ ] `easy_to_use`
- [ ] `qrcode_title`
- [ ] `scan_qrcode`
- [ ] `or_copy_code`
- [ ] `copy_code`
- [ ] `code_copied`
- [ ] `scan_instruction`
- [ ] `open_bank_app`
- [ ] `scan_code`
- [ ] `confirm_payment`

#### payment.pix_auto.*
- [ ] `title`
- [ ] `description`
- [ ] `advantages` (array)

#### payment.boleto.*
- [ ] `payer_document`
- [ ] `barcode_title`
- [ ] `copy_code`
- [ ] `code_copied`
- [ ] `print`
- [ ] `pay_at_bank`
- [ ] `due_date`

#### payment.picpay.*
- [ ] `title`
- [ ] `scan_qrcode`

#### checkout.*
- [ ] `order_summary`
- [ ] `coupon_code`
- [ ] `coupon_placeholder`
- [ ] `coupon_applied`
- [ ] `coupon_success`
- [ ] `service_fee`
- [ ] `service_fee_tooltip`
- [ ] `total`
- [ ] `secure_purchase`
- [ ] `recaptcha_notice`

#### success.*
- [ ] `title`
- [ ] `subtitle`
- [ ] `email_sent`
- [ ] `access_product`
- [ ] `order_number`

#### common.*
- [ ] `from`
- [ ] `per`
- [ ] `month`
- [ ] `year`
- [ ] `week`
- [ ] `day`
- [ ] `apply`
- [ ] `remove`

#### errors.*
- [ ] `invalid_date`
- [ ] `invalid_expiration`
- [ ] `invalid_coupon`
- [ ] `invalid_email`
- [ ] `invalid_phone`
- [ ] `invalid_card_number`
- [ ] `invalid_cvv`
- [ ] `required_field`

#### Atualizar Types
- [ ] `apps/web/src/types/i18n.ts` - Adicionar novos tipos

---

### 2. Atualizar Componentes para i18n ⭐⭐⭐⭐⭐

#### Componentes de Checkout
- [ ] `CheckoutComponent.tsx`
  - [ ] "Informações de Contato"
  - [ ] "Nome completo"
  - [ ] "Email"
  - [ ] "CPF/CNPJ"
  - [ ] "Forma de Pagamento"
  - [ ] "Compra 100% segura"
  - [ ] "Este site é protegido pelo reCAPTCHA"

#### Componentes de Pagamento
- [ ] `CreditCardForm.tsx`
  - [ ] "Número do cartão"
  - [ ] "Nome no cartão"
  - [ ] "Validade"
  - [ ] "CVV"
  - [ ] "Salvar cartão"
  - [ ] "Digite uma data válida"
  - [ ] "Data inválida"

- [ ] `PixForm.tsx`
  - [ ] "Liberação imediata"
  - [ ] "É simples, só usar o aplicativo..."

- [ ] `PixAutoForm.tsx`
  - [ ] Vantagens do PIX Auto

- [ ] `BoletoForm.tsx`
  - [ ] "CPF/CNPJ do Pagador"
  - [ ] Outras mensagens

- [ ] `PicPayForm.tsx`
  - [ ] Mensagens do PicPay

- [ ] `OrderSummary.tsx`
  - [ ] "Resumo do pedido"
  - [ ] "Cupom aplicado"
  - [ ] "Taxa de serviço"
  - [ ] "Total"

- [ ] `OrderItemResume.tsx`
  - [ ] "à vista"
  - [ ] "de"
  - [ ] "por"
  - [ ] "mês", "ano", "semana", "dia"

- [ ] `CouponFormNew.tsx`
  - [ ] "Cupom de desconto"
  - [ ] "Digite seu cupom"
  - [ ] "Aplicar"
  - [ ] "Remover"
  - [ ] Mensagens de sucesso/erro

- [ ] `InstallmentsSelector.tsx`
  - [ ] "Parcelas"
  - [ ] "à vista"

#### Componentes de Status
- [ ] `WaitingPayment.tsx`
  - [ ] "Aguardando pagamento"
  - [ ] "Verificando pagamento..."
  - [ ] "Verificar pagamento"

- [ ] `PixPayment.tsx`
  - [ ] "QR Code PIX"
  - [ ] "Copiar código"
  - [ ] "Código copiado!"
  - [ ] "Escanear QR Code"
  - [ ] "Abra o app do seu banco"
  - [ ] "Escaneie o código"
  - [ ] "Confirme o pagamento"

- [ ] `PixAutoPayment.tsx`
  - [ ] Mensagens do PIX Auto

- [ ] `PixAutoSuccessPayment.tsx`
  - [ ] Mensagens de sucesso

- [ ] `BoletoPayment.tsx`
  - [ ] "Código de barras do boleto"
  - [ ] "Copiar código"
  - [ ] "Imprimir boleto"
  - [ ] "Pagar no banco"
  - [ ] "Data de vencimento"

- [ ] `PicPayPayment.tsx`
  - [ ] Mensagens do PicPay

- [ ] `SuccessPayment.tsx`
  - [ ] "Pagamento confirmado!"
  - [ ] "Seu pagamento foi aprovado"
  - [ ] "Você receberá um email"
  - [ ] "Acessar produto"
  - [ ] "Número do pedido"

#### Componentes de Builder
- [ ] `CheckoutComponentText.tsx`
- [ ] `CheckoutComponentHeader.tsx`
- [ ] `CheckoutComponentTestimonial.tsx`
- [ ] `CheckoutComponentList.tsx`
- [ ] `CheckoutComponentAdvantage.tsx`
- [ ] `CheckoutComponentSeal.tsx`
- [ ] `CheckoutComponentCountdown.tsx`

---

### 3. Migrar Componentes Faltantes ⭐⭐⭐

#### Componentes de Checkout
- [ ] `CheckoutTitles.tsx`
- [ ] `ProductComponent.tsx`
- [ ] `ProductPurchaseComponent.tsx`

#### Componentes Comuns
- [ ] `EmailAutoComplete.tsx`
  - [ ] Criar componente
  - [ ] Implementar sugestões de email
  - [ ] Traduzir mensagens
  - [ ] Testar funcionamento

- [ ] `ErrorModal.tsx`
  - [ ] Criar componente
  - [ ] Traduzir mensagens
  - [ ] Testar funcionamento

- [ ] `ErrorTopAlert.tsx`
  - [ ] Criar componente
  - [ ] Traduzir mensagens
  - [ ] Testar funcionamento

- [ ] `Toast.tsx` (melhorado)
  - [ ] Criar componente
  - [ ] Adicionar tipos (success, error, warning, info)
  - [ ] Adicionar animações
  - [ ] Testar funcionamento

- [ ] `ServiceFeeTooltip.tsx` (melhorado)
  - [ ] Migrar componente
  - [ ] Traduzir texto do tooltip
  - [ ] Testar funcionamento

- [ ] `TextFieldPhoneNumber.tsx`
  - [ ] Migrar componente
  - [ ] Máscara adaptativa (8/9 dígitos)
  - [ ] Traduzir placeholders
  - [ ] Testar funcionamento

#### Componentes de Info
- [ ] `AboutPixSecurityAlert.tsx`
  - [ ] Migrar componente
  - [ ] Traduzir mensagens
  - [ ] Testar funcionamento

---

### 4. Implementar UX Features ⭐⭐⭐⭐

#### A. Loading States
- [ ] Criar Skeleton component
- [ ] Adicionar loading em AddressForm (busca CEP)
- [ ] Adicionar loading em CouponForm (aplicar cupom)
- [ ] Adicionar loading em formulários de pagamento
- [ ] Adicionar shimmer effect nos cards

#### B. Error Handling
- [ ] Criar ErrorBoundary component
- [ ] Implementar Toast notifications
- [ ] Adicionar alertas contextuais
- [ ] Implementar retry logic

#### C. Validações
- [ ] Criar `useValidation()` hook
- [ ] Validação de email
- [ ] Validação de CPF/CNPJ
- [ ] Validação de telefone
- [ ] Validação de cartão de crédito
- [ ] Validação de data de validade
- [ ] Validação de CVV
- [ ] Traduzir mensagens de erro

#### D. Feedback Visual
- [ ] Animações de transição
- [ ] Hover states
- [ ] Focus states
- [ ] Success states
- [ ] Error states

#### E. Autocomplete e Máscaras
- [ ] Autocomplete de email funcionando
- [ ] Máscara de telefone adaptativa
- [ ] Máscara de CPF/CNPJ adaptativa
- [ ] Máscara de cartão por bandeira
- [ ] Máscara de CVV (3/4 dígitos)

#### F. Acessibilidade
- [ ] Labels corretos em todos os campos
- [ ] ARIA labels onde necessário
- [ ] Tab navigation funcionando
- [ ] Screen reader support
- [ ] Focus visible

---

### 5. Otimizações ⭐⭐⭐

#### A. Performance
- [ ] Code splitting dos formulários de pagamento
- [ ] Lazy load do QR Code
- [ ] Lazy load do Confetti
- [ ] Image optimization (Next/Image)
- [ ] Bundle analysis

#### B. SEO e Meta Tags
- [ ] Metadata dinâmico por produto
- [ ] Open Graph tags
- [ ] Twitter Card
- [ ] Canonical URLs
- [ ] Structured data (JSON-LD)

#### C. SSR/SSG
- [ ] Separar Server Components
- [ ] Adicionar Suspense boundaries
- [ ] Streaming SSR onde apropriado

#### D. Analytics e Tracking
- [ ] Eventos de tracking completos
- [ ] Error tracking (PostHog)
- [ ] Performance monitoring

---

## 🧪 Testes

### Testes de i18n
- [ ] Abrir checkout em português (/pt/7rohg3i)
- [ ] Verificar que não há texto hardcoded
- [ ] Trocar para inglês
- [ ] Verificar que TODOS os textos mudaram
- [ ] Trocar para espanhol
- [ ] Verificar que TODOS os textos mudaram
- [ ] Recarregar página e verificar persistência

### Testes de Formulários
- [ ] Preencher todos os campos
- [ ] Testar validações
- [ ] Verificar mensagens de erro em 3 idiomas
- [ ] Testar autocomplete de email
- [ ] Testar máscaras de input
- [ ] Testar busca de CEP

### Testes de Pagamento
- [ ] Testar PIX
- [ ] Testar PIX Auto
- [ ] Testar Boleto
- [ ] Testar PicPay
- [ ] Testar Cartão de Crédito
- [ ] Testar todos os loading states
- [ ] Testar mensagens de sucesso/erro

### Testes de Componentes
- [ ] Testar AddressForm
- [ ] Testar BumpItem
- [ ] Testar CouponForm
- [ ] Testar OrderSummary
- [ ] Testar todos os componentes de builder

### Testes de Error Handling
- [ ] Simular erro de rede
- [ ] Simular erro de validação
- [ ] Simular erro de pagamento
- [ ] Verificar que ErrorBoundary funciona
- [ ] Verificar que Toast aparece

### Testes de Qualidade
- [ ] Rodar linter (pnpm --filter web run lint)
- [ ] Verificar TypeScript (pnpm --filter web run build)
- [ ] Verificar bundle size
- [ ] Testar em mobile
- [ ] Testar em desktop
- [ ] Testar em diferentes navegadores

---

## ✅ Critérios de Aceitação

### Para considerar CONCLUÍDO:

1. ✅ **ZERO textos hardcoded**
   - [ ] Buscar por textos em português: `grep -r "Informações" apps/web/src/components/`
   - [ ] Buscar por textos em inglês hardcoded
   - [ ] Buscar por placeholders hardcoded
   - [ ] Verificar manualmente cada componente

2. ✅ **Todas as mensagens traduzidas**
   - [ ] pt.json completo
   - [ ] en.json completo
   - [ ] es.json completo
   - [ ] Types i18n atualizados

3. ✅ **Todos os componentes migrados**
   - [ ] Comparar com projeto original
   - [ ] Verificar paridade de features
   - [ ] Verificar UX idêntica

4. ✅ **Zero erros**
   - [ ] Zero erros de linter
   - [ ] Zero erros de TypeScript
   - [ ] Zero erros no console
   - [ ] Zero warnings críticos

5. ✅ **Troca de idioma perfeita**
   - [ ] Funciona no header
   - [ ] Persiste em cookie
   - [ ] Todos os textos mudam
   - [ ] URL atualiza corretamente

6. ✅ **UX completa**
   - [ ] Loading states
   - [ ] Error handling
   - [ ] Validações
   - [ ] Feedback visual
   - [ ] Autocomplete
   - [ ] Máscaras

7. ✅ **Qualidade**
   - [ ] Código limpo
   - [ ] Comentários onde necessário
   - [ ] Seguindo padrões do projeto
   - [ ] Performance adequada

---

## 📊 Progresso

**Última atualização:** 10/11/2025

### Componentes Migrados
- Total: 0/40
- Checkout: 0/10
- Pagamento: 0/20
- Builder: 0/7
- Comuns: 0/3

### Mensagens i18n
- Total: 0/150
- Form: 0/10
- Payment: 0/50
- Checkout: 0/20
- Success: 0/10
- Common: 0/10
- Errors: 0/10

### Testes
- i18n: 0/7
- Formulários: 0/6
- Pagamento: 0/6
- Componentes: 0/5
- Error Handling: 0/5
- Qualidade: 0/6

---

**Status:** 🔴 NÃO INICIADO  
**Prioridade:** CRÍTICA ⭐⭐⭐⭐⭐  
**Tempo estimado:** 9-10 horas


