# Fase 7 (Continuação) - Completar i18n e Implementar UX Avançada

**Data de Início:** 10/11/2025  
**Progresso Anterior:** 40% Completo  
**Objetivo:** Atingir 100% de paridade com projeto original e zero textos hardcoded

---

## 📋 Contexto e Progresso Anterior

### ✅ O Que JÁ Foi Feito (Fase 7 - Parte 1)

1. **Sistema i18n Expandido** ⭐⭐⭐⭐⭐
   - ✅ 300+ mensagens adicionadas em 3 idiomas (pt/en/es)
   - ✅ Types TypeScript atualizados com tipagem forte
   - ✅ Estrutura de mensagens bem organizada por categoria

2. **Componentes Principais Internacionalizados** ⭐⭐⭐⭐⭐
   - ✅ `CheckoutComponent.tsx` - 11 strings removidas
   - ✅ `OrderSummary.tsx` - 5 strings removidas
   - ✅ `CouponFormNew.tsx` - 6 strings removidas
   - ✅ `CreditCardForm.tsx` - 10 strings removidas
   - ✅ `PixForm.tsx` - 2 strings removidas
   - ✅ `BoletoForm.tsx` - 1 string removida
   
   **Total: 35 textos hardcoded removidos**

3. **Qualidade do Código**
   - ✅ Todos os imports usando `@/` paths
   - ✅ `useTranslation()` hook padronizado
   - ✅ Mensagens bem estruturadas
   - ✅ Traduções profissionais

### 📊 Status Atual: 40% Completo

**Arquivos de Referência:**
- `STATUS_FASE_7_PROGRESSO.md` - Progresso detalhado
- `PROMPT_FASE_7_I18N_COMPLETO_UX.md` - Prompt original
- `_docs/fases/STATUS_FASE_7_CHECKLIST.md` - Checklist completo

---

## 🎯 Objetivos da Continuação

### 1. PRIORIDADE ALTA: Componentes de Status ⭐⭐⭐⭐⭐

**Componentes a internacionalizar:**

#### WaitingPayment.tsx
Textos a remover:
- "Aguardando pagamento"
- "Escaneie o QR Code"
- "Ou copie o código"
- "Verificando pagamento..."
- "Verificar pagamento"

#### SuccessPayment.tsx
Textos a remover:
- "Pagamento confirmado!"
- "Seu pedido foi confirmado com sucesso"
- "Você receberá um email com os detalhes"
- "Acessar produto"
- "Número do pedido"
- "Obrigado pela sua compra!"

#### PixPayment.tsx
Textos a remover:
- "QR Code PIX"
- "Copiar código"
- "Código copiado!"
- "Escaneie o QR Code"
- "Abra o app do seu banco"
- "Escaneie o código"
- "Confirme o pagamento"

#### PixAutoPayment.tsx
Textos a remover:
- Mensagens específicas do PIX Auto
- Vantagens e instruções

#### BoletoPayment.tsx
Textos a remover:
- "Código de barras do boleto"
- "Copiar código"
- "Código copiado!"
- "Imprimir boleto"
- "Pagar no banco"
- "Data de vencimento"

#### PicPayPayment.tsx
Textos a remover:
- Mensagens do PicPay

---

### 2. PRIORIDADE ALTA: Componentes Faltantes ⭐⭐⭐⭐

#### A. Toast Component
Criar componente de notificação toast com i18n desde o início:

```typescript
// apps/web/src/components/common/toast.tsx
"use client";

import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { useTranslation } from "@/hooks/useTranslation";

type ToastType = "success" | "error" | "info" | "warning";

interface ToastProps {
	message: string;
	type?: ToastType;
	duration?: number;
	onClose?: () => void;
}

export function Toast({ message, type = "success", duration = 3000, onClose }: ToastProps) {
	const [visible, setVisible] = useState(true);
	const { t } = useTranslation();

	useEffect(() => {
		const timer = setTimeout(() => {
			setVisible(false);
			onClose?.();
		}, duration);
		return () => clearTimeout(timer);
	}, [duration, onClose]);

	if (!visible || typeof window === "undefined") return null;

	const colors = {
		success: "bg-green-500",
		error: "bg-red-500",
		info: "bg-blue-500",
		warning: "bg-yellow-500",
	};

	return createPortal(
		<div
			className={`fixed top-4 right-4 px-4 py-3 rounded-md shadow-lg ${colors[type]} text-white animate-slide-in-right z-50`}
		>
			<div className="flex items-center gap-2">
				{type === "success" && "✓"}
				{type === "error" && "✕"}
				{type === "info" && "ℹ"}
				{type === "warning" && "⚠"}
				<span>{message}</span>
			</div>
		</div>,
		document.body
	);
}

// Hook para usar toast
export function useToast() {
	const { t } = useTranslation();
	const [toasts, setToasts] = useState<ToastProps[]>([]);

	const showToast = (message: string, type: ToastType = "success") => {
		setToasts((prev) => [...prev, { message, type }]);
	};

	return {
		showToast,
		showSuccess: (message: string) => showToast(message, "success"),
		showError: (message: string) => showToast(message, "error"),
		showInfo: (message: string) => showToast(message, "info"),
		showWarning: (message: string) => showToast(message, "warning"),
		toasts,
	};
}
```

**Mensagens a adicionar ao i18n:**
```json
{
  "toast": {
    "success": "Sucesso!",
    "error": "Erro!",
    "info": "Informação",
    "warning": "Atenção"
  }
}
```

#### B. ErrorBoundary Component
Criar error boundary com i18n:

```typescript
// apps/web/src/components/common/error-boundary.tsx
"use client";

import { Component, ReactNode } from "react";
import Button from "@/components/ui/button-form";

interface Props {
	children: ReactNode;
	fallback?: ReactNode;
}

interface State {
	hasError: boolean;
	error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError(error: Error) {
		return { hasError: true, error };
	}

	componentDidCatch(error: Error, errorInfo: any) {
		console.error("ErrorBoundary caught:", error, errorInfo);
	}

	render() {
		if (this.state.hasError) {
			return this.props.fallback || <ErrorFallback error={this.state.error} />;
		}

		return this.props.children;
	}
}

function ErrorFallback({ error }: { error?: Error }) {
	// Note: useTranslation precisa ser usado em um componente funcional separado
	return (
		<div className="flex flex-col items-center justify-center min-h-screen p-4">
			<div className="text-center max-w-md">
				<h1 className="text-2xl font-bold mb-4 text-red-600">
					Algo deu errado
				</h1>
				<p className="text-gray-600 mb-4">
					{error?.message || "Ocorreu um erro inesperado"}
				</p>
				<Button onClick={() => window.location.reload()}>
					Tentar Novamente
				</Button>
			</div>
		</div>
	);
}
```

**Usar no layout:**
```typescript
// apps/web/src/app/[locale]/[id]/layout.tsx
import { ErrorBoundary } from "@/components/common/error-boundary";

export default function CheckoutLayout({ children }: { children: ReactNode }) {
	return (
		<ErrorBoundary>
			{children}
		</ErrorBoundary>
	);
}
```

#### C. Skeleton Components
Criar componentes de loading:

```typescript
// apps/web/src/components/ui/skeleton.tsx
export function Skeleton({ className = "", ...props }) {
	return (
		<div
			className={`animate-pulse bg-gray-200 rounded ${className}`}
			{...props}
		/>
	);
}

export function SkeletonCard() {
	return (
		<div className="border border-gray-200 rounded-lg p-4 space-y-4">
			<Skeleton className="h-4 w-3/4" />
			<Skeleton className="h-20 w-full" />
			<Skeleton className="h-4 w-1/2" />
		</div>
	);
}

export function SkeletonCheckout() {
	return (
		<div className="space-y-6">
			<Skeleton className="h-8 w-1/2" />
			<SkeletonCard />
			<SkeletonCard />
			<Skeleton className="h-12 w-full" />
		</div>
	);
}
```

#### D. EmailAutoComplete Component
Criar autocomplete de email com i18n:

```typescript
// apps/web/src/components/common/email-autocomplete.tsx
"use client";

import { useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import TextField from "@/components/ui/text-field";

const EMAIL_DOMAINS = [
	"@gmail.com",
	"@hotmail.com",
	"@outlook.com",
	"@yahoo.com",
	"@live.com",
	"@icloud.com",
];

interface EmailAutoCompleteProps {
	name?: string;
	label?: string;
	placeholder?: string;
	onChange?: (value: string) => void;
}

export function EmailAutoComplete({
	name = "email",
	label,
	placeholder,
	onChange,
}: EmailAutoCompleteProps) {
	const { t } = useTranslation();
	const [suggestions, setSuggestions] = useState<string[]>([]);
	const [inputValue, setInputValue] = useState("");

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value = e.target.value;
		setInputValue(value);
		onChange?.(value);

		if (value.includes("@")) {
			setSuggestions([]);
			return;
		}

		if (value.trim()) {
			setSuggestions(EMAIL_DOMAINS.map((domain) => value + domain));
		} else {
			setSuggestions([]);
		}
	};

	const handleSelectSuggestion = (suggestion: string) => {
		setInputValue(suggestion);
		onChange?.(suggestion);
		setSuggestions([]);
	};

	return (
		<div className="relative">
			<TextField
				name={name}
				label={label || t("form.labels.email")}
				placeholder={placeholder || t("form.placeholders.email")}
				type="email"
				value={inputValue}
				onChange={handleChange}
			/>

			{suggestions.length > 0 && (
				<div className="absolute z-10 w-full bg-white border border-gray-200 rounded-md shadow-lg mt-1 max-h-48 overflow-y-auto">
					{suggestions.map((suggestion) => (
						<div
							key={suggestion}
							className="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
							onClick={() => handleSelectSuggestion(suggestion)}
						>
							{suggestion}
						</div>
					))}
				</div>
			)}
		</div>
	);
}
```

---

### 3. PRIORIDADE MÉDIA: Sistema de Validações ⭐⭐⭐

#### Criar Validation Utilities

```typescript
// apps/web/src/lib/utils/validation.ts
import { useTranslation } from "@/hooks/useTranslation";

export function useValidation() {
	const { t } = useTranslation();

	return {
		required: (value: any) => {
			if (!value) return t("errors.validation.required");
			return true;
		},

		email: (value: string) => {
			if (!value) return t("errors.validation.required");
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			if (!emailRegex.test(value)) return t("errors.validation.invalid_email");
			return true;
		},

		cpf: (value: string) => {
			if (!value) return t("errors.validation.required");
			// Validação real de CPF
			const cleaned = value.replace(/\D/g, "");
			if (cleaned.length !== 11) return t("errors.validation.invalid_cpf");
			// Adicionar validação de dígitos verificadores
			return true;
		},

		phone: (value: string) => {
			if (!value) return t("errors.validation.required");
			const cleaned = value.replace(/\D/g, "");
			if (cleaned.length < 10) return t("errors.validation.invalid_phone");
			return true;
		},

		cardNumber: (value: string) => {
			if (!value) return t("errors.validation.required");
			const cleaned = value.replace(/\s/g, "");
			if (cleaned.length < 13) return t("errors.validation.invalid_card_number");
			return true;
		},

		cardExpiration: (value: string) => {
			if (!value) return t("errors.validation.required");
			const match = value.match(/^(0[1-9]|1[0-2])\/(\d{2})$/);
			if (!match) return t("errors.validation.invalid_date");

			const month = parseInt(match[1], 10);
			const year = 2000 + parseInt(match[2], 10);
			const now = new Date();
			const y = now.getFullYear();
			const m = now.getMonth() + 1;

			if (year < y || (year === y && month < m)) {
				return t("errors.validation.invalid_expiry");
			}

			return true;
		},

		cvv: (value: string, cardType?: string) => {
			if (!value) return t("errors.validation.required");
			const expectedLength = cardType === "amex" ? 4 : 3;
			if (value.length !== expectedLength) {
				return t("errors.validation.invalid_cvv");
			}
			return true;
		},
	};
}
```

#### Aplicar Validações nos Formulários

Atualizar `CheckoutComponent.tsx`, `CreditCardForm.tsx`, etc para usar:

```typescript
import { useValidation } from "@/lib/utils/validation";

export default function CheckoutComponent() {
	const validation = useValidation();
	const form = useForm({
		defaultValues: { /* ... */ },
	});

	return (
		<FormProvider {...form}>
			<TextField
				name="email"
				validate={validation.email}
				// ...
			/>
			<TextField
				name="cpf"
				validate={validation.cpf}
				// ...
			/>
		</FormProvider>
	);
}
```

---

### 4. PRIORIDADE MÉDIA: Loading States ⭐⭐⭐

#### Implementar Loading States nos Componentes

**CheckoutComponent:**
```typescript
import { SkeletonCheckout } from "@/components/ui/skeleton";

export default function CheckoutComponent() {
	const { offer, isFetching } = useCheckout();

	if (isFetching) {
		return <SkeletonCheckout />;
	}

	// ... resto do código
}
```

**OrderSummary:**
```typescript
import { Skeleton } from "@/components/ui/skeleton";

export default function OrderSummary() {
	const { isFetching } = useCheckout();

	if (isFetching) {
		return (
			<div className="space-y-4">
				<Skeleton className="h-8 w-full" />
				<Skeleton className="h-20 w-full" />
				<Skeleton className="h-8 w-full" />
			</div>
		);
	}

	// ... resto do código
}
```

---

### 5. PRIORIDADE BAIXA: Outros Componentes ⭐⭐

#### Componentes a Internacionalizar (se houver tempo)

- [ ] `OrderItemResume.tsx`
  - Verificar se há textos hardcoded em "à vista", "por mês", etc
  - Usar `t("payment.one_time")`, `t("common.per")`, `t("common.month")`

- [ ] `InstallmentsSelector.tsx`
  - Verificar textos de parcelas
  - Usar `t("payment.installments")`

- [ ] `PaymentMethods.tsx`
  - Verificar se tabs estão usando i18n

---

## 📝 Passos de Implementação

### Passo 1: Verificar Mensagens i18n Existentes
```bash
# Verificar se todas as mensagens necessárias já existem
cat apps/web/src/lib/i18n/messages/pt.json | grep -E "(waiting|success|qrcode|barcode)"
```

### Passo 2: Adicionar Mensagens Faltantes (se necessário)

Se faltar alguma mensagem, adicionar em pt.json, en.json, es.json:

```json
{
  "payment": {
    "messages": {
      "waiting_payment_title": "Aguardando pagamento",
      "check_payment": "Verificar pagamento",
      "checking_payment": "Verificando pagamento..."
    }
  },
  "success": {
    "thank_you": "Obrigado pela sua compra!",
    "email_sent": "Você receberá um email com os detalhes",
    "access_product": "Acessar produto"
  }
}
```

### Passo 3: Atualizar Componentes de Status

Para cada componente:
1. Adicionar `import { useTranslation } from "@/hooks/useTranslation";`
2. Adicionar `const { t } = useTranslation();` no início
3. Substituir TODOS os textos hardcoded por `t("...")`
4. Verificar linter

### Passo 4: Criar Componentes Faltantes

Criar na ordem:
1. `Skeleton.tsx` - Mais simples
2. `Toast.tsx` - Moderado
3. `EmailAutoComplete.tsx` - Moderado
4. `ErrorBoundary.tsx` - Mais complexo

### Passo 5: Implementar Validações

1. Criar `lib/utils/validation.ts`
2. Aplicar em todos os formulários
3. Testar mensagens de erro

### Passo 6: Adicionar Loading States

1. Usar `Skeleton` nos componentes principais
2. Adicionar loading indicators nos botões
3. Shimmer effects onde apropriado

### Passo 7: Testes Completos

1. Testar checkout em português
2. Testar checkout em inglês
3. Testar checkout em espanhol
4. Verificar TODOS os fluxos de pagamento
5. Validar mensagens de erro
6. Confirmar zero textos hardcoded

---

## ✅ Critérios de Aceitação

### Para considerar CONCLUÍDO:

1. ✅ **ZERO textos hardcoded em português/inglês/espanhol**
2. ✅ **TODOS os componentes de status internacionalizados**
3. ✅ **Toast component funcionando com i18n**
4. ✅ **ErrorBoundary implementado**
5. ✅ **Skeleton/Loading states em todos os lugares apropriados**
6. ✅ **EmailAutoComplete funcionando**
7. ✅ **Sistema de validações com mensagens traduzidas**
8. ✅ **Zero erros de linter/TypeScript**
9. ✅ **Checkout funciona perfeitamente em 3 idiomas**
10. ✅ **Todos os fluxos de pagamento testados**

---

## 🧪 Como Testar

### Teste Manual Completo

```bash
# 1. Iniciar servidor
cd apps/web
pnpm dev

# 2. Testar em cada idioma
open http://localhost:3001/pt/7rohg3i
open http://localhost:3001/en/7rohg3i
open http://localhost:3001/es/7rohg3i

# 3. Em cada idioma, testar:
# - Preencher formulário
# - Validações (campos vazios, email inválido, etc)
# - Trocar método de pagamento
# - Aplicar cupom
# - Ver loading states
# - Simular erro (se possível)
# - Verificar toast notifications
# - Completar pagamento (se ambiente de teste)
```

### Verificar Textos Hardcoded

```bash
# Buscar por textos em português hardcoded
grep -r "Aguardando\|Pagamento\|Confirma\|Digite\|Preencha\|Selecione" \
  apps/web/src/components --include="*.tsx" | \
  grep -v "t(" | \
  grep -v "//" | \
  grep -v "\.md"

# Deve retornar ZERO resultados
```

### Build e Verificação de Tipos

```bash
# Verificar se compila sem erros
cd apps/web
pnpm build

# Verificar linter
pnpm lint
```

---

## 📊 Métricas de Sucesso

### Obrigatório
- [ ] **100% dos textos traduzidos** (0 textos hardcoded)
- [ ] **100% dos componentes de status migrados**
- [ ] **0 erros de linter**
- [ ] **0 erros de TypeScript**
- [ ] **3 idiomas funcionando perfeitamente** (pt, en, es)
- [ ] **Sistema de validações completo**
- [ ] **Error handling robusto**
- [ ] **Loading states em todos os lugares**

### Desejável
- [ ] **Lighthouse score > 85**
- [ ] **FCP < 1.5s**
- [ ] **TTI < 3s**
- [ ] **Bundle size < 500KB (gzipped)**

---

## 📚 Referências

### Arquivos Importantes

**Documentação:**
- `STATUS_FASE_7_PROGRESSO.md` - Progresso anterior
- `_docs/README.md` - Índice da documentação
- `.cursorrules` - Regras do projeto

**Código de Referência:**
- `apps/web/src/components/checkout/CheckoutComponent.tsx` - Exemplo de i18n
- `apps/web/src/components/payments/CreditCardForm.tsx` - Exemplo de validações
- `apps/web/src/hooks/useTranslation.ts` - Hook de tradução
- `apps/web/src/lib/i18n/messages/pt.json` - Mensagens em português

**Projeto Original:**
- `_docs/cakto-checkout/src/components/` - Código original para referência

---

## 🚀 Começar Agora

### Ordem Sugerida de Implementação:

1. **Componentes de Status** (2-3h)
   - WaitingPayment
   - SuccessPayment
   - PixPayment
   - BoletoPayment
   - Outros componentes de pagamento

2. **Skeleton Component** (30min)
   - Criar componente base
   - Variantes (Card, Checkout, etc)

3. **Toast Component** (1h)
   - Criar componente
   - Criar hook useToast
   - Testar

4. **EmailAutoComplete** (1h)
   - Criar componente
   - Integrar com formulário
   - Testar

5. **Sistema de Validações** (1-2h)
   - Criar utilitários
   - Aplicar nos formulários
   - Adicionar mensagens de erro

6. **ErrorBoundary** (1h)
   - Criar componente
   - Integrar no layout
   - Testar erro recovery

7. **Loading States** (1h)
   - Adicionar skeletons
   - Loading indicators
   - Shimmer effects

8. **Testes Completos** (2h)
   - Testar em 3 idiomas
   - Todos os fluxos
   - Validações
   - Error handling

**Tempo total estimado:** 10-12 horas

---

## 🎉 Resultado Esperado

Ao final desta fase, você terá:

1. ✅ **Checkout 100% internacionalizado** em pt/en/es
2. ✅ **Zero textos hardcoded**
3. ✅ **Sistema de validações robusto**
4. ✅ **Error handling profissional**
5. ✅ **UX polida com loading states**
6. ✅ **Paridade completa com projeto original**
7. ✅ **Código de alta qualidade e manutenível**

---

**Última atualização:** 10/11/2025  
**Versão:** 2.0.0  
**Prioridade:** ALTA ⭐⭐⭐⭐⭐  
**Status:** PRONTO PARA EXECUÇÃO  
**Progresso anterior:** 40% → Objetivo: 100%




