# Prompt para Migração de Componentes - Checkout V2

## 🎯 Objetivo

Migrar os componentes do checkout original (`cakto-checkout` - Vite + React) para o novo projeto (`cakto-checkoutv2` - Next.js 16), **reutilizando ao máximo** os componentes existentes, especialmente os componentes do builder, e adaptando apenas o necessário para funcionar no Next.js.

## 📋 Contexto do Projeto

### Projeto Original (Fonte)
- **Localização**: `/Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkout`
- **Stack**: Vite + React 18 + TypeScript
- **Roteamento**: React Router DOM
- **HTTP Client**: Axios
- **Formulários**: React Hook Form + Yup

### Projeto Novo (Destino)
- **Localização**: `/Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkoutv2/apps/web`
- **Stack**: Next.js 16 + React 19 + TypeScript
- **Roteamento**: Next.js App Router
- **HTTP Client**: fetch nativo (server-side) ou Server Actions
- **Formulários**: React Hook Form (manter)

### Status Atual
- ✅ Sistema de i18n implementado (pt, es, en)
- ✅ Rotas SSR configuradas (`app/[locale]/[id]/page.tsx`)
- ✅ API client adaptado para Next.js
- ✅ Variáveis de ambiente migradas
- ✅ Middleware de detecção de idioma funcionando
- 🔄 **AGORA**: Migrar componentes do checkout

## 🎯 Princípios da Migração

### ✅ REUTILIZAR (NÃO REESCREVER)
- **Componentes do Builder**: Copiar direto, apenas adicionar `"use client"` e atualizar imports
- **Lógica de Negócio**: Manter toda validação, cálculos e regras
- **Hooks Customizados**: Manter lógica, apenas atualizar imports
- **Utilitários**: Copiar direto sem modificações
- **Tipos TypeScript**: Copiar direto
- **Estilos**: Manter classes Tailwind e CSS existentes

### ⚠️ ADAPTAR (MUDANÇAS MÍNIMAS)
- **Imports**: Atualizar paths para estrutura Next.js (`@/` ao invés de paths relativos)
- **Client Components**: Adicionar `"use client"` no topo
- **Variáveis de Ambiente**: Usar helpers de `lib/env.ts` ao invés de `import.meta.env`
- **Contextos**: Adaptar para receber `initialData` do SSR
- **APIs**: Usar `lib/api/` ao invés de `services/`

### ❌ REMOVER/SUBSTITUIR
- **React Router**: Remover `useNavigate`, `useLocation`, `BrowserRouter`
- **Axios**: Substituir por `fetch` ou helpers de `lib/api/`
- **import.meta.env**: Substituir por helpers de `lib/env.ts`
- **Vite-specific code**: Remover qualquer código específico do Vite

## 📁 Estrutura de Migração

### Origem (cakto-checkout)
```
src/
├── components/
│   ├── builder/          # ⭐ PRIORIDADE: Copiar direto
│   ├── checkout/         # Adaptar para Next.js
│   ├── payment/          # Adaptar para Next.js
│   ├── common/           # Copiar direto
│   └── pixels/           # Adaptar para Next.js
├── contexts/             # Adaptar para SSR
├── hooks/                # Manter lógica, atualizar imports
├── services/             # Substituir por lib/api/
├── utils/                # Copiar direto
└── types/                # Copiar direto
```

### Destino (cakto-checkoutv2/apps/web/src)
```
src/
├── components/
│   ├── builder/          # ← COPIAR DA ORIGEM
│   ├── checkout/         # ← MIGRAR DA ORIGEM
│   ├── payments/         # ← MIGRAR DA ORIGEM (renomear payment → payments)
│   ├── common/           # ← COPIAR DA ORIGEM
│   └── pixels/           # ← MIGRAR DA ORIGEM
├── contexts/             # ← MIGRAR DA ORIGEM
├── hooks/                # ← MIGRAR DA ORIGEM
├── lib/
│   ├── api/              # ✅ JÁ CRIADO
│   └── utils/            # ← COPIAR utils/ DA ORIGEM
└── types/                # ← COPIAR DA ORIGEM
```

## 🚀 Plano de Execução

### Fase 1: Base (Tipos e Utilitários)
**Objetivo**: Criar base para todos os componentes

1. **Copiar tipos TypeScript**:
   ```bash
   cp -r cakto-checkout/src/types/* cakto-checkoutv2/apps/web/src/types/
   ```

2. **Copiar utilitários**:
   ```bash
   cp -r cakto-checkout/src/utils/* cakto-checkoutv2/apps/web/src/lib/utils/
   ```

3. **Atualizar imports nos utilitários** (se necessário):
   - Verificar se há imports relativos que precisam ser atualizados
   - Manter funcionalidade idêntica

### Fase 2: Componentes do Builder (PRIORIDADE MÁXIMA)
**Objetivo**: Reutilizar 100% dos componentes do builder

1. **Copiar todos os componentes do builder**:
   ```bash
   cp -r cakto-checkout/src/components/builder/* cakto-checkoutv2/apps/web/src/components/builder/
   ```

2. **Para cada componente do builder**:
   - Adicionar `"use client"` como primeira linha
   - Atualizar imports:
     ```typescript
     // ANTES
     import { SomeUtil } from '@/utils/someUtil';
     
     // DEPOIS
     import { SomeUtil } from '@/lib/utils/someUtil';
     ```
   - Substituir `import.meta.env` por helpers de `lib/env.ts`:
     ```typescript
     // ANTES
     const apiKey = import.meta.env.VITE_HOPYPAY_PUBLIC_KEY;
     
     // DEPOIS
     import { getHopyPayPublicKey } from '@/lib/env';
     const apiKey = getHopyPayPublicKey();
     ```
   - Remover uso de React Router (se houver)
   - **NÃO modificar lógica ou estrutura do componente**

3. **Componentes do builder a migrar**:
   - `CheckoutComponentText.tsx`
   - `CheckoutComponentImage.tsx`
   - `CheckoutComponentVideo.tsx`
   - `CheckoutComponentTestimonial.tsx`
   - `CheckoutComponentSeal.tsx`
   - `CheckoutComponentCountdown.tsx`
   - `CheckoutComponentList.tsx`
   - `CheckoutComponentMap.tsx`
   - `CheckoutComponentChat.tsx`
   - `CheckoutComponentFacebook.tsx`
   - `CheckoutComponentNotification.tsx`
   - `CheckoutComponentExitPopup.tsx`
   - `CheckoutComponentAdvantage.tsx`
   - E outros componentes do builder

### Fase 3: Contextos
**Objetivo**: Adaptar contextos para funcionar com SSR

1. **CheckoutContext**:
   - Copiar de `cakto-checkout/src/contexts/CheckoutContext.tsx`
   - Adaptar para receber `initialData` do SSR:
     ```typescript
     "use client";
     
     import { createContext, useContext, useState } from 'react';
     
     type CheckoutContextType = {
       // ... tipos existentes
     };
     
     const CheckoutContext = createContext<CheckoutContextType | null>(null);
     
     export function CheckoutProvider({ 
       children, 
       initialData 
     }: { 
       children: React.ReactNode;
       initialData: CheckoutData;
     }) {
       const [data, setData] = useState(initialData);
       // ... resto da lógica
     }
     ```

2. **CheckoutModeContext**:
   - Copiar e adaptar similarmente
   - Manter toda lógica de preview mode

3. **NotificationContext**:
   - Copiar e adaptar
   - Manter funcionalidade de notificações

### Fase 4: Hooks Customizados
**Objetivo**: Migrar hooks mantendo toda lógica

1. **Copiar todos os hooks**:
   ```bash
   cp -r cakto-checkout/src/hooks/* cakto-checkoutv2/apps/web/src/hooks/
   ```

2. **Para cada hook**:
   - Atualizar imports para estrutura Next.js
   - Substituir `import.meta.env` por helpers de `lib/env.ts`
   - Substituir uso de `axios` por `fetch` ou helpers de `lib/api/`
   - Remover dependências do React Router
   - **Manter toda lógica do hook**

### Fase 5: Componentes de Pagamento
**Objetivo**: Migrar formulários de pagamento

1. **Copiar componentes de pagamento**:
   ```bash
   mkdir -p cakto-checkoutv2/apps/web/src/components/payments
   cp -r cakto-checkout/src/components/payment/* cakto-checkoutv2/apps/web/src/components/payments/
   ```

2. **Para cada componente de pagamento**:
   - Adicionar `"use client"`
   - Atualizar imports
   - Substituir `import.meta.env` por helpers de `lib/env.ts`
   - Adaptar chamadas de API para usar `lib/api/`
   - **Manter toda lógica de validação e formulário**

3. **Componentes a migrar**:
   - `CreditCardForm.tsx`
   - `PixForm.tsx`
   - `PixAutoForm.tsx`
   - `BoletoForm.tsx`
   - `ApplePayForm.tsx`
   - `GooglePayForm.tsx`
   - `PicPayForm.tsx`
   - `NubankPayForm.tsx`
   - `WaitingPayment.tsx`
   - `SuccessPayment.tsx`

### Fase 6: Componentes do Checkout
**Objetivo**: Migrar componentes principais

1. **CheckoutForm.tsx**:
   - Copiar de `cakto-checkout/src/components/checkout/CheckoutForm.tsx`
   - Adicionar `"use client"`
   - Atualizar imports
   - Adaptar para usar contextos migrados
   - **Manter toda lógica de formulário e validação**

2. **ProductPurchaseComponent.tsx**:
   - Copiar e adaptar
   - Manter como Client Component
   - Atualizar imports

3. **PaymentMethods.tsx**:
   - Copiar e adaptar
   - Manter funcionalidade de seleção de métodos

### Fase 7: Componentes Comuns
**Objetivo**: Migrar componentes reutilizáveis

1. **Copiar componentes comuns**:
   ```bash
   cp -r cakto-checkout/src/components/common/* cakto-checkoutv2/apps/web/src/components/common/
   ```

2. **Adaptar cada componente**:
   - Adicionar `"use client"` se necessário
   - Atualizar imports
   - Substituir `import.meta.env` se houver

### Fase 8: Tracking Pixels
**Objetivo**: Migrar pixels mantendo funcionalidade

1. **Copiar componentes de pixels**:
   ```bash
   cp -r cakto-checkout/src/components/pixels/* cakto-checkoutv2/apps/web/src/components/pixels/
   ```

2. **Adaptar**:
   - Adicionar `"use client"` em todos
   - Atualizar imports
   - Substituir `import.meta.env` por helpers de `lib/env.ts`
   - Garantir que carregam apenas no cliente

### Fase 9: Integração e Teste
**Objetivo**: Integrar tudo e testar

1. **Integrar componentes do builder no CheckoutClient**:
   - Atualizar `components/checkout/checkout-client.tsx`
   - Renderizar componentes do builder baseado na configuração
   - Usar dados do SSR

2. **Conectar formulários de pagamento**:
   - Integrar no CheckoutForm
   - Conectar com contextos

3. **Testar fluxo completo**:
   - Testar carregamento do checkout
   - Testar seleção de método de pagamento
   - Testar preenchimento de formulário
   - Testar submissão

## 📝 Checklist de Migração por Componente

Para cada componente migrado, verificar:

- [ ] Adicionado `"use client"` se necessário (todos os componentes interativos)
- [ ] Imports atualizados para estrutura Next.js (`@/` paths)
- [ ] Variáveis de ambiente adaptadas (usar `lib/env.ts`)
- [ ] Removido código específico do Vite (`import.meta.env`)
- [ ] Removido uso de React Router (se houver)
- [ ] Adaptado uso de APIs para `lib/api/`
- [ ] Removido uso de Axios (substituir por `fetch` ou helpers)
- [ ] Mantida toda lógica de negócio
- [ ] Mantidos estilos e classes Tailwind
- [ ] Testado funcionamento no navegador

## 🔍 Comandos Úteis

### Buscar uso de import.meta.env:
```bash
cd cakto-checkoutv2/apps/web/src
grep -r "import.meta.env" .
```

### Buscar uso de React Router:
```bash
grep -r "react-router" .
grep -r "useNavigate\|useLocation" .
```

### Buscar uso de Axios:
```bash
grep -r "axios" .
grep -r "from 'axios'" .
```

### Adicionar "use client" em todos os componentes:
```bash
cd cakto-checkoutv2/apps/web/src/components/builder
find . -name "*.tsx" -exec sed -i '' '1i\
"use client";
' {} \;
```

## 📚 Arquivos de Referência

### Documentação
- `.cursor/rules` - Regras e padrões do projeto
- `MIGRACAO_COMPONENTES.md` - Guia detalhado de migração
- `apps/web/ENV.md` - Documentação de variáveis de ambiente

### Código de Referência
- `apps/web/src/lib/api/checkout.ts` - Exemplo de API client
- `apps/web/src/lib/env.ts` - Helpers de variáveis de ambiente
- `apps/web/src/app/[locale]/[id]/page.tsx` - Exemplo de Server Component
- `apps/web/src/components/checkout/checkout-client.tsx` - Exemplo de Client Component
- `apps/web/src/contexts/intl-context.tsx` - Exemplo de contexto adaptado

### Projeto Original
- `/Users/<USER>/Documents/www/cakto/cakto-checkout/cakto-checkout/src/` - Código fonte original

## ⚠️ Regras Importantes

### NUNCA
- ❌ Reescrever componentes do builder - apenas adaptar
- ❌ Modificar lógica de negócio - manter idêntica
- ❌ Usar `import.meta.env` - usar `lib/env.ts`
- ❌ Usar `axios` - usar `fetch` ou `lib/api/`
- ❌ Usar React Router - usar Next.js App Router
- ❌ Criar Server Components com hooks do React

### SEMPRE
- ✅ Adicionar `"use client"` em componentes interativos
- ✅ Usar path aliases (`@/`) para imports
- ✅ Usar helpers de `lib/env.ts` para env vars
- ✅ Manter compatibilidade com APIs existentes
- ✅ Testar após migrar cada componente
- ✅ Manter estilos e classes Tailwind existentes

## 🎯 Prioridades

1. **ALTA**: Componentes do builder (copiar direto, adaptar mínimo)
2. **ALTA**: Tipos e utilitários (base para tudo)
3. **MÉDIA**: Contextos (necessários para outros componentes)
4. **MÉDIA**: Hooks customizados (usados por componentes)
5. **MÉDIA**: Componentes de pagamento (funcionalidade core)
6. **MÉDIA**: Componentes do checkout (integração)
7. **BAIXA**: Componentes comuns (reutilizáveis)
8. **BAIXA**: Tracking pixels (último passo)

## 🚀 Começar Agora

**Inicie pela Fase 1 (Tipos e Utilitários)** e siga a ordem das fases. Para cada componente:

1. Copiar do projeto original
2. Adaptar apenas o necessário (imports, "use client", env vars)
3. Testar funcionamento
4. Continuar para o próximo

**Lembre-se**: O objetivo é **reutilizar ao máximo**, não reescrever. Os componentes do builder devem funcionar quase idênticos ao original, apenas com adaptações mínimas para Next.js.

