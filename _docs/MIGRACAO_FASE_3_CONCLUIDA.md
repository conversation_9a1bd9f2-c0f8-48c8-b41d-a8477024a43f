# Fase 3: Migração de Contextos - CONCLUÍDA ✅

## 📋 Resumo da Migração

A Fase 3 da migração foi concluída com sucesso. Todos os contextos principais foram migrados do projeto original (Vite + React) para o novo projeto (Next.js 16), adaptados para funcionar com SSR e Client Components.

## ✅ Arquivos Migrados

### Contextos

#### 1. CheckoutModeContext
- **Origem**: `_docs/cakto-checkout/src/contexts/CheckoutModeContext.tsx`
- **Destino**: `apps/web/src/contexts/checkout-mode-context.tsx`
- **Status**: ✅ COMPLETO
- **Adaptações**:
  - Adicionado `"use client"` directive
  - Hook customizado `useCheckoutMode()` criado
  - TypeScript types melhorados
  - Context null check implementado

#### 2. NotificationContext
- **Origem**: `_docs/cakto-checkout/src/contexts/NotificationContext.tsx`
- **Destino**: `apps/web/src/contexts/notification-context.tsx`
- **Status**: ✅ COMPLETO
- **Adaptações**:
  - Adicionado `"use client"` directive
  - Hook customizado `useNotificationContext()` criado
  - Importa `useNotifications` do novo hook migrado

#### 3. CheckoutContext
- **Origem**: `_docs/cakto-checkout/src/contexts/CheckoutContext.tsx`
- **Destino**: `apps/web/src/contexts/checkout-context.tsx`
- **Status**: ✅ COMPLETO COM PLACEHOLDERS
- **Adaptações**:
  - Adicionado `"use client"` directive
  - Hook customizado `useCheckout()` criado
  - Adaptado para receber `initialData` do SSR
  - Substituído React Query por `useState` e callbacks manuais
  - Substituído React Router por props (`checkoutId`)
  - Substituído Axios por funções de `lib/api/checkout-client.ts`
  - Pixel hooks importados (placeholders)
  - Componentes de pagamento com placeholders temporários
  - Ícones com placeholders temporários (usando heroicons)

### Hooks

#### 1. useNotifications
- **Origem**: `_docs/cakto-checkout/src/hooks/useNotifications.ts`
- **Destino**: `apps/web/src/hooks/useNotifications.ts`
- **Status**: ✅ COMPLETO
- **Adaptações**:
  - Adicionado `"use client"` directive
  - Imports atualizados para Next.js paths (`@/`)
  - Lógica mantida idêntica

#### 2. Pixel Hooks (Placeholders)
- **Arquivos criados**:
  - `apps/web/src/hooks/useFacebookPixels.ts`
  - `apps/web/src/hooks/useGoogleAds.ts`
  - `apps/web/src/hooks/useTikTokPixels.ts`
  - `apps/web/src/hooks/useKwaiPixels.ts`
- **Status**: ⚠️ PLACEHOLDERS (console.log temporários)
- **Nota**: Implementação completa será feita na Fase 4

### API Client (Client-Side)

#### checkout-client.ts
- **Arquivo**: `apps/web/src/lib/api/checkout-client.ts`
- **Status**: ✅ COMPLETO
- **Funções criadas**:
  - `startPayment()` - Iniciar pagamento
  - `getPaymentStatus()` - Consultar status do pagamento
  - `getInstallments()` - Obter parcelamento
  - `validateCoupon()` - Validar cupom
  - `generateFingerprint()` - Gerar fingerprint do dispositivo
  - `generateCreditCardToken()` - Gerar token do cartão (NoxPay)
- **Características**:
  - Usa `fetch` nativo (client-side)
  - Erros formatados para compatibilidade com errorHandler
  - Credenciais incluídas automaticamente

### Utilities

#### 1. error-handler.ts
- **Arquivo**: `apps/web/src/lib/utils/error-handler.ts`
- **Status**: ✅ COMPLETO
- **Adaptações**:
  - Adicionado `"use client"` directive
  - Adaptado para usar `fetch` errors ao invés de Axios
  - Mantida toda lógica de negócio de tratamento de erros
  - Mensagens de erro em português mantidas

#### 2. error-logger.ts
- **Arquivo**: `apps/web/src/lib/utils/error-logger.ts`
- **Status**: ✅ COMPLETO
- **Adaptações**:
  - Adicionado `"use client"` directive
  - Substituído `import.meta.env` por `process.env.NODE_ENV`
  - Checks de ambiente SSR (`typeof window !== "undefined"`)
  - Mantida toda lógica de logging

### Index File
- **Arquivo**: `apps/web/src/contexts/index.tsx`
- **Exports**:
  - `CheckoutProvider`, `useCheckout`
  - `CheckoutModeProvider`, `useCheckoutMode`
  - `NotificationProvider`, `useNotificationContext`

## 🔧 Principais Adaptações para Next.js

### 1. Client Components
Todos os contextos e hooks receberam a directive `"use client"` no topo do arquivo, pois contextos e hooks do React só funcionam em Client Components.

### 2. SSR Support
O `CheckoutContext` foi adaptado para receber dados iniciais do servidor:

```typescript
export function CheckoutProvider({
  children,
  initialData,
  checkoutId,
}: {
  children: ReactNode;
  initialData: ProductData;
  checkoutId: string;
}) {
  const [offer, setOffer] = useState<ProductData | undefined>(initialData);
  // ...
}
```

### 3. Substituição de React Query
Ao invés de usar React Query (tanstack-query), foi implementado state management manual com `useState` e callbacks:

```typescript
const pay = useCallback(async (payload: PaymentPayload) => {
  try {
    setPaying(true);
    setStatus("loading");
    const data = await startPaymentAPI(...);
    setStatus("success");
    return data;
  } catch (err) {
    setStatus("error");
    setError(err);
    throw err;
  } finally {
    setPaying(false);
  }
}, [dependencies]);
```

### 4. Substituição de React Router
Props são passadas diretamente ao invés de usar hooks do React Router:

```typescript
// ANTES (Vite)
const { id } = useParams<{ id: string }>();
const [searchParams] = useSearchParams();

// DEPOIS (Next.js)
// Props recebidas do Server Component
checkoutId: string
```

### 5. Substituição de Axios
Todas as chamadas de API agora usam `fetch` através de funções helper em `lib/api/checkout-client.ts`:

```typescript
// ANTES
import { startPayment } from '@/services/checkout';

// DEPOIS
import { startPayment as startPaymentAPI } from '@/lib/api/checkout-client';
```

## ⚠️ Componentes com Placeholders

Os seguintes componentes ainda precisam ser implementados nas próximas fases:

### 1. Payment Forms (Fase 5)
Placeholders temporários foram criados:
- `CreditCardForm`
- `BoletoForm`
- `PixForm`
- `PixAutoForm`
- `PicPayForm`
- `ApplePayForm`
- `GooglePayForm`
- `NubankPayForm`

### 2. Payment Icons (Fase 4)
Usando `CreditCardIcon` do Heroicons como placeholder. Ícones originais precisam ser migrados:
- `AlertIcon`
- `ApplePayIcon`
- `BarcodeIcon`
- `GooglePayIcon`
- `NubankIcon`
- `PicPayIcon`
- `PixIcon`
- `PixAutoIcon`

### 3. Pixel Hooks (Fase 4)
Implementação completa dos hooks de pixels com tracking real ao invés de `console.log`:
- `useFacebookPixels`
- `useGoogleAds`
- `useTikTokPixels`
- `useKwaiPixels`

## 📝 Como Usar os Contextos

### Setup no Layout ou Page (Server Component)

```typescript
// app/[locale]/[id]/page.tsx (Server Component)
import { getCheckoutData } from "@/lib/api/checkout";
import { CheckoutProvider, CheckoutModeProvider } from "@/contexts";

export default async function CheckoutPage({ 
  params 
}: { 
  params: { id: string } 
}) {
  const checkoutData = await getCheckoutData(params.id);
  
  return (
    <CheckoutModeProvider preview={false}>
      <CheckoutProvider 
        initialData={checkoutData} 
        checkoutId={params.id}
      >
        <CheckoutContent />
      </CheckoutProvider>
    </CheckoutModeProvider>
  );
}
```

### Uso em Client Components

```typescript
"use client";

import { useCheckout, useCheckoutMode, useNotificationContext } from "@/contexts";

export function MyComponent() {
  const { offer, pay, paying } = useCheckout();
  const { preview } = useCheckoutMode();
  const { notify } = useNotificationContext();
  
  // Usar os dados e funções do contexto
}
```

## 🎯 Próximos Passos

### Fase 4: Hooks e Serviços
- Migrar hooks de pixels completos
- Migrar ícones personalizados
- Migrar serviços auxiliares (CEP, etc)

### Fase 5: Componentes de Pagamento
- Migrar formulários de pagamento
- Migrar componentes de checkout
- Integrar com contextos

### Fase 6: Páginas e Roteamento
- Migrar páginas do checkout
- Configurar roteamento Next.js
- Integrar SSR completo

## ✅ Checklist de Verificação

- [x] CheckoutModeContext migrado e funcionando
- [x] NotificationContext migrado e funcionando
- [x] CheckoutContext migrado com estrutura completa
- [x] useNotifications hook migrado
- [x] Pixel hooks (placeholders) criados
- [x] API client (client-side) criado
- [x] errorHandler utility migrado
- [x] errorLogger utility migrado
- [x] Todos imports usando paths Next.js (`@/`)
- [x] Todas diretivas `"use client"` adicionadas
- [x] Context null checks implementados
- [x] Hooks customizados criados para todos contextos
- [x] Sem erros de linting
- [x] Index file criado para exports centralizados

## 📚 Referências

- Documento original: `PROMPT_FASE_3_CONTEXTOS.md`
- Código original: `_docs/cakto-checkout/src/contexts/`
- Next.js App Router: https://nextjs.org/docs/app
- React Context: https://react.dev/reference/react/useContext
- Server and Client Components: https://nextjs.org/docs/app/building-your-application/rendering

## 🎉 Conclusão

A Fase 3 foi concluída com sucesso! Todos os contextos principais estão migrados e adaptados para Next.js. A estrutura está pronta para receber os componentes de pagamento e páginas nas próximas fases.

**Pontos fortes da migração:**
- ✅ Toda lógica de negócio foi preservada
- ✅ Código adaptado para SSR do Next.js
- ✅ Tipos TypeScript mantidos e melhorados
- ✅ Placeholders claros para próximas fases
- ✅ Sem dependências quebradas

**Pronto para Fase 4**: Migração de Hooks e Serviços completos

