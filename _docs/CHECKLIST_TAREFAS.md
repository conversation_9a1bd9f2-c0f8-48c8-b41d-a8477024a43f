# Checklist de Tarefas - Migração Checkout V2

## ✅ Fase 1: Configuração Inicial e Infraestrutura

### 1.1 Setup do Projeto Next.js
- [ ] Configurar Next.js 16 com App Router
- [ ] Configurar TypeScript com paths aliases (`@/` para `src/`)
- [ ] Configurar Tailwind CSS 4
- [ ] Configurar ESLint e Prettier
- [ ] Criar arquivo `.env.example` com todas as variáveis necessárias
- [ ] Configurar estrutura de pastas base conforme especificação
- [ ] Configurar `next.config.ts` com otimizações necessárias
- [ ] Configurar `tsconfig.json` com paths e strict mode

### 1.2 Sistema de Internacionalização
- [ ] Instalar `next-intl` ou similar
- [ ] Criar estrutura de pastas `lib/i18n/`
- [ ] Criar arquivos de tradução:
  - [ ] `lib/i18n/messages/pt.json`
  - [ ] `lib/i18n/messages/es.json`
  - [ ] `lib/i18n/messages/en.json`
- [ ] Criar hook `useTranslation` customizado
- [ ] Configurar provider de i18n
- [ ] Migrar mensagens de erro existentes para sistema de i18n
- [ ] Criar utilitário de formatação de moeda por país
- [ ] Criar utilitário de formatação de data por país

### 1.3 Detecção de IP e Geolocalização
- [ ] Escolher serviço de detecção de IP (ipapi.co, ip-api.com, etc.)
- [ ] Criar serviço `lib/services/ip-detection.ts`
- [ ] Implementar cache de detecção de IP (Redis ou in-memory)
- [ ] Criar middleware Next.js `middleware.ts` na raiz
- [ ] Implementar detecção de país no middleware
- [ ] Mapear países da América Latina para idiomas:
  - [ ] Brasil → Português (pt)
  - [ ] Argentina, Chile, Colômbia, México, Peru, Equador, Bolívia, Paraguai, Uruguai, Venezuela → Espanhol (es)
  - [ ] Outros → Inglês (en) como fallback
- [ ] Implementar fallback para quando detecção falhar
- [ ] Criar cookie `NEXT_LOCALE` para preferência do usuário
- [ ] Implementar troca manual de idioma

## ✅ Fase 2: Migração de Componentes Core

### 2.1 Componentes de Layout
- [ ] Criar `app/[locale]/layout.tsx` com providers
- [ ] Criar `app/[locale]/[id]/page.tsx` (Server Component)
- [ ] Criar `app/[locale]/[id]/loading.tsx`
- [ ] Criar `app/[locale]/[id]/error.tsx`
- [ ] Criar `app/[locale]/preview/page.tsx`
- [ ] Migrar lógica de CheckoutPage para Server Component
- [ ] Adaptar sistema de rotas para Next.js App Router

### 2.2 Sistema de Contextos
- [ ] Migrar `CheckoutContext`:
  - [ ] Separar em Server Component (dados iniciais) e Client Component (estado interativo)
  - [ ] Adaptar para Next.js
- [ ] Migrar `CheckoutModeContext`
- [ ] Migrar `NotificationContext`
- [ ] Criar providers no layout principal
- [ ] Adaptar uso de contextos nos componentes

### 2.3 Componentes de Formulário
- [ ] Migrar `CheckoutForm`:
  - [ ] Adaptar para Client Component
  - [ ] Migrar validações de Yup para Zod
  - [ ] Implementar Server Action para submissão
  - [ ] Manter validações client-side
- [ ] Migrar `AddressForm`
- [ ] Migrar componentes de input:
  - [ ] `TextField`
  - [ ] `TextFieldPhoneNumber`
  - [ ] `EmailAutoComplete`
- [ ] Migrar validações:
  - [ ] CPF/CNPJ
  - [ ] Email
  - [ ] Telefone
  - [ ] Cartão de crédito
- [ ] Implementar validação server-side com Zod

### 2.4 Componentes de Pagamento
- [ ] Migrar `PaymentMethods`
- [ ] Migrar formulários de pagamento:
  - [ ] `CreditCardForm`
  - [ ] `PixForm`
  - [ ] `PixAutoForm`
  - [ ] `BoletoForm`
  - [ ] `ApplePayForm`
  - [ ] `GooglePayForm`
  - [ ] `PicPayForm`
  - [ ] `NubankPayForm`
- [ ] Adaptar sistema de 3DS para SSR:
  - [ ] Cielo 3DS
  - [ ] Pagar.me 3DS
- [ ] Migrar sistema de parcelamento:
  - [ ] `useFetchCalculatedInstallments`
  - [ ] Componente de seleção de parcelas
- [ ] Adaptar Apple Pay e Google Pay para SSR

## ✅ Fase 3: Serviços e Integrações

### 3.1 Serviços de API
- [ ] Criar `lib/api/checkout.ts`:
  - [ ] `getCheckoutData` (server-side fetch)
  - [ ] `startPayment` (Server Action)
  - [ ] `getPaymentStatus` (Server Action)
  - [ ] `getInstallments` (server-side fetch)
- [ ] Criar `lib/api/coupon.ts`:
  - [ ] `validateCoupon` (Server Action)
- [ ] Criar `lib/api/abandonment.ts`:
  - [ ] `createAbandonment` (Server Action)
- [ ] Criar `lib/api/3ds.ts`:
  - [ ] `getThreeDSToken` (server-side fetch)
- [ ] Criar `lib/api/antifraud.ts`:
  - [ ] `getAntifraudToken` (server-side fetch)
- [ ] Manter compatibilidade com APIs existentes:
  - [ ] Mesmas URLs
  - [ ] Mesmos headers
  - [ ] Mesmos payloads
  - [ ] `withCredentials: true`

### 3.2 Integrações de Terceiros
- [ ] Migrar sistema de tracking pixels:
  - [ ] `FacebookPixel` (Client Component)
  - [ ] `TikTokPixel` (Client Component)
  - [ ] `GoogleAds` (Client Component)
  - [ ] `KwaiPixel` (Client Component)
- [ ] Adaptar PostHog:
  - [ ] Configurar para SSR
  - [ ] Manter Session Replay
  - [ ] Adaptar eventos
- [ ] Migrar sistema de 3DS:
  - [ ] Cielo 3DS
  - [ ] Pagar.me 3DS
  - [ ] Adaptar para Server Actions quando possível
- [ ] Adaptar Nethone (antifraude):
  - [ ] Manter fingerprinting no cliente
  - [ ] Adaptar para SSR
- [ ] Adaptar ClientJS (fingerprinting):
  - [ ] Manter no cliente
  - [ ] Adaptar para Next.js
- [ ] Migrar sistema de CEP:
  - [ ] `useCepSearch` hook
  - [ ] `cepService.ts`
  - [ ] Adaptar para Server Action quando possível

### 3.3 Sistema de Builder
- [ ] Migrar `CheckoutConfigRenderer`
- [ ] Migrar componentes do builder:
  - [ ] `CheckoutComponentAdvantage`
  - [ ] `CheckoutComponentCountdown`
  - [ ] `CheckoutComponentHeader`
  - [ ] `CheckoutComponentImage`
  - [ ] `CheckoutComponentText`
  - [ ] `CheckoutComponentVideo`
  - [ ] `CheckoutComponentTestimonial`
  - [ ] `CheckoutComponentSeal`
  - [ ] `CheckoutComponentList`
  - [ ] `CheckoutComponentMap`
  - [ ] `CheckoutComponentChat`
  - [ ] `CheckoutComponentFacebook`
  - [ ] `CheckoutComponentNotification`
  - [ ] `CheckoutComponentExitPopup`
- [ ] Adaptar para Server Components quando possível
- [ ] Manter funcionalidade completa

## ✅ Fase 4: Otimizações e Compatibilidade

### 4.1 Compatibilidade com Navegadores de Redes Sociais
- [ ] Implementar polyfills para APIs não suportadas
- [ ] Otimizar JavaScript para navegadores antigos
- [ ] Testar em navegadores embutidos:
  - [ ] Instagram (iOS e Android)
  - [ ] TikTok (iOS e Android)
  - [ ] Facebook (iOS e Android)
  - [ ] WhatsApp (iOS e Android)
- [ ] Implementar fallbacks para recursos não suportados
- [ ] Otimizar carregamento de recursos externos
- [ ] Implementar Progressive Enhancement
- [ ] Testar com JavaScript desabilitado (degradação graciosa)

### 4.2 Performance e SEO
- [ ] Implementar Server-Side Rendering para conteúdo crítico
- [ ] Otimizar imagens:
  - [ ] Usar Next.js Image component
  - [ ] Configurar lazy loading
  - [ ] Otimizar tamanhos
- [ ] Implementar lazy loading de componentes pesados
- [ ] Otimizar bundle size:
  - [ ] Code splitting
  - [ ] Tree shaking
  - [ ] Remover dependências não utilizadas
- [ ] Implementar ISR (Incremental Static Regeneration) quando possível
- [ ] Adicionar meta tags dinâmicas:
  - [ ] Title
  - [ ] Description
  - [ ] Open Graph
  - [ ] Twitter Cards
- [ ] Implementar sitemap.xml
- [ ] Implementar robots.txt

### 4.3 Tratamento de Erros
- [ ] Migrar sistema de tratamento de erros
- [ ] Implementar error boundaries:
  - [ ] Global error boundary
  - [ ] Error boundary por rota
- [ ] Adaptar error logging para SSR
- [ ] Criar páginas de erro customizadas:
  - [ ] `app/404.tsx`
  - [ ] `app/500.tsx`
  - [ ] `app/error.tsx`
- [ ] Implementar retry logic para APIs
- [ ] Criar sistema de notificações de erro (toast)

## ✅ Fase 5: Testes e Validação

### 5.1 Testes Funcionais
- [ ] Testar fluxo completo de checkout:
  - [ ] Preenchimento de formulário
  - [ ] Validações
  - [ ] Submissão
  - [ ] Processamento de pagamento
- [ ] Testar todos os métodos de pagamento:
  - [ ] PIX
  - [ ] PIX Automático
  - [ ] Cartão de Crédito
  - [ ] Boleto
  - [ ] Apple Pay
  - [ ] Google Pay
  - [ ] PicPay
  - [ ] Nubank
- [ ] Testar sistema de cupons:
  - [ ] Validação
  - [ ] Aplicação
  - [ ] Erros
- [ ] Testar sistema de bumps:
  - [ ] Seleção
  - [ ] Remoção
  - [ ] Cálculo de preço
- [ ] Testar validações de formulário:
  - [ ] CPF/CNPJ
  - [ ] Email
  - [ ] Telefone
  - [ ] Cartão de crédito
  - [ ] Endereço
- [ ] Testar sistema de 3DS:
  - [ ] Cielo
  - [ ] Pagar.me
- [ ] Testar tracking pixels:
  - [ ] Facebook
  - [ ] TikTok
  - [ ] Google Ads
  - [ ] Kwai

### 5.2 Testes de Compatibilidade
- [ ] Testar em navegadores principais:
  - [ ] Chrome (desktop e mobile)
  - [ ] Firefox (desktop e mobile)
  - [ ] Safari (desktop e mobile)
  - [ ] Edge (desktop e mobile)
- [ ] Testar em navegadores móveis:
  - [ ] iOS Safari
  - [ ] Chrome Mobile
  - [ ] Samsung Internet
- [ ] Testar em navegadores embutidos:
  - [ ] Instagram (iOS e Android)
  - [ ] TikTok (iOS e Android)
  - [ ] Facebook (iOS e Android)
  - [ ] WhatsApp (iOS e Android)
- [ ] Testar em diferentes resoluções:
  - [ ] Desktop (1920x1080, 1366x768)
  - [ ] Tablet (768x1024, 1024x768)
  - [ ] Mobile (375x667, 414x896)
- [ ] Testar com conexões lentas:
  - [ ] 3G
  - [ ] 4G
  - [ ] WiFi

### 5.3 Testes de Internacionalização
- [ ] Testar detecção de idioma por IP:
  - [ ] Brasil → Português
  - [ ] Argentina → Espanhol
  - [ ] México → Espanhol
  - [ ] Estados Unidos → Inglês
  - [ ] Outros países → Fallback
- [ ] Testar troca manual de idioma:
  - [ ] Português → Espanhol
  - [ ] Espanhol → Inglês
  - [ ] Inglês → Português
- [ ] Testar todas as traduções:
  - [ ] Português (pt)
  - [ ] Espanhol (es)
  - [ ] Inglês (en)
- [ ] Validar formatação de moeda por país:
  - [ ] Brasil (R$)
  - [ ] Argentina (ARS$)
  - [ ] México (MXN$)
  - [ ] Estados Unidos (US$)
- [ ] Validar formatação de data por país:
  - [ ] Brasil (DD/MM/YYYY)
  - [ ] Argentina (DD/MM/YYYY)
  - [ ] Estados Unidos (MM/DD/YYYY)
- [ ] Testar países da América Latina:
  - [ ] Brasil
  - [ ] Argentina
  - [ ] Chile
  - [ ] Colômbia
  - [ ] México
  - [ ] Peru
  - [ ] Equador
  - [ ] Bolívia
  - [ ] Paraguai
  - [ ] Uruguai
  - [ ] Venezuela

## ✅ Fase 6: Deploy e Monitoramento

### 6.1 Configuração de Deploy
- [ ] Configurar Vercel ou plataforma de deploy
- [ ] Configurar variáveis de ambiente:
  - [ ] Produção
  - [ ] Staging
  - [ ] Desenvolvimento
- [ ] Configurar domínios e SSL
- [ ] Configurar CDN
- [ ] Configurar cache strategies:
  - [ ] ISR
  - [ ] Revalidation
  - [ ] Cache headers
- [ ] Configurar redirects e rewrites

### 6.2 Monitoramento
- [ ] Configurar analytics:
  - [ ] PostHog
  - [ ] Google Analytics (se necessário)
- [ ] Configurar error tracking:
  - [ ] Sentry ou similar
  - [ ] Logging estruturado
- [ ] Configurar performance monitoring:
  - [ ] Web Vitals
  - [ ] Core Web Vitals
- [ ] Configurar uptime monitoring
- [ ] Implementar logging estruturado:
  - [ ] Server-side logs
  - [ ] Client-side logs
  - [ ] Error logs
  - [ ] Performance logs

### 6.3 Documentação
- [ ] Documentar arquitetura:
  - [ ] Estrutura de pastas
  - [ ] Fluxo de dados
  - [ ] Decisões técnicas
- [ ] Documentar APIs:
  - [ ] Endpoints utilizados
  - [ ] Payloads
  - [ ] Respostas
- [ ] Documentar processo de deploy:
  - [ ] Configuração
  - [ ] Variáveis de ambiente
  - [ ] Troubleshooting
- [ ] Criar guia de desenvolvimento:
  - [ ] Setup local
  - [ ] Comandos disponíveis
  - [ ] Convenções de código
- [ ] Criar guia de troubleshooting:
  - [ ] Problemas comuns
  - [ ] Soluções
  - [ ] Contatos

## 📊 Métricas de Sucesso

### Performance
- [ ] First Contentful Paint (FCP) < 1.5s
- [ ] Largest Contentful Paint (LCP) < 2.5s
- [ ] Time to Interactive (TTI) < 3.5s
- [ ] Cumulative Layout Shift (CLS) < 0.1
- [ ] First Input Delay (FID) < 100ms

### Compatibilidade
- [ ] Funciona em 100% dos navegadores principais
- [ ] Funciona em navegadores embutidos (Instagram, TikTok, Facebook)
- [ ] Funciona com JavaScript desabilitado (degradação graciosa)

### Internacionalização
- [ ] Detecção de idioma funciona em 100% dos casos
- [ ] Todas as traduções estão completas
- [ ] Formatação de moeda e data correta por país

### Funcionalidade
- [ ] Todos os métodos de pagamento funcionando
- [ ] Sistema de cupons funcionando
- [ ] Sistema de bumps funcionando
- [ ] Tracking pixels funcionando
- [ ] Analytics funcionando

---

**Última atualização**: 2024
**Versão**: 1.0

